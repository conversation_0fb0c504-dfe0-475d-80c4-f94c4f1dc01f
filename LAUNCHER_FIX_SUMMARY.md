# 启动器全屏模式切换到窗口模式自动关闭问题修复

## 问题描述
首次打开启动器全屏模式，切换到窗口模式时启动器窗口自动关闭。

## 问题根本原因

通过详细的日志分析发现：

1. **时间序列分析**：
   - `11:39:05.268` - 窗口模式弹窗打开 (`popupVisible = true`)
   - `11:39:05.496` - 弹窗立即变为不可见 (`popupVisible = false`) - **仅228ms后**

2. **根本原因**：
   - PanelPopup在打开后立即因为某种原因（可能是失去焦点）而关闭
   - 这触发了`onPopupVisibleChanged`事件
   - 导致`LauncherController.visible`被设置为false，启动器自动关闭

## 修复方案

实现了一个**模式切换保护机制**，防止在模式切换过程中因弹窗的瞬时状态变化而误关闭启动器。

### 核心修改

#### 1. 添加保护标志和定时器
```qml
property bool isFrameSwitching: false

Timer {
    id: frameSwitchProtectionTimer
    interval: 500  // 500ms保护期
    repeat: false
    onTriggered: {
        windowedModeLauncher.isFrameSwitching = false
        console.log("[LauncherItem] Frame switch protection period ended")
    }
}
```

#### 2. 监听模式切换事件
```qml
Connections {
    target: LauncherController
    function onCurrentFrameChanged() {
        console.log("[LauncherItem] Frame changed, starting protection timer")
        windowedModeLauncher.isFrameSwitching = true
        windowedModeLauncher.frameSwitchProtectionTimer.start()
    }
}
```

#### 3. 在保护期内忽略弹窗关闭事件
```qml
onPopupVisibleChanged: function() {
    // 在模式切换保护期内，忽略弹窗关闭事件
    if (isFrameSwitching && !popupVisible) {
        console.log("[LauncherItem] Frame switching in progress, ignoring popup close event")
        return
    }
    
    if (popupVisible !== visibility) {
        LauncherController.visible = popupVisible
    }
}
```

## 修复原理

1. **检测模式切换**：当`LauncherController.currentFrame`改变时，启动500ms保护期
2. **保护期内忽略关闭**：在保护期内，忽略所有弹窗关闭事件，防止误关闭启动器
3. **保护期后恢复正常**：500ms后，恢复正常的弹窗状态处理逻辑

## 修改的文件

- `shell-launcher-applet/package/launcheritem.qml`
  - 添加了`isFrameSwitching`保护标志
  - 添加了`frameSwitchProtectionTimer`定时器
  - 添加了`LauncherController.onCurrentFrameChanged`连接
  - 修改了`onPopupVisibleChanged`逻辑

## 测试方法

1. **编译和安装**：
   ```bash
   ./test_launcher_fix.sh
   ```

2. **测试步骤**：
   - 点击启动器图标打开全屏模式
   - 点击右上角"窗口模式"按钮
   - 确认启动器保持打开状态（不会自动关闭）

3. **查看日志**：
   ```bash
   journalctl -f | grep -E '\[LauncherController\]|\[LauncherItem\]|\[FullscreenFrame\]|\[WindowedFrame\]'
   ```

4. **预期日志输出**：
   ```
   [LauncherItem] Frame changed, starting protection timer
   [LauncherItem] Frame switching in progress, ignoring popup close event
   [LauncherItem] Frame switch protection period ended
   ```

## 优势

1. **非侵入性**：不影响正常的启动器功能
2. **时间窗口保护**：只在模式切换的关键时间窗口内生效
3. **自动恢复**：保护期结束后自动恢复正常行为
4. **详细日志**：提供完整的调试信息

## 注意事项

- 保护期设置为500ms，可根据实际情况调整
- 只影响从全屏模式切换到窗口模式的场景
- 不影响用户主动关闭启动器的操作
