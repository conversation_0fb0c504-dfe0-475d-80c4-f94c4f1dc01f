/****************************************************************************
** Meta object code from reading C++ file 'launcheritem.cpp'
**
** Created by: The Qt Meta Object Compiler version 68 (Qt 6.8.0)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include <QtCore/qmetatype.h>
#include <QtCore/qplugin.h>

#include <QtCore/qtmochelpers.h>

#include <memory>


#include <QtCore/qxptype_traits.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'launcheritem.cpp' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 68
#error "This file was generated using the moc from 6.8.0. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
QT_WARNING_DISABLE_GCC("-Wuseless-cast")
namespace {

#ifdef QT_MOC_HAS_STRINGDATA
struct qt_meta_stringdata_CLASSdockSCOPELauncherItemAppletFactoryENDCLASS_t {};
constexpr auto qt_meta_stringdata_CLASSdockSCOPELauncherItemAppletFactoryENDCLASS = QtMocHelpers::stringData(
    "dock::LauncherItemAppletFactory"
);
#else  // !QT_MOC_HAS_STRINGDATA
#error "qtmochelpers.h not found or too old."
#endif // !QT_MOC_HAS_STRINGDATA
} // unnamed namespace

Q_CONSTINIT static const uint qt_meta_data_CLASSdockSCOPELauncherItemAppletFactoryENDCLASS[] = {

 // content:
      12,       // revision
       0,       // classname
       0,    0, // classinfo
       0,    0, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       0,       // signalCount

       0        // eod
};

Q_CONSTINIT const QMetaObject dock::LauncherItemAppletFactory::staticMetaObject = { {
    QMetaObject::SuperData::link<ds::DAppletFactory::staticMetaObject>(),
    qt_meta_stringdata_CLASSdockSCOPELauncherItemAppletFactoryENDCLASS.offsetsAndSizes,
    qt_meta_data_CLASSdockSCOPELauncherItemAppletFactoryENDCLASS,
    qt_static_metacall,
    nullptr,
    qt_incomplete_metaTypeArray<qt_meta_stringdata_CLASSdockSCOPELauncherItemAppletFactoryENDCLASS_t,
        // Q_OBJECT / Q_GADGET
        QtPrivate::TypeAndForceComplete<LauncherItemAppletFactory, std::true_type>
    >,
    nullptr
} };

void dock::LauncherItemAppletFactory::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    (void)_o;
    (void)_id;
    (void)_c;
    (void)_a;
}

const QMetaObject *dock::LauncherItemAppletFactory::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *dock::LauncherItemAppletFactory::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_CLASSdockSCOPELauncherItemAppletFactoryENDCLASS.stringdata0))
        return static_cast<void*>(this);
    if (!strcmp(_clname, "org.deepin.ds.applet-factory"))
        return static_cast< ds::DAppletFactory*>(this);
    return ds::DAppletFactory::qt_metacast(_clname);
}

int dock::LauncherItemAppletFactory::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = ds::DAppletFactory::qt_metacall(_c, _id, _a);
    return _id;
}
using namespace dock;

#ifdef QT_MOC_EXPORT_PLUGIN_V2
static constexpr unsigned char qt_pluginMetaDataV2_LauncherItemAppletFactory[] = {
    0xbf, 
    // "IID"
    0x02,  0x78,  0x1c,  'o',  'r',  'g',  '.',  'd', 
    'e',  'e',  'p',  'i',  'n',  '.',  'd',  's', 
    '.',  'a',  'p',  'p',  'l',  'e',  't',  '-', 
    'f',  'a',  'c',  't',  'o',  'r',  'y', 
    // "className"
    0x03,  0x78,  0x19,  'L',  'a',  'u',  'n',  'c', 
    'h',  'e',  'r',  'I',  't',  'e',  'm',  'A', 
    'p',  'p',  'l',  'e',  't',  'F',  'a',  'c', 
    't',  'o',  'r',  'y', 
    0xff, 
};
QT_MOC_EXPORT_PLUGIN_V2(dock::LauncherItemAppletFactory, LauncherItemAppletFactory, qt_pluginMetaDataV2_LauncherItemAppletFactory)
#else
QT_PLUGIN_METADATA_SECTION
Q_CONSTINIT static constexpr unsigned char qt_pluginMetaData_LauncherItemAppletFactory[] = {
    'Q', 'T', 'M', 'E', 'T', 'A', 'D', 'A', 'T', 'A', ' ', '!',
    // metadata version, Qt version, architectural requirements
    0, QT_VERSION_MAJOR, QT_VERSION_MINOR, qPluginArchRequirements(),
    0xbf, 
    // "IID"
    0x02,  0x78,  0x1c,  'o',  'r',  'g',  '.',  'd', 
    'e',  'e',  'p',  'i',  'n',  '.',  'd',  's', 
    '.',  'a',  'p',  'p',  'l',  'e',  't',  '-', 
    'f',  'a',  'c',  't',  'o',  'r',  'y', 
    // "className"
    0x03,  0x78,  0x19,  'L',  'a',  'u',  'n',  'c', 
    'h',  'e',  'r',  'I',  't',  'e',  'm',  'A', 
    'p',  'p',  'l',  'e',  't',  'F',  'a',  'c', 
    't',  'o',  'r',  'y', 
    0xff, 
};
QT_MOC_EXPORT_PLUGIN(dock::LauncherItemAppletFactory, LauncherItemAppletFactory)
#endif  // QT_MOC_EXPORT_PLUGIN_V2

QT_WARNING_POP
