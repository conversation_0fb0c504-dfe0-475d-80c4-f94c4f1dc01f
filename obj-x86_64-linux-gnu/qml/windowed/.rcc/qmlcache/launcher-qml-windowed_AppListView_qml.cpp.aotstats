[{"moduleFiles": [{"entries": [{"codegenSuccessfull": true, "column": 33, "durationMicroseconds": 152, "errorMessage": "", "functionName": "keyTab<PERSON>arget", "line": 18}, {"codegenSuccessfull": false, "column": 5, "durationMicroseconds": 281, "errorMessage": "Cannot access value for name LauncherController", "functionName": "resetViewState", "line": 22}, {"codegenSuccessfull": false, "column": 5, "durationMicroseconds": 0, "errorMessage": "Functions without type annotations won't be compiled", "functionName": "scrollToAlphabetCategory", "line": 30}, {"codegenSuccessfull": false, "column": 5, "durationMicroseconds": 0, "errorMessage": "Functions without type annotations won't be compiled", "functionName": "scrollToDDECategory", "line": 41}, {"codegenSuccessfull": false, "column": 5, "durationMicroseconds": 0, "errorMessage": "Functions without type annotations won't be compiled", "functionName": "scrollToIndex", "line": 52}, {"codegenSuccessfull": true, "column": 20, "durationMicroseconds": 137, "errorMessage": "", "functionName": "width", "line": 67}, {"codegenSuccessfull": true, "column": 24, "durationMicroseconds": 113, "errorMessage": "", "functionName": "width", "line": 70}, {"codegenSuccessfull": false, "column": 26, "durationMicroseconds": 333, "errorMessage": "Cannot access value for name CategorizedSortProxyModel", "functionName": "visible", "line": 72}, {"codegenSuccessfull": true, "column": 24, "durationMicroseconds": 112, "errorMessage": "", "functionName": "width", "line": 87}, {"codegenSuccessfull": true, "column": 25, "durationMicroseconds": 102, "errorMessage": "", "functionName": "height", "line": 88}, {"codegenSuccessfull": true, "column": 34, "durationMicroseconds": 118, "errorMessage": "", "functionName": "focusPolicy", "line": 95}, {"codegenSuccessfull": false, "column": 27, "durationMicroseconds": 605, "errorMessage": "Cannot access value for name CategorizedSortProxyModel", "functionName": "text", "line": 100}, {"codegenSuccessfull": true, "column": 35, "durationMicroseconds": 76, "errorMessage": "", "functionName": "fill", "line": 96}, {"codegenSuccessfull": true, "column": 43, "durationMicroseconds": 71, "errorMessage": "", "functionName": "fill", "line": 110}, {"codegenSuccessfull": false, "column": 39, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type  for binding on text.", "functionName": "text", "line": 114}, {"codegenSuccessfull": false, "column": 39, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type  for binding on font.", "functionName": "font", "line": 115}, {"codegenSuccessfull": false, "column": 57, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type  for binding on verticalCenter.", "functionName": "verticalCenter", "line": 113}, {"codegenSuccessfull": true, "column": 33, "durationMicroseconds": 113, "errorMessage": "", "functionName": "button", "line": 122}, {"codegenSuccessfull": true, "column": 42, "durationMicroseconds": 154, "errorMessage": "", "functionName": "acceptedButtons", "line": 127}, {"codegenSuccessfull": false, "column": 36, "durationMicroseconds": 415, "errorMessage": "Cannot access value for name CategorizedSortProxyModel", "functionName": "onClicked", "line": 129}, {"codegenSuccessfull": true, "column": 39, "durationMicroseconds": 88, "errorMessage": "", "functionName": "fill", "line": 126}, {"codegenSuccessfull": false, "column": 16, "durationMicroseconds": 151, "errorMessage": "Cannot access value for name CategorizedSortProxyModel", "functionName": "model", "line": 155}, {"codegenSuccessfull": false, "column": 20, "durationMicroseconds": 40, "errorMessage": "Cannot access value for name listView", "functionName": "width", "line": 158}, {"codegenSuccessfull": true, "column": 21, "durationMicroseconds": 124, "errorMessage": "", "functionName": "height", "line": 159}, {"codegenSuccessfull": true, "column": 26, "durationMicroseconds": 149, "errorMessage": "", "functionName": "visible", "line": 168}, {"codegenSuccessfull": false, "column": 23, "durationMicroseconds": 109, "errorMessage": "Cannot access value for name model", "functionName": "text", "line": 169}, {"codegenSuccessfull": false, "column": 23, "durationMicroseconds": 29, "errorMessage": "Cannot load property fontManager from Dtk::Quick::DQMLGlobalObject.", "functionName": "font", "line": 200}, {"codegenSuccessfull": false, "column": 45, "durationMicroseconds": 393, "errorMessage": "Cannot access value for name DStyle", "functionName": "textColor", "line": 202}, {"codegenSuccessfull": true, "column": 27, "durationMicroseconds": 195, "errorMessage": "", "functionName": "left", "line": 163}, {"codegenSuccessfull": true, "column": 28, "durationMicroseconds": 148, "errorMessage": "", "functionName": "right", "line": 164}, {"codegenSuccessfull": false, "column": 28, "durationMicroseconds": 422, "errorMessage": "Cannot access value for name iconName", "functionName": "name", "line": 171}, {"codegenSuccessfull": true, "column": 31, "durationMicroseconds": 114, "errorMessage": "", "functionName": "mode", "line": 172}, {"codegenSuccessfull": false, "column": 30, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type  for binding on visible.", "functionName": "visible", "line": 177}, {"codegenSuccessfull": true, "column": 33, "durationMicroseconds": 339, "errorMessage": "", "functionName": "sourceSize", "line": 178}, {"codegenSuccessfull": false, "column": 30, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type Dtk::Gui::DDciIconPalette for binding on palette.", "functionName": "palette", "line": 179}, {"codegenSuccessfull": false, "column": 28, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type Dtk::Gui::DGuiApplicationHelper::ColorType for binding on theme.", "functionName": "theme", "line": 180}, {"codegenSuccessfull": false, "column": 35, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type  for binding on left.", "functionName": "left", "line": 174}, {"codegenSuccessfull": false, "column": 37, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type  for binding on bottom.", "functionName": "bottom", "line": 175}, {"codegenSuccessfull": false, "column": 28, "durationMicroseconds": 33, "errorMessage": "Cannot load property themeType from Dtk::Quick::DQMLGlobalObject.", "functionName": "color", "line": 187}, {"codegenSuccessfull": false, "column": 30, "durationMicroseconds": 59, "errorMessage": "Cannot access value for name model", "functionName": "visible", "line": 191}, {"codegenSuccessfull": false, "column": 35, "durationMicroseconds": 42, "errorMessage": "Cannot load property themeType from Dtk::Quick::DQMLGlobalObject.", "functionName": "color", "line": 189}, {"codegenSuccessfull": true, "column": 32, "durationMicroseconds": 176, "errorMessage": "", "functionName": "right", "line": 193}, {"codegenSuccessfull": true, "column": 41, "durationMicroseconds": 208, "errorMessage": "", "functionName": "verticalCenter", "line": 194}, {"codegenSuccessfull": false, "column": 37, "durationMicroseconds": 55, "errorMessage": "Cannot load property textColor from Dtk::Quick::DQuickControlColorSelector.", "functionName": "windowText", "line": 203}, {"codegenSuccessfull": true, "column": 31, "durationMicroseconds": 83, "errorMessage": "", "functionName": "text", "line": 204}, {"codegenSuccessfull": true, "column": 34, "durationMicroseconds": 428, "errorMessage": "", "functionName": "visible", "line": 206}, {"codegenSuccessfull": true, "column": 32, "durationMicroseconds": 117, "errorMessage": "", "functionName": "dragType", "line": 208}, {"codegenSuccessfull": false, "column": 32, "durationMicroseconds": 399, "errorMessage": "Cannot access value for name Helper", "functionName": "mimeData", "line": 209}, {"codegenSuccessfull": true, "column": 33, "durationMicroseconds": 160, "errorMessage": "", "functionName": "y", "line": 210}, {"codegenSuccessfull": true, "column": 33, "durationMicroseconds": 168, "errorMessage": "", "functionName": "x", "line": 211}, {"codegenSuccessfull": true, "column": 27, "durationMicroseconds": 123, "errorMessage": "", "functionName": "when", "line": 215}, {"codegenSuccessfull": true, "column": 33, "durationMicroseconds": 123, "errorMessage": "", "functionName": "target", "line": 219}, {"codegenSuccessfull": false, "column": 28, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type  for binding on x. You may want use ID-based grouped properties here.", "functionName": "x", "line": 220}, {"codegenSuccessfull": false, "column": 28, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type  for binding on y. You may want use ID-based grouped properties here.", "functionName": "y", "line": 221}, {"codegenSuccessfull": true, "column": 38, "durationMicroseconds": 153, "errorMessage": "", "functionName": "acceptedButtons", "line": 226}, {"codegenSuccessfull": false, "column": 31, "durationMicroseconds": 83, "errorMessage": "Cannot access value for name model", "functionName": "onTapped", "line": 227}, {"codegenSuccessfull": true, "column": 29, "durationMicroseconds": 89, "errorMessage": "", "functionName": "target", "line": 235}, {"codegenSuccessfull": true, "column": 38, "durationMicroseconds": 140, "errorMessage": "", "functionName": "acceptedButtons", "line": 236}, {"codegenSuccessfull": false, "column": 30, "durationMicroseconds": 49, "errorMessage": "Cannot access value for name ddeCategoryMenu", "functionName": "enabled", "line": 239}, {"codegenSuccessfull": false, "column": 38, "durationMicroseconds": 161, "errorMessage": "Cannot access value for name dndItem", "functionName": "onActiveChanged", "line": 240}, {"codegenSuccessfull": true, "column": 29, "durationMicroseconds": 142, "errorMessage": "", "functionName": "active", "line": 259}, {"codegenSuccessfull": true, "column": 38, "durationMicroseconds": 118, "errorMessage": "", "functionName": "focusPolicy", "line": 261}, {"codegenSuccessfull": false, "column": 40, "durationMicroseconds": 212, "errorMessage": "Cannot access value for name DStyle", "functionName": "implicitWidth", "line": 262}, {"codegenSuccessfull": false, "column": 41, "durationMicroseconds": 175, "errorMessage": "Cannot access value for name Helper", "functionName": "implicitHeight", "line": 263}, {"codegenSuccessfull": false, "column": 33, "durationMicroseconds": 42, "errorMessage": "Cannot access value for name itemDelegate", "functionName": "button", "line": 264}, {"codegenSuccessfull": false, "column": 31, "durationMicroseconds": 125, "errorMessage": "Cannot access value for name desktopId", "functionName": "onTapped", "line": 269}, {"codegenSuccessfull": false, "column": 39, "durationMicroseconds": 430, "errorMessage": "Cannot access value for name desktopId", "functionName": "onReturnPressed", "line": 274}, {"codegenSuccessfull": false, "column": 38, "durationMicroseconds": 421, "errorMessage": "Cannot access value for name desktopId", "functionName": "onSpacePressed", "line": 278}, {"codegenSuccessfull": true, "column": 22, "durationMicroseconds": 208, "errorMessage": "", "functionName": "closePolicy", "line": 289}, {"codegenSuccessfull": false, "column": 40, "durationMicroseconds": 87, "errorMessage": "Cannot generate efficient code for storing an array in a non-sequence type", "functionName": "existingSections", "line": 291}, {"codegenSuccessfull": true, "column": 27, "durationMicroseconds": 302, "errorMessage": "", "functionName": "onVisibleChanged", "line": 338}, {"codegenSuccessfull": true, "column": 20, "durationMicroseconds": 122, "errorMessage": "", "functionName": "model", "line": 293}, {"codegenSuccessfull": false, "column": 23, "durationMicroseconds": 378, "errorMessage": "Cannot access value for name modelData", "functionName": "text", "line": 296}, {"codegenSuccessfull": false, "column": 28, "durationMicroseconds": 269, "errorMessage": "Cannot access value for name DStyle", "functionName": "textColor", "line": 297}, {"codegenSuccessfull": false, "column": 30, "durationMicroseconds": 343, "errorMessage": "Cannot access value for name modelData", "functionName": "onTriggered", "line": 298}, {"codegenSuccessfull": true, "column": 32, "durationMicroseconds": 124, "errorMessage": "", "functionName": "alignment", "line": 302}, {"codegenSuccessfull": true, "column": 27, "durationMicroseconds": 106, "errorMessage": "", "functionName": "text", "line": 303}, {"codegenSuccessfull": false, "column": 28, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type QColor for binding on color.", "functionName": "color", "line": 304}, {"codegenSuccessfull": true, "column": 30, "durationMicroseconds": 224, "errorMessage": "", "functionName": "visible", "line": 313}, {"codegenSuccessfull": true, "column": 29, "durationMicroseconds": 69, "errorMessage": "", "functionName": "color1", "line": 332}, {"codegenSuccessfull": true, "column": 29, "durationMicroseconds": 61, "errorMessage": "", "functionName": "color2", "line": 333}, {"codegenSuccessfull": true, "column": 35, "durationMicroseconds": 157, "errorMessage": "", "functionName": "left", "line": 307}, {"codegenSuccessfull": true, "column": 36, "durationMicroseconds": 140, "errorMessage": "", "functionName": "right", "line": 309}, {"codegenSuccessfull": true, "column": 34, "durationMicroseconds": 145, "errorMessage": "", "functionName": "top", "line": 311}, {"codegenSuccessfull": true, "column": 37, "durationMicroseconds": 150, "errorMessage": "", "functionName": "bottom", "line": 312}, {"codegenSuccessfull": false, "column": 37, "durationMicroseconds": 367, "errorMessage": "Cannot generate efficient code for internal conversion with incompatible or ambiguous types: QVariant -> Dtk::Quick::DColor stored as QVariant", "functionName": "common", "line": 320}, {"codegenSuccessfull": false, "column": 38, "durationMicroseconds": 344, "errorMessage": "Cannot generate efficient code for internal conversion with incompatible or ambiguous types: QVariant -> Dtk::Quick::DColor stored as QVariant", "functionName": "crystal", "line": 321}, {"codegenSuccessfull": false, "column": 37, "durationMicroseconds": 341, "errorMessage": "Cannot generate efficient code for internal conversion with incompatible or ambiguous types: QVariant -> Dtk::Quick::DColor stored as QVariant", "functionName": "common", "line": 324}, {"codegenSuccessfull": false, "column": 38, "durationMicroseconds": 331, "errorMessage": "Cannot generate efficient code for internal conversion with incompatible or ambiguous types: QVariant -> Dtk::Quick::DColor stored as QVariant", "functionName": "crystal", "line": 325}, {"codegenSuccessfull": false, "column": 37, "durationMicroseconds": 558, "errorMessage": "Cannot generate efficient code for internal conversion with incompatible or ambiguous types: QVariant -> Dtk::Quick::DColor stored as QVariant", "functionName": "common", "line": 328}, {"codegenSuccessfull": false, "column": 38, "durationMicroseconds": 524, "errorMessage": "Cannot generate efficient code for internal conversion with incompatible or ambiguous types: QVariant -> Dtk::Quick::DColor stored as QVariant", "functionName": "crystal", "line": 329}, {"codegenSuccessfull": false, "column": 21, "durationMicroseconds": 256, "errorMessage": "Cannot access value for name DStyle", "functionName": "radius", "line": 344}, {"codegenSuccessfull": true, "column": 30, "durationMicroseconds": 125, "errorMessage": "", "functionName": "backgroundColor", "line": 345}, {"codegenSuccessfull": true, "column": 36, "durationMicroseconds": 105, "errorMessage": "", "functionName": "backgroundNoBlurColor", "line": 346}, {"codegenSuccessfull": false, "column": 31, "durationMicroseconds": 742, "errorMessage": "method scrollToIndex cannot be resolved.", "functionName": "onActiveFocusChanged", "line": 360}, {"codegenSuccessfull": true, "column": 16, "durationMicroseconds": 109, "errorMessage": "", "functionName": "model", "line": 397}, {"codegenSuccessfull": true, "column": 23, "durationMicroseconds": 86, "errorMessage": "", "functionName": "fill", "line": 354}, {"codegenSuccessfull": false, "column": 21, "durationMicroseconds": 89, "errorMessage": "Cannot access value for name CategorizedSortProxyModel", "functionName": "target", "line": 368}, {"codegenSuccessfull": true, "column": 13, "durationMicroseconds": 129, "errorMessage": "", "functionName": "onCategoryTypeChanged", "line": 369}, {"codegenSuccessfull": false, "column": 27, "durationMicroseconds": 992, "errorMessage": "Cannot access value for name CategorizedSortProxyModel", "functionName": "property", "line": 374}, {"codegenSuccessfull": true, "column": 27, "durationMicroseconds": 477, "errorMessage": "", "functionName": "criteria", "line": 375}, {"codegenSuccessfull": true, "column": 27, "durationMicroseconds": 86, "errorMessage": "", "functionName": "delegate", "line": 376}, {"codegenSuccessfull": true, "column": 35, "durationMicroseconds": 101, "errorMessage": "", "functionName": "labelPositioning", "line": 377}, {"codegenSuccessfull": false, "column": 24, "durationMicroseconds": 80, "errorMessage": "Cannot use shadowable base type for further lookups: QQuickItem::palette with type QQuickPalette (stored as QVariant)", "functionName": "color", "line": 387}, {"codegenSuccessfull": false, "column": 26, "durationMicroseconds": 48, "errorMessage": "Cannot access value for name listView", "functionName": "visible", "line": 388}, {"codegenSuccessfull": true, "column": 27, "durationMicroseconds": 75, "errorMessage": "", "functionName": "fill", "line": 383}, {"codegenSuccessfull": false, "column": 25, "durationMicroseconds": 971, "errorMessage": "Cannot access value for name CategorizedSortProxyModel", "functionName": "onPressed", "line": 401}, {"codegenSuccessfull": false, "column": 32, "durationMicroseconds": 43, "errorMessage": "Cannot access value for name character", "functionName": "onCategoryClicked", "line": 412}, {"codegenSuccessfull": true, "column": 31, "durationMicroseconds": 316, "errorMessage": "", "functionName": "onVisibleChanged", "line": 417}], "filepath": "/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/AppListView.qml"}], "moduleId": "org.deepin.launchpad.windowed(launcher-qml-windowed)"}]