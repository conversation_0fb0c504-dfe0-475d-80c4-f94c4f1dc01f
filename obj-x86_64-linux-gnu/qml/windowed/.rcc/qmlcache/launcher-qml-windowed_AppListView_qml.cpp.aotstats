[{"moduleFiles": [{"entries": [{"codegenSuccessfull": true, "column": 33, "durationMicroseconds": 123, "errorMessage": "", "functionName": "keyTab<PERSON>arget", "line": 18}, {"codegenSuccessfull": false, "column": 5, "durationMicroseconds": 182, "errorMessage": "Cannot access value for name LauncherController", "functionName": "resetViewState", "line": 22}, {"codegenSuccessfull": false, "column": 5, "durationMicroseconds": 0, "errorMessage": "Functions without type annotations won't be compiled", "functionName": "scrollToAlphabetCategory", "line": 30}, {"codegenSuccessfull": false, "column": 5, "durationMicroseconds": 0, "errorMessage": "Functions without type annotations won't be compiled", "functionName": "scrollToDDECategory", "line": 41}, {"codegenSuccessfull": false, "column": 5, "durationMicroseconds": 0, "errorMessage": "Functions without type annotations won't be compiled", "functionName": "scrollToIndex", "line": 52}, {"codegenSuccessfull": true, "column": 20, "durationMicroseconds": 102, "errorMessage": "", "functionName": "width", "line": 67}, {"codegenSuccessfull": true, "column": 24, "durationMicroseconds": 79, "errorMessage": "", "functionName": "width", "line": 70}, {"codegenSuccessfull": false, "column": 26, "durationMicroseconds": 201, "errorMessage": "Cannot access value for name CategorizedSortProxyModel", "functionName": "visible", "line": 72}, {"codegenSuccessfull": true, "column": 24, "durationMicroseconds": 93, "errorMessage": "", "functionName": "width", "line": 87}, {"codegenSuccessfull": true, "column": 25, "durationMicroseconds": 83, "errorMessage": "", "functionName": "height", "line": 88}, {"codegenSuccessfull": true, "column": 34, "durationMicroseconds": 122, "errorMessage": "", "functionName": "focusPolicy", "line": 95}, {"codegenSuccessfull": false, "column": 27, "durationMicroseconds": 408, "errorMessage": "Cannot access value for name CategorizedSortProxyModel", "functionName": "text", "line": 100}, {"codegenSuccessfull": true, "column": 35, "durationMicroseconds": 68, "errorMessage": "", "functionName": "fill", "line": 96}, {"codegenSuccessfull": true, "column": 43, "durationMicroseconds": 70, "errorMessage": "", "functionName": "fill", "line": 110}, {"codegenSuccessfull": false, "column": 39, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type  for binding on text.", "functionName": "text", "line": 114}, {"codegenSuccessfull": false, "column": 39, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type  for binding on font.", "functionName": "font", "line": 115}, {"codegenSuccessfull": false, "column": 57, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type  for binding on verticalCenter.", "functionName": "verticalCenter", "line": 113}, {"codegenSuccessfull": true, "column": 33, "durationMicroseconds": 95, "errorMessage": "", "functionName": "button", "line": 122}, {"codegenSuccessfull": true, "column": 42, "durationMicroseconds": 111, "errorMessage": "", "functionName": "acceptedButtons", "line": 127}, {"codegenSuccessfull": false, "column": 36, "durationMicroseconds": 291, "errorMessage": "Cannot access value for name CategorizedSortProxyModel", "functionName": "onClicked", "line": 129}, {"codegenSuccessfull": true, "column": 39, "durationMicroseconds": 56, "errorMessage": "", "functionName": "fill", "line": 126}, {"codegenSuccessfull": false, "column": 16, "durationMicroseconds": 95, "errorMessage": "Cannot access value for name CategorizedSortProxyModel", "functionName": "model", "line": 155}, {"codegenSuccessfull": false, "column": 20, "durationMicroseconds": 28, "errorMessage": "Cannot access value for name listView", "functionName": "width", "line": 158}, {"codegenSuccessfull": true, "column": 21, "durationMicroseconds": 80, "errorMessage": "", "functionName": "height", "line": 159}, {"codegenSuccessfull": true, "column": 26, "durationMicroseconds": 100, "errorMessage": "", "functionName": "visible", "line": 168}, {"codegenSuccessfull": false, "column": 23, "durationMicroseconds": 69, "errorMessage": "Cannot access value for name model", "functionName": "text", "line": 169}, {"codegenSuccessfull": false, "column": 23, "durationMicroseconds": 18, "errorMessage": "Cannot load property fontManager from Dtk::Quick::DQMLGlobalObject.", "functionName": "font", "line": 200}, {"codegenSuccessfull": false, "column": 45, "durationMicroseconds": 264, "errorMessage": "Cannot access value for name DStyle", "functionName": "textColor", "line": 202}, {"codegenSuccessfull": true, "column": 27, "durationMicroseconds": 132, "errorMessage": "", "functionName": "left", "line": 163}, {"codegenSuccessfull": true, "column": 28, "durationMicroseconds": 129, "errorMessage": "", "functionName": "right", "line": 164}, {"codegenSuccessfull": false, "column": 28, "durationMicroseconds": 319, "errorMessage": "Cannot access value for name iconName", "functionName": "name", "line": 171}, {"codegenSuccessfull": true, "column": 31, "durationMicroseconds": 104, "errorMessage": "", "functionName": "mode", "line": 172}, {"codegenSuccessfull": false, "column": 30, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type  for binding on visible.", "functionName": "visible", "line": 177}, {"codegenSuccessfull": true, "column": 33, "durationMicroseconds": 329, "errorMessage": "", "functionName": "sourceSize", "line": 178}, {"codegenSuccessfull": false, "column": 30, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type Dtk::Gui::DDciIconPalette for binding on palette.", "functionName": "palette", "line": 179}, {"codegenSuccessfull": false, "column": 28, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type Dtk::Gui::DGuiApplicationHelper::ColorType for binding on theme.", "functionName": "theme", "line": 180}, {"codegenSuccessfull": false, "column": 35, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type  for binding on left.", "functionName": "left", "line": 174}, {"codegenSuccessfull": false, "column": 37, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type  for binding on bottom.", "functionName": "bottom", "line": 175}, {"codegenSuccessfull": false, "column": 28, "durationMicroseconds": 35, "errorMessage": "Cannot load property themeType from Dtk::Quick::DQMLGlobalObject.", "functionName": "color", "line": 187}, {"codegenSuccessfull": false, "column": 30, "durationMicroseconds": 59, "errorMessage": "Cannot access value for name model", "functionName": "visible", "line": 191}, {"codegenSuccessfull": false, "column": 35, "durationMicroseconds": 47, "errorMessage": "Cannot load property themeType from Dtk::Quick::DQMLGlobalObject.", "functionName": "color", "line": 189}, {"codegenSuccessfull": true, "column": 32, "durationMicroseconds": 161, "errorMessage": "", "functionName": "right", "line": 193}, {"codegenSuccessfull": true, "column": 41, "durationMicroseconds": 118, "errorMessage": "", "functionName": "verticalCenter", "line": 194}, {"codegenSuccessfull": false, "column": 37, "durationMicroseconds": 40, "errorMessage": "Cannot load property textColor from Dtk::Quick::DQuickControlColorSelector.", "functionName": "windowText", "line": 203}, {"codegenSuccessfull": true, "column": 31, "durationMicroseconds": 57, "errorMessage": "", "functionName": "text", "line": 204}, {"codegenSuccessfull": true, "column": 34, "durationMicroseconds": 286, "errorMessage": "", "functionName": "visible", "line": 206}, {"codegenSuccessfull": true, "column": 32, "durationMicroseconds": 80, "errorMessage": "", "functionName": "dragType", "line": 208}, {"codegenSuccessfull": false, "column": 32, "durationMicroseconds": 277, "errorMessage": "Cannot access value for name Helper", "functionName": "mimeData", "line": 209}, {"codegenSuccessfull": true, "column": 33, "durationMicroseconds": 119, "errorMessage": "", "functionName": "y", "line": 210}, {"codegenSuccessfull": true, "column": 33, "durationMicroseconds": 99, "errorMessage": "", "functionName": "x", "line": 211}, {"codegenSuccessfull": true, "column": 27, "durationMicroseconds": 81, "errorMessage": "", "functionName": "when", "line": 215}, {"codegenSuccessfull": true, "column": 33, "durationMicroseconds": 86, "errorMessage": "", "functionName": "target", "line": 219}, {"codegenSuccessfull": false, "column": 28, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type  for binding on x. You may want use ID-based grouped properties here.", "functionName": "x", "line": 220}, {"codegenSuccessfull": false, "column": 28, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type  for binding on y. You may want use ID-based grouped properties here.", "functionName": "y", "line": 221}, {"codegenSuccessfull": true, "column": 38, "durationMicroseconds": 107, "errorMessage": "", "functionName": "acceptedButtons", "line": 226}, {"codegenSuccessfull": false, "column": 31, "durationMicroseconds": 67, "errorMessage": "Cannot access value for name model", "functionName": "onTapped", "line": 227}, {"codegenSuccessfull": true, "column": 29, "durationMicroseconds": 64, "errorMessage": "", "functionName": "target", "line": 235}, {"codegenSuccessfull": true, "column": 38, "durationMicroseconds": 100, "errorMessage": "", "functionName": "acceptedButtons", "line": 236}, {"codegenSuccessfull": false, "column": 30, "durationMicroseconds": 33, "errorMessage": "Cannot access value for name ddeCategoryMenu", "functionName": "enabled", "line": 239}, {"codegenSuccessfull": false, "column": 38, "durationMicroseconds": 99, "errorMessage": "Cannot access value for name dndItem", "functionName": "onActiveChanged", "line": 240}, {"codegenSuccessfull": true, "column": 29, "durationMicroseconds": 95, "errorMessage": "", "functionName": "active", "line": 259}, {"codegenSuccessfull": true, "column": 38, "durationMicroseconds": 86, "errorMessage": "", "functionName": "focusPolicy", "line": 261}, {"codegenSuccessfull": false, "column": 40, "durationMicroseconds": 139, "errorMessage": "Cannot access value for name DStyle", "functionName": "implicitWidth", "line": 262}, {"codegenSuccessfull": false, "column": 41, "durationMicroseconds": 126, "errorMessage": "Cannot access value for name Helper", "functionName": "implicitHeight", "line": 263}, {"codegenSuccessfull": false, "column": 33, "durationMicroseconds": 27, "errorMessage": "Cannot access value for name itemDelegate", "functionName": "button", "line": 264}, {"codegenSuccessfull": false, "column": 31, "durationMicroseconds": 81, "errorMessage": "Cannot access value for name desktopId", "functionName": "onTapped", "line": 269}, {"codegenSuccessfull": false, "column": 39, "durationMicroseconds": 289, "errorMessage": "Cannot access value for name desktopId", "functionName": "onReturnPressed", "line": 274}, {"codegenSuccessfull": false, "column": 38, "durationMicroseconds": 280, "errorMessage": "Cannot access value for name desktopId", "functionName": "onSpacePressed", "line": 278}, {"codegenSuccessfull": true, "column": 22, "durationMicroseconds": 209, "errorMessage": "", "functionName": "closePolicy", "line": 289}, {"codegenSuccessfull": false, "column": 40, "durationMicroseconds": 79, "errorMessage": "Cannot generate efficient code for storing an array in a non-sequence type", "functionName": "existingSections", "line": 291}, {"codegenSuccessfull": true, "column": 27, "durationMicroseconds": 305, "errorMessage": "", "functionName": "onVisibleChanged", "line": 338}, {"codegenSuccessfull": true, "column": 20, "durationMicroseconds": 129, "errorMessage": "", "functionName": "model", "line": 293}, {"codegenSuccessfull": false, "column": 23, "durationMicroseconds": 267, "errorMessage": "Cannot access value for name modelData", "functionName": "text", "line": 296}, {"codegenSuccessfull": false, "column": 28, "durationMicroseconds": 168, "errorMessage": "Cannot access value for name DStyle", "functionName": "textColor", "line": 297}, {"codegenSuccessfull": false, "column": 30, "durationMicroseconds": 202, "errorMessage": "Cannot access value for name modelData", "functionName": "onTriggered", "line": 298}, {"codegenSuccessfull": true, "column": 32, "durationMicroseconds": 85, "errorMessage": "", "functionName": "alignment", "line": 302}, {"codegenSuccessfull": true, "column": 27, "durationMicroseconds": 78, "errorMessage": "", "functionName": "text", "line": 303}, {"codegenSuccessfull": false, "column": 28, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type QColor for binding on color.", "functionName": "color", "line": 304}, {"codegenSuccessfull": true, "column": 30, "durationMicroseconds": 168, "errorMessage": "", "functionName": "visible", "line": 313}, {"codegenSuccessfull": true, "column": 29, "durationMicroseconds": 59, "errorMessage": "", "functionName": "color1", "line": 332}, {"codegenSuccessfull": true, "column": 29, "durationMicroseconds": 46, "errorMessage": "", "functionName": "color2", "line": 333}, {"codegenSuccessfull": true, "column": 35, "durationMicroseconds": 150, "errorMessage": "", "functionName": "left", "line": 307}, {"codegenSuccessfull": true, "column": 36, "durationMicroseconds": 116, "errorMessage": "", "functionName": "right", "line": 309}, {"codegenSuccessfull": true, "column": 34, "durationMicroseconds": 107, "errorMessage": "", "functionName": "top", "line": 311}, {"codegenSuccessfull": true, "column": 37, "durationMicroseconds": 107, "errorMessage": "", "functionName": "bottom", "line": 312}, {"codegenSuccessfull": false, "column": 37, "durationMicroseconds": 259, "errorMessage": "Cannot generate efficient code for internal conversion with incompatible or ambiguous types: QVariant -> Dtk::Quick::DColor stored as QVariant", "functionName": "common", "line": 320}, {"codegenSuccessfull": false, "column": 38, "durationMicroseconds": 220, "errorMessage": "Cannot generate efficient code for internal conversion with incompatible or ambiguous types: QVariant -> Dtk::Quick::DColor stored as QVariant", "functionName": "crystal", "line": 321}, {"codegenSuccessfull": false, "column": 37, "durationMicroseconds": 217, "errorMessage": "Cannot generate efficient code for internal conversion with incompatible or ambiguous types: QVariant -> Dtk::Quick::DColor stored as QVariant", "functionName": "common", "line": 324}, {"codegenSuccessfull": false, "column": 38, "durationMicroseconds": 224, "errorMessage": "Cannot generate efficient code for internal conversion with incompatible or ambiguous types: QVariant -> Dtk::Quick::DColor stored as QVariant", "functionName": "crystal", "line": 325}, {"codegenSuccessfull": false, "column": 37, "durationMicroseconds": 385, "errorMessage": "Cannot generate efficient code for internal conversion with incompatible or ambiguous types: QVariant -> Dtk::Quick::DColor stored as QVariant", "functionName": "common", "line": 328}, {"codegenSuccessfull": false, "column": 38, "durationMicroseconds": 413, "errorMessage": "Cannot generate efficient code for internal conversion with incompatible or ambiguous types: QVariant -> Dtk::Quick::DColor stored as QVariant", "functionName": "crystal", "line": 329}, {"codegenSuccessfull": false, "column": 21, "durationMicroseconds": 170, "errorMessage": "Cannot access value for name DStyle", "functionName": "radius", "line": 344}, {"codegenSuccessfull": true, "column": 30, "durationMicroseconds": 90, "errorMessage": "", "functionName": "backgroundColor", "line": 345}, {"codegenSuccessfull": true, "column": 36, "durationMicroseconds": 72, "errorMessage": "", "functionName": "backgroundNoBlurColor", "line": 346}, {"codegenSuccessfull": false, "column": 31, "durationMicroseconds": 454, "errorMessage": "method scrollToIndex cannot be resolved.", "functionName": "onActiveFocusChanged", "line": 360}, {"codegenSuccessfull": true, "column": 16, "durationMicroseconds": 73, "errorMessage": "", "functionName": "model", "line": 397}, {"codegenSuccessfull": true, "column": 23, "durationMicroseconds": 53, "errorMessage": "", "functionName": "fill", "line": 354}, {"codegenSuccessfull": false, "column": 21, "durationMicroseconds": 53, "errorMessage": "Cannot access value for name CategorizedSortProxyModel", "functionName": "target", "line": 368}, {"codegenSuccessfull": true, "column": 13, "durationMicroseconds": 98, "errorMessage": "", "functionName": "onCategoryTypeChanged", "line": 369}, {"codegenSuccessfull": false, "column": 27, "durationMicroseconds": 593, "errorMessage": "Cannot access value for name CategorizedSortProxyModel", "functionName": "property", "line": 374}, {"codegenSuccessfull": true, "column": 27, "durationMicroseconds": 304, "errorMessage": "", "functionName": "criteria", "line": 375}, {"codegenSuccessfull": true, "column": 27, "durationMicroseconds": 59, "errorMessage": "", "functionName": "delegate", "line": 376}, {"codegenSuccessfull": true, "column": 35, "durationMicroseconds": 69, "errorMessage": "", "functionName": "labelPositioning", "line": 377}, {"codegenSuccessfull": false, "column": 24, "durationMicroseconds": 57, "errorMessage": "Cannot use shadowable base type for further lookups: QQuickItem::palette with type QQuickPalette (stored as QVariant)", "functionName": "color", "line": 387}, {"codegenSuccessfull": false, "column": 26, "durationMicroseconds": 31, "errorMessage": "Cannot access value for name listView", "functionName": "visible", "line": 388}, {"codegenSuccessfull": true, "column": 27, "durationMicroseconds": 60, "errorMessage": "", "functionName": "fill", "line": 383}, {"codegenSuccessfull": false, "column": 25, "durationMicroseconds": 770, "errorMessage": "Cannot access value for name CategorizedSortProxyModel", "functionName": "onPressed", "line": 401}, {"codegenSuccessfull": false, "column": 32, "durationMicroseconds": 33, "errorMessage": "Cannot access value for name character", "functionName": "onCategoryClicked", "line": 412}, {"codegenSuccessfull": true, "column": 31, "durationMicroseconds": 217, "errorMessage": "", "functionName": "onVisibleChanged", "line": 417}], "filepath": "/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/AppListView.qml"}], "moduleId": "org.deepin.launchpad.windowed(launcher-qml-windowed)"}]