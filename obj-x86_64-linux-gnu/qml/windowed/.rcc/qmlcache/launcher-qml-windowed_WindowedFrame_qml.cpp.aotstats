[{"moduleFiles": [{"entries": [{"codegenSuccessfull": false, "column": 5, "durationMicroseconds": 64, "errorMessage": "function without return type annotation returns double. This may prevent proper compilation to Cpp.", "functionName": "getHorizontalCoordinatesOfSideBar", "line": 32}, {"codegenSuccessfull": false, "column": 5, "durationMicroseconds": 0, "errorMessage": "Functions without type annotations won't be compiled", "functionName": "dropOnItem", "line": 61}, {"codegenSuccessfull": false, "column": 5, "durationMicroseconds": 0, "errorMessage": "Functions without type annotations won't be compiled", "functionName": "dropOnPage", "line": 66}, {"codegenSuccessfull": false, "column": 22, "durationMicroseconds": 0, "errorMessage": "Functions without type annotations won't be compiled", "functionName": "onInputReceived", "line": 279}, {"codegenSuccessfull": false, "column": 22, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type  for binding on onInputReceived.", "functionName": "onInputReceived", "line": 279}, {"codegenSuccessfull": false, "column": 24, "durationMicroseconds": 40, "errorMessage": "cannot convert from QQuickLoader::item with type QObject to QQuickItem", "functionName": "tab", "line": 23}, {"codegenSuccessfull": true, "column": 18, "durationMicroseconds": 119, "errorMessage": "", "functionName": "context", "line": 26}, {"codegenSuccessfull": true, "column": 20, "durationMicroseconds": 195, "errorMessage": "", "functionName": "sequences", "line": 27}, {"codegenSuccessfull": false, "column": 22, "durationMicroseconds": 58, "errorMessage": "Cannot access value for name LauncherController", "functionName": "onActivated", "line": 28}, {"codegenSuccessfull": false, "column": 33, "durationMicroseconds": 48, "errorMessage": "Cannot access value for name LauncherController", "functionName": "onActivatedAmbiguously", "line": 29}, {"codegenSuccessfull": false, "column": 20, "durationMicroseconds": 19, "errorMessage": "Cannot retrieve a non-object type by ID: baseLayer", "functionName": "onClicked", "line": 39}, {"codegenSuccessfull": true, "column": 20, "durationMicroseconds": 45, "errorMessage": "", "functionName": "onClicked", "line": 39}, {"codegenSuccessfull": true, "column": 23, "durationMicroseconds": 52, "errorMessage": "", "functionName": "fill", "line": 38}, {"codegenSuccessfull": false, "column": 18, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type  for binding on visible.", "functionName": "visible", "line": 49}, {"codegenSuccessfull": false, "column": 31, "durationMicroseconds": 51, "errorMessage": "Cannot find name text", "functionName": "onActiveChanged", "line": 52}, {"codegenSuccessfull": true, "column": 27, "durationMicroseconds": 77, "errorMessage": "", "functionName": "nextKeyTabTarget", "line": 79}, {"codegenSuccessfull": true, "column": 23, "durationMicroseconds": 111, "errorMessage": "", "functionName": "left", "line": 74}, {"codegenSuccessfull": true, "column": 22, "durationMicroseconds": 103, "errorMessage": "", "functionName": "top", "line": 75}, {"codegenSuccessfull": true, "column": 25, "durationMicroseconds": 107, "errorMessage": "", "functionName": "bottom", "line": 76}, {"codegenSuccessfull": false, "column": 16, "durationMicroseconds": 145, "errorMessage": "Cannot access value for name Helper", "functionName": "width", "line": 84}, {"codegenSuccessfull": true, "column": 23, "durationMicroseconds": 110, "errorMessage": "", "functionName": "left", "line": 85}, {"codegenSuccessfull": false, "column": 22, "durationMicroseconds": 16, "errorMessage": "Cannot retrieve a non-object type by ID: baseLayer", "functionName": "top", "line": 86}, {"codegenSuccessfull": true, "column": 25, "durationMicroseconds": 103, "errorMessage": "", "functionName": "bottom", "line": 87}, {"codegenSuccessfull": false, "column": 25, "durationMicroseconds": 220, "errorMessage": "Cannot generate efficient code for internal conversion with incompatible or ambiguous types: QVariant -> Dtk::Quick::DColor stored as QVariant", "functionName": "common", "line": 92}, {"codegenSuccessfull": false, "column": 26, "durationMicroseconds": 203, "errorMessage": "Cannot generate efficient code for internal conversion with incompatible or ambiguous types: QVariant -> Dtk::Quick::DColor stored as QVariant", "functionName": "crystal", "line": 93}, {"codegenSuccessfull": false, "column": 25, "durationMicroseconds": 201, "errorMessage": "Cannot generate efficient code for internal conversion with incompatible or ambiguous types: QVariant -> Dtk::Quick::DColor stored as QVariant", "functionName": "common", "line": 96}, {"codegenSuccessfull": false, "column": 26, "durationMicroseconds": 197, "errorMessage": "Cannot generate efficient code for internal conversion with incompatible or ambiguous types: QVariant -> Dtk::Quick::DColor stored as QVariant", "functionName": "crystal", "line": 97}, {"codegenSuccessfull": false, "column": 20, "durationMicroseconds": 42, "errorMessage": "Cannot load property backgroundColor from Dtk::Quick::DQuickControlColorSelector.", "functionName": "color", "line": 102}, {"codegenSuccessfull": true, "column": 23, "durationMicroseconds": 111, "errorMessage": "", "functionName": "left", "line": 108}, {"codegenSuccessfull": true, "column": 22, "durationMicroseconds": 104, "errorMessage": "", "functionName": "top", "line": 109}, {"codegenSuccessfull": true, "column": 24, "durationMicroseconds": 102, "errorMessage": "", "functionName": "right", "line": 110}, {"codegenSuccessfull": true, "column": 25, "durationMicroseconds": 99, "errorMessage": "", "functionName": "bottom", "line": 111}, {"codegenSuccessfull": true, "column": 59, "durationMicroseconds": 68, "errorMessage": "", "functionName": "type", "line": 116}, {"codegenSuccessfull": true, "column": 31, "durationMicroseconds": 72, "errorMessage": "", "functionName": "nextKeyTabTarget", "line": 122}, {"codegenSuccessfull": false, "column": 41, "durationMicroseconds": 30, "errorMessage": "Cannot load property keyTabTarget from QQuickLoader::item with type QObject.", "functionName": "keyTab<PERSON>arget", "line": 127}, {"codegenSuccessfull": false, "column": 30, "durationMicroseconds": 307, "errorMessage": "Cannot generate efficient code for retrieving the metaType of a composite type without an element name.", "functionName": "sourceComponent", "line": 145}, {"codegenSuccessfull": false, "column": 39, "durationMicroseconds": 34, "errorMessage": "Cannot access value for name appList", "functionName": "nextKeyTabTarget", "line": 131}, {"codegenSuccessfull": false, "column": 39, "durationMicroseconds": 24, "errorMessage": "Cannot access value for name appList", "functionName": "nextKeyTabTarget", "line": 137}, {"codegenSuccessfull": true, "column": 31, "durationMicroseconds": 154, "errorMessage": "", "functionName": "alignment", "line": 142}, {"codegenSuccessfull": false, "column": 32, "durationMicroseconds": 126, "errorMessage": "Cannot access value for name Helper", "functionName": "leftMargin", "line": 143}, {"codegenSuccessfull": false, "column": 33, "durationMicroseconds": 98, "errorMessage": "Cannot access value for name Helper", "functionName": "<PERSON><PERSON><PERSON><PERSON>", "line": 144}, {"codegenSuccessfull": true, "column": 27, "durationMicroseconds": 73, "errorMessage": "", "functionName": "nextKeyTabTarget", "line": 155}, {"codegenSuccessfull": true, "column": 23, "durationMicroseconds": 111, "errorMessage": "", "functionName": "left", "line": 152}, {"codegenSuccessfull": true, "column": 24, "durationMicroseconds": 110, "errorMessage": "", "functionName": "right", "line": 153}, {"codegenSuccessfull": true, "column": 25, "durationMicroseconds": 108, "errorMessage": "", "functionName": "bottom", "line": 154}, {"codegenSuccessfull": false, "column": 17, "durationMicroseconds": 147, "errorMessage": "Cannot access value for name Helper", "functionName": "height", "line": 160}, {"codegenSuccessfull": false, "column": 23, "durationMicroseconds": 18, "errorMessage": "Cannot retrieve a non-object type by ID: baseLayer", "functionName": "left", "line": 161}, {"codegenSuccessfull": false, "column": 24, "durationMicroseconds": 15, "errorMessage": "Cannot retrieve a non-object type by ID: baseLayer", "functionName": "right", "line": 162}, {"codegenSuccessfull": true, "column": 25, "durationMicroseconds": 108, "errorMessage": "", "functionName": "bottom", "line": 163}, {"codegenSuccessfull": false, "column": 25, "durationMicroseconds": 230, "errorMessage": "Cannot generate efficient code for internal conversion with incompatible or ambiguous types: QVariant -> Dtk::Quick::DColor stored as QVariant", "functionName": "common", "line": 167}, {"codegenSuccessfull": false, "column": 26, "durationMicroseconds": 208, "errorMessage": "Cannot generate efficient code for internal conversion with incompatible or ambiguous types: QVariant -> Dtk::Quick::DColor stored as QVariant", "functionName": "crystal", "line": 168}, {"codegenSuccessfull": false, "column": 25, "durationMicroseconds": 213, "errorMessage": "Cannot generate efficient code for internal conversion with incompatible or ambiguous types: QVariant -> Dtk::Quick::DColor stored as QVariant", "functionName": "common", "line": 171}, {"codegenSuccessfull": false, "column": 26, "durationMicroseconds": 215, "errorMessage": "Cannot generate efficient code for internal conversion with incompatible or ambiguous types: QVariant -> Dtk::Quick::DColor stored as QVariant", "functionName": "crystal", "line": 172}, {"codegenSuccessfull": false, "column": 20, "durationMicroseconds": 43, "errorMessage": "Cannot load property backgroundColor from Dtk::Quick::DQuickControlColorSelector.", "functionName": "color", "line": 177}, {"codegenSuccessfull": false, "column": 25, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type  for binding on folderNameFont.", "functionName": "folderNameFont", "line": 185}, {"codegenSuccessfull": false, "column": 25, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type  for binding on centerPosition.", "functionName": "centerPosition", "line": 186}, {"codegenSuccessfull": false, "column": 43, "durationMicroseconds": 47, "errorMessage": "Cannot access value for name parent", "functionName": "endPoint", "line": 191}, {"codegenSuccessfull": false, "column": 27, "durationMicroseconds": 0, "errorMessage": "Functions without type annotations won't be compiled", "functionName": "onVisibleChanged", "line": 195}, {"codegenSuccessfull": false, "column": 27, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type  for binding on onVisibleChanged.", "functionName": "onVisibleChanged", "line": 195}, {"codegenSuccessfull": false, "column": 31, "durationMicroseconds": 20, "errorMessage": "Cannot retrieve a non-object type by ID: folderGridViewPopup", "functionName": "duration", "line": 204}, {"codegenSuccessfull": true, "column": 34, "durationMicroseconds": 70, "errorMessage": "", "functionName": "type", "line": 206}, {"codegenSuccessfull": false, "column": 31, "durationMicroseconds": 19, "errorMessage": "Cannot retrieve a non-object type by ID: folderGridViewPopup", "functionName": "duration", "line": 211}, {"codegenSuccessfull": false, "column": 27, "durationMicroseconds": 14, "errorMessage": "Cannot retrieve a non-object type by ID: folderGridViewPopup", "functionName": "from", "line": 214}, {"codegenSuccessfull": false, "column": 25, "durationMicroseconds": 14, "errorMessage": "Cannot retrieve a non-object type by ID: folderGridViewPopup", "functionName": "to", "line": 215}, {"codegenSuccessfull": true, "column": 34, "durationMicroseconds": 71, "errorMessage": "", "functionName": "type", "line": 213}, {"codegenSuccessfull": false, "column": 31, "durationMicroseconds": 19, "errorMessage": "Cannot retrieve a non-object type by ID: folderGridViewPopup", "functionName": "duration", "line": 218}, {"codegenSuccessfull": false, "column": 27, "durationMicroseconds": 15, "errorMessage": "Cannot retrieve a non-object type by ID: folderGridViewPopup", "functionName": "from", "line": 221}, {"codegenSuccessfull": false, "column": 25, "durationMicroseconds": 14, "errorMessage": "Cannot retrieve a non-object type by ID: folderGridViewPopup", "functionName": "to", "line": 222}, {"codegenSuccessfull": true, "column": 34, "durationMicroseconds": 74, "errorMessage": "", "functionName": "type", "line": 220}, {"codegenSuccessfull": false, "column": 31, "durationMicroseconds": 21, "errorMessage": "Cannot retrieve a non-object type by ID: folderGridViewPopup", "functionName": "duration", "line": 230}, {"codegenSuccessfull": true, "column": 34, "durationMicroseconds": 64, "errorMessage": "", "functionName": "type", "line": 232}, {"codegenSuccessfull": false, "column": 31, "durationMicroseconds": 18, "errorMessage": "Cannot retrieve a non-object type by ID: folderGridViewPopup", "functionName": "duration", "line": 237}, {"codegenSuccessfull": false, "column": 25, "durationMicroseconds": 14, "errorMessage": "Cannot retrieve a non-object type by ID: folderGridViewPopup", "functionName": "to", "line": 240}, {"codegenSuccessfull": false, "column": 27, "durationMicroseconds": 17, "errorMessage": "Cannot retrieve a non-object type by ID: folderGridViewPopup", "functionName": "from", "line": 241}, {"codegenSuccessfull": true, "column": 34, "durationMicroseconds": 77, "errorMessage": "", "functionName": "type", "line": 239}, {"codegenSuccessfull": false, "column": 31, "durationMicroseconds": 19, "errorMessage": "Cannot retrieve a non-object type by ID: folderGridViewPopup", "functionName": "duration", "line": 244}, {"codegenSuccessfull": false, "column": 25, "durationMicroseconds": 14, "errorMessage": "Cannot retrieve a non-object type by ID: folderGridViewPopup", "functionName": "to", "line": 247}, {"codegenSuccessfull": false, "column": 27, "durationMicroseconds": 14, "errorMessage": "Cannot retrieve a non-object type by ID: folderGridViewPopup", "functionName": "from", "line": 248}, {"codegenSuccessfull": true, "column": 34, "durationMicroseconds": 72, "errorMessage": "", "functionName": "type", "line": 246}, {"codegenSuccessfull": true, "column": 21, "durationMicroseconds": 178, "errorMessage": "", "functionName": "forwardTo", "line": 254}, {"codegenSuccessfull": false, "column": 21, "durationMicroseconds": 85, "errorMessage": "Cannot retrieve a non-object type by ID: baseLayer", "functionName": "onPressed", "line": 255}, {"codegenSuccessfull": true, "column": 21, "durationMicroseconds": 47, "errorMessage": "", "functionName": "onPressed", "line": 255}, {"codegenSuccessfull": false, "column": 27, "durationMicroseconds": 65, "errorMessage": "Cannot access value for name DebugHelper", "functionName": "onEscapePressed", "line": 274}, {"codegenSuccessfull": true, "column": 27, "durationMicroseconds": 41, "errorMessage": "", "functionName": "onEscapePressed", "line": 274}, {"codegenSuccessfull": false, "column": 17, "durationMicroseconds": 50, "errorMessage": "Cannot access value for name LauncherController", "functionName": "target", "line": 287}, {"codegenSuccessfull": false, "column": 9, "durationMicroseconds": 41, "errorMessage": "Cannot access value for name LauncherController", "functionName": "onVisibleChanged", "line": 288}, {"codegenSuccessfull": true, "column": 17, "durationMicroseconds": 76, "errorMessage": "", "functionName": "target", "line": 303}, {"codegenSuccessfull": false, "column": 9, "durationMicroseconds": 0, "errorMessage": "Functions without type annotations won't be compiled", "functionName": "onSwitchToFreeSort", "line": 304}, {"codegenSuccessfull": true, "column": 17, "durationMicroseconds": 81, "errorMessage": "", "functionName": "target", "line": 310}, {"codegenSuccessfull": false, "column": 9, "durationMicroseconds": 0, "errorMessage": "Functions without type annotations won't be compiled", "functionName": "onFreeSortViewFolderClicked", "line": 311}], "filepath": "/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/WindowedFrame.qml"}], "moduleId": "org.deepin.launchpad.windowed(launcher-qml-windowed)"}]