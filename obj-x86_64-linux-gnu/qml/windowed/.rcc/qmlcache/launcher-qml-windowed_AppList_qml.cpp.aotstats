[{"moduleFiles": [{"entries": [{"codegenSuccessfull": false, "column": 33, "durationMicroseconds": 70, "errorMessage": "cannot convert from QQuickLoader::item with type QObject to QQuickItem", "functionName": "keyTab<PERSON>arget", "line": 21}, {"codegenSuccessfull": false, "column": 21, "durationMicroseconds": 25, "errorMessage": "Type QQuickLoader::item with type QObject does not have a property focus for writing", "functionName": "onFocusChanged", "line": 24}, {"codegenSuccessfull": true, "column": 21, "durationMicroseconds": 56, "errorMessage": "", "functionName": "onFocusChanged", "line": 24}, {"codegenSuccessfull": false, "column": 5, "durationMicroseconds": 227, "errorMessage": "Cannot access value for name CategorizedSortProxyModel", "functionName": "resetViewState", "line": 28}, {"codegenSuccessfull": false, "column": 5, "durationMicroseconds": 0, "errorMessage": "Functions without type annotations won't be compiled", "functionName": "switchToFreeSort", "line": 34}, {"codegenSuccessfull": false, "column": 26, "durationMicroseconds": 202, "errorMessage": "Cannot access value for name CategorizedSortProxyModel", "functionName": "sourceComponent", "line": 44}, {"codegenSuccessfull": false, "column": 32, "durationMicroseconds": 35, "errorMessage": "Cannot access value for name nextKeyTabTarget", "functionName": "tab", "line": 55}, {"codegenSuccessfull": false, "column": 30, "durationMicroseconds": 26, "errorMessage": "Cannot access value for name folderId", "functionName": "onFolderClicked", "line": 64}, {"codegenSuccessfull": false, "column": 32, "durationMicroseconds": 20, "errorMessage": "Cannot access value for name nextKeyTabTarget", "functionName": "tab", "line": 69}, {"codegenSuccessfull": true, "column": 26, "durationMicroseconds": 139, "errorMessage": "", "functionName": "acceptedButtons", "line": 77}, {"codegenSuccessfull": false, "column": 18, "durationMicroseconds": 184, "errorMessage": "method closeContextMenu cannot be resolved.", "functionName": "onWheel", "line": 79}, {"codegenSuccessfull": true, "column": 18, "durationMicroseconds": 48, "errorMessage": "", "functionName": "onWheel", "line": 79}, {"codegenSuccessfull": true, "column": 23, "durationMicroseconds": 60, "errorMessage": "", "functionName": "fill", "line": 74}], "filepath": "/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/AppList.qml"}], "moduleId": "org.deepin.launchpad.windowed(launcher-qml-windowed)"}]