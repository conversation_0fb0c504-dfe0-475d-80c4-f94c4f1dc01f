[{"moduleFiles": [{"entries": [{"codegenSuccessfull": true, "column": 33, "durationMicroseconds": 114, "errorMessage": "", "functionName": "keyTab<PERSON>arget", "line": 19}, {"codegenSuccessfull": false, "column": 24, "durationMicroseconds": 174, "errorMessage": "Cannot access value for name DS", "functionName": "onClicked", "line": 36}, {"codegenSuccessfull": true, "column": 24, "durationMicroseconds": 102, "errorMessage": "", "functionName": "button", "line": 30}, {"codegenSuccessfull": true, "column": 30, "durationMicroseconds": 76, "errorMessage": "", "functionName": "visible", "line": 32}, {"codegenSuccessfull": true, "column": 31, "durationMicroseconds": 171, "errorMessage": "", "functionName": "alignment", "line": 35}, {"codegenSuccessfull": true, "column": 31, "durationMicroseconds": 150, "errorMessage": "", "functionName": "alignment", "line": 49}, {"codegenSuccessfull": false, "column": 45, "durationMicroseconds": 260, "errorMessage": "Cannot access value for name DStyle", "functionName": "textColor", "line": 56}, {"codegenSuccessfull": false, "column": 39, "durationMicroseconds": 36, "errorMessage": "Cannot load property textColor from Dtk::Quick::DQuickControlColorSelector.", "functionName": "placeholderTextColor", "line": 57}, {"codegenSuccessfull": false, "column": 32, "durationMicroseconds": 481, "errorMessage": "Cannot access value for name SearchFilterProxyModel", "functionName": "onTextChanged", "line": 61}, {"codegenSuccessfull": true, "column": 34, "durationMicroseconds": 192, "errorMessage": "", "functionName": "backgroundColor", "line": 92}, {"codegenSuccessfull": false, "column": 37, "durationMicroseconds": 34, "errorMessage": "Cannot load property textColor from Dtk::Quick::DQuickControlColorSelector.", "functionName": "windowText", "line": 58}, {"codegenSuccessfull": false, "column": 34, "durationMicroseconds": 233, "errorMessage": "Cannot generate efficient code for internal conversion with incompatible or ambiguous types: QVariant -> Dtk::Quick::DColor stored as QVariant", "functionName": "crystal", "line": 70}, {"codegenSuccessfull": false, "column": 34, "durationMicroseconds": 210, "errorMessage": "Cannot generate efficient code for internal conversion with incompatible or ambiguous types: QVariant -> Dtk::Quick::DColor stored as QVariant", "functionName": "crystal", "line": 73}, {"codegenSuccessfull": false, "column": 34, "durationMicroseconds": 31, "errorMessage": "cannot convert from QString to Dtk::Quick::DColor", "functionName": "crystal", "line": 79}, {"codegenSuccessfull": false, "column": 34, "durationMicroseconds": 26, "errorMessage": "cannot convert from QString to Dtk::Quick::DColor", "functionName": "crystal", "line": 82}, {"codegenSuccessfull": false, "column": 35, "durationMicroseconds": 210, "errorMessage": "Cannot generate efficient code for internal conversion with incompatible or ambiguous types: QVariant -> Dtk::Quick::DColor stored as QVariant", "functionName": "crystal", "line": 85}, {"codegenSuccessfull": false, "column": 35, "durationMicroseconds": 214, "errorMessage": "Cannot generate efficient code for internal conversion with incompatible or ambiguous types: QVariant -> Dtk::Quick::DColor stored as QVariant", "functionName": "crystal", "line": 88}, {"codegenSuccessfull": false, "column": 24, "durationMicroseconds": 332, "errorMessage": "Cannot access value for name LauncherController", "functionName": "onClicked", "line": 107}, {"codegenSuccessfull": true, "column": 25, "durationMicroseconds": 79, "errorMessage": "", "functionName": "button", "line": 100}, {"codegenSuccessfull": true, "column": 30, "durationMicroseconds": 52, "errorMessage": "", "functionName": "visible", "line": 102}, {"codegenSuccessfull": true, "column": 31, "durationMicroseconds": 156, "errorMessage": "", "functionName": "alignment", "line": 106}, {"codegenSuccessfull": false, "column": 32, "durationMicroseconds": 38, "errorMessage": "Cannot access value for name nextKeyTabTarget", "functionName": "tab", "line": 111}], "filepath": "/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/BottomBar.qml"}], "moduleId": "org.deepin.launchpad.windowed(launcher-qml-windowed)"}]