#include <QtQml/qqmlprivate.h>
#include <QtCore/qdir.h>
#include <QtCore/qurl.h>
#include <QtCore/qhash.h>
#include <QtCore/qstring.h>

namespace QmlCacheGeneratedCode {
namespace _qt_qml_org_deepin_launchpad_windowed_WindowedFrame_qml { 
    extern const unsigned char qmlData[];
    extern const QQmlPrivate::AOTCompiledFunction aotBuiltFunctions[];
    const QQmlPrivate::CachedQmlUnit unit = {
        reinterpret_cast<const QV4::CompiledData::Unit*>(&qmlData), &aotBuiltFunctions[0], nullptr
    };
}
namespace _qt_qml_org_deepin_launchpad_windowed_SideBar_qml { 
    extern const unsigned char qmlData[];
    extern const QQmlPrivate::AOTCompiledFunction aotBuiltFunctions[];
    const QQmlPrivate::CachedQmlUnit unit = {
        reinterpret_cast<const QV4::CompiledData::Unit*>(&qmlData), &aotBuiltFunctions[0], nullptr
    };
}
namespace _qt_qml_org_deepin_launchpad_windowed_BottomBar_qml { 
    extern const unsigned char qmlData[];
    extern const QQmlPrivate::AOTCompiledFunction aotBuiltFunctions[];
    const QQmlPrivate::CachedQmlUnit unit = {
        reinterpret_cast<const QV4::CompiledData::Unit*>(&qmlData), &aotBuiltFunctions[0], nullptr
    };
}
namespace _qt_qml_org_deepin_launchpad_windowed_AlphabetCategoryPopup_qml { 
    extern const unsigned char qmlData[];
    extern const QQmlPrivate::AOTCompiledFunction aotBuiltFunctions[];
    const QQmlPrivate::CachedQmlUnit unit = {
        reinterpret_cast<const QV4::CompiledData::Unit*>(&qmlData), &aotBuiltFunctions[0], nullptr
    };
}
namespace _qt_qml_org_deepin_launchpad_windowed_AppList_qml { 
    extern const unsigned char qmlData[];
    extern const QQmlPrivate::AOTCompiledFunction aotBuiltFunctions[];
    const QQmlPrivate::CachedQmlUnit unit = {
        reinterpret_cast<const QV4::CompiledData::Unit*>(&qmlData), &aotBuiltFunctions[0], nullptr
    };
}
namespace _qt_qml_org_deepin_launchpad_windowed_AppListView_qml { 
    extern const unsigned char qmlData[];
    extern const QQmlPrivate::AOTCompiledFunction aotBuiltFunctions[];
    const QQmlPrivate::CachedQmlUnit unit = {
        reinterpret_cast<const QV4::CompiledData::Unit*>(&qmlData), &aotBuiltFunctions[0], nullptr
    };
}
namespace _qt_qml_org_deepin_launchpad_windowed_IconItemDelegate_qml { 
    extern const unsigned char qmlData[];
    extern const QQmlPrivate::AOTCompiledFunction aotBuiltFunctions[];
    const QQmlPrivate::CachedQmlUnit unit = {
        reinterpret_cast<const QV4::CompiledData::Unit*>(&qmlData), &aotBuiltFunctions[0], nullptr
    };
}
namespace _qt_qml_org_deepin_launchpad_windowed_FreeSortListView_qml { 
    extern const unsigned char qmlData[];
    extern const QQmlPrivate::AOTCompiledFunction aotBuiltFunctions[];
    const QQmlPrivate::CachedQmlUnit unit = {
        reinterpret_cast<const QV4::CompiledData::Unit*>(&qmlData), &aotBuiltFunctions[0], nullptr
    };
}
namespace _qt_qml_org_deepin_launchpad_windowed_GridViewContainer_qml { 
    extern const unsigned char qmlData[];
    extern const QQmlPrivate::AOTCompiledFunction aotBuiltFunctions[];
    const QQmlPrivate::CachedQmlUnit unit = {
        reinterpret_cast<const QV4::CompiledData::Unit*>(&qmlData), &aotBuiltFunctions[0], nullptr
    };
}
namespace _qt_qml_org_deepin_launchpad_windowed_AnalysisView_qml { 
    extern const unsigned char qmlData[];
    extern const QQmlPrivate::AOTCompiledFunction aotBuiltFunctions[];
    const QQmlPrivate::CachedQmlUnit unit = {
        reinterpret_cast<const QV4::CompiledData::Unit*>(&qmlData), &aotBuiltFunctions[0], nullptr
    };
}
namespace _qt_qml_org_deepin_launchpad_windowed_RecentlyInstalledView_qml { 
    extern const unsigned char qmlData[];
    extern const QQmlPrivate::AOTCompiledFunction aotBuiltFunctions[];
    const QQmlPrivate::CachedQmlUnit unit = {
        reinterpret_cast<const QV4::CompiledData::Unit*>(&qmlData), &aotBuiltFunctions[0], nullptr
    };
}
namespace _qt_qml_org_deepin_launchpad_windowed_FrequentlyUsedView_qml { 
    extern const unsigned char qmlData[];
    extern const QQmlPrivate::AOTCompiledFunction aotBuiltFunctions[];
    const QQmlPrivate::CachedQmlUnit unit = {
        reinterpret_cast<const QV4::CompiledData::Unit*>(&qmlData), &aotBuiltFunctions[0], nullptr
    };
}
namespace _qt_qml_org_deepin_launchpad_windowed_SearchResultView_qml { 
    extern const unsigned char qmlData[];
    extern const QQmlPrivate::AOTCompiledFunction aotBuiltFunctions[];
    const QQmlPrivate::CachedQmlUnit unit = {
        reinterpret_cast<const QV4::CompiledData::Unit*>(&qmlData), &aotBuiltFunctions[0], nullptr
    };
}
namespace _qt_qml_org_deepin_launchpad_windowed_ItemBackground_qml { 
    extern const unsigned char qmlData[];
    extern const QQmlPrivate::AOTCompiledFunction aotBuiltFunctions[];
    const QQmlPrivate::CachedQmlUnit unit = {
        reinterpret_cast<const QV4::CompiledData::Unit*>(&qmlData), &aotBuiltFunctions[0], nullptr
    };
}

}
namespace {
struct Registry {
    Registry();
    ~Registry();
    QHash<QString, const QQmlPrivate::CachedQmlUnit*> resourcePathToCachedUnit;
    static const QQmlPrivate::CachedQmlUnit *lookupCachedUnit(const QUrl &url);
};

Q_GLOBAL_STATIC(Registry, unitRegistry)


Registry::Registry() {
    resourcePathToCachedUnit.insert(QStringLiteral("/qt/qml/org/deepin/launchpad/windowed/WindowedFrame.qml"), &QmlCacheGeneratedCode::_qt_qml_org_deepin_launchpad_windowed_WindowedFrame_qml::unit);
    resourcePathToCachedUnit.insert(QStringLiteral("/qt/qml/org/deepin/launchpad/windowed/SideBar.qml"), &QmlCacheGeneratedCode::_qt_qml_org_deepin_launchpad_windowed_SideBar_qml::unit);
    resourcePathToCachedUnit.insert(QStringLiteral("/qt/qml/org/deepin/launchpad/windowed/BottomBar.qml"), &QmlCacheGeneratedCode::_qt_qml_org_deepin_launchpad_windowed_BottomBar_qml::unit);
    resourcePathToCachedUnit.insert(QStringLiteral("/qt/qml/org/deepin/launchpad/windowed/AlphabetCategoryPopup.qml"), &QmlCacheGeneratedCode::_qt_qml_org_deepin_launchpad_windowed_AlphabetCategoryPopup_qml::unit);
    resourcePathToCachedUnit.insert(QStringLiteral("/qt/qml/org/deepin/launchpad/windowed/AppList.qml"), &QmlCacheGeneratedCode::_qt_qml_org_deepin_launchpad_windowed_AppList_qml::unit);
    resourcePathToCachedUnit.insert(QStringLiteral("/qt/qml/org/deepin/launchpad/windowed/AppListView.qml"), &QmlCacheGeneratedCode::_qt_qml_org_deepin_launchpad_windowed_AppListView_qml::unit);
    resourcePathToCachedUnit.insert(QStringLiteral("/qt/qml/org/deepin/launchpad/windowed/IconItemDelegate.qml"), &QmlCacheGeneratedCode::_qt_qml_org_deepin_launchpad_windowed_IconItemDelegate_qml::unit);
    resourcePathToCachedUnit.insert(QStringLiteral("/qt/qml/org/deepin/launchpad/windowed/FreeSortListView.qml"), &QmlCacheGeneratedCode::_qt_qml_org_deepin_launchpad_windowed_FreeSortListView_qml::unit);
    resourcePathToCachedUnit.insert(QStringLiteral("/qt/qml/org/deepin/launchpad/windowed/GridViewContainer.qml"), &QmlCacheGeneratedCode::_qt_qml_org_deepin_launchpad_windowed_GridViewContainer_qml::unit);
    resourcePathToCachedUnit.insert(QStringLiteral("/qt/qml/org/deepin/launchpad/windowed/AnalysisView.qml"), &QmlCacheGeneratedCode::_qt_qml_org_deepin_launchpad_windowed_AnalysisView_qml::unit);
    resourcePathToCachedUnit.insert(QStringLiteral("/qt/qml/org/deepin/launchpad/windowed/RecentlyInstalledView.qml"), &QmlCacheGeneratedCode::_qt_qml_org_deepin_launchpad_windowed_RecentlyInstalledView_qml::unit);
    resourcePathToCachedUnit.insert(QStringLiteral("/qt/qml/org/deepin/launchpad/windowed/FrequentlyUsedView.qml"), &QmlCacheGeneratedCode::_qt_qml_org_deepin_launchpad_windowed_FrequentlyUsedView_qml::unit);
    resourcePathToCachedUnit.insert(QStringLiteral("/qt/qml/org/deepin/launchpad/windowed/SearchResultView.qml"), &QmlCacheGeneratedCode::_qt_qml_org_deepin_launchpad_windowed_SearchResultView_qml::unit);
    resourcePathToCachedUnit.insert(QStringLiteral("/qt/qml/org/deepin/launchpad/windowed/ItemBackground.qml"), &QmlCacheGeneratedCode::_qt_qml_org_deepin_launchpad_windowed_ItemBackground_qml::unit);
    QQmlPrivate::RegisterQmlUnitCacheHook registration;
    registration.structVersion = 0;
    registration.lookupCachedQmlUnit = &lookupCachedUnit;
    QQmlPrivate::qmlregister(QQmlPrivate::QmlUnitCacheHookRegistration, &registration);
}

Registry::~Registry() {
    QQmlPrivate::qmlunregister(QQmlPrivate::QmlUnitCacheHookRegistration, quintptr(&lookupCachedUnit));
}

const QQmlPrivate::CachedQmlUnit *Registry::lookupCachedUnit(const QUrl &url) {
    if (url.scheme() != QLatin1String("qrc"))
        return nullptr;
    QString resourcePath = QDir::cleanPath(url.path());
    if (resourcePath.isEmpty())
        return nullptr;
    if (!resourcePath.startsWith(QLatin1Char('/')))
        resourcePath.prepend(QLatin1Char('/'));
    return unitRegistry()->resourcePathToCachedUnit.value(resourcePath, nullptr);
}
}
int QT_MANGLE_NAMESPACE(qInitResources_qmlcache_launcher_qml_windowed)() {
    ::unitRegistry();
    return 1;
}
Q_CONSTRUCTOR_FUNCTION(QT_MANGLE_NAMESPACE(qInitResources_qmlcache_launcher_qml_windowed))
int QT_MANGLE_NAMESPACE(qCleanupResources_qmlcache_launcher_qml_windowed)() {
    return 1;
}
