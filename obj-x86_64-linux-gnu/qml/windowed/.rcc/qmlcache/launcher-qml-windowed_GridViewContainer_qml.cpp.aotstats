[{"moduleFiles": [{"entries": [{"codegenSuccessfull": true, "column": 12, "durationMicroseconds": 115, "errorMessage": "", "functionName": "width", "line": 17}, {"codegenSuccessfull": true, "column": 13, "durationMicroseconds": 88, "errorMessage": "", "functionName": "height", "line": 18}, {"codegenSuccessfull": false, "column": 24, "durationMicroseconds": 221, "errorMessage": "Cannot access value for name Helper", "functionName": "rows", "line": 29}, {"codegenSuccessfull": false, "column": 34, "durationMicroseconds": 158, "errorMessage": "Cannot access value for name Helper", "functionName": "paddingColumns", "line": 30}, {"codegenSuccessfull": false, "column": 31, "durationMicroseconds": 153, "errorMessage": "Cannot access value for name Helper", "functionName": "paddingRows", "line": 31}, {"codegenSuccessfull": true, "column": 5, "durationMicroseconds": 113, "errorMessage": "", "functionName": "positionViewAtBeginning", "line": 42}, {"codegenSuccessfull": false, "column": 5, "durationMicroseconds": 0, "errorMessage": "Functions without type annotations won't be compiled", "functionName": "itemAt", "line": 46}, {"codegenSuccessfull": false, "column": 5, "durationMicroseconds": 0, "errorMessage": "Functions without type annotations won't be compiled", "functionName": "indexAt", "line": 51}, {"codegenSuccessfull": true, "column": 23, "durationMicroseconds": 60, "errorMessage": "", "functionName": "fill", "line": 59}, {"codegenSuccessfull": false, "column": 20, "durationMicroseconds": 67, "errorMessage": "Cannot access value for name columns", "functionName": "width", "line": 63}, {"codegenSuccessfull": false, "column": 21, "durationMicroseconds": 54, "errorMessage": "Cannot access value for name rows", "functionName": "height", "line": 64}, {"codegenSuccessfull": true, "column": 31, "durationMicroseconds": 164, "errorMessage": "", "functionName": "activeFocusOnTab", "line": 74}, {"codegenSuccessfull": true, "column": 20, "durationMicroseconds": 100, "errorMessage": "", "functionName": "focus", "line": 75}, {"codegenSuccessfull": true, "column": 35, "durationMicroseconds": 879, "errorMessage": "", "functionName": "onActiveFocusChanged", "line": 76}, {"codegenSuccessfull": false, "column": 25, "durationMicroseconds": 58, "errorMessage": "Cannot access value for name paddingRows", "functionName": "cellHeight", "line": 87}, {"codegenSuccessfull": false, "column": 24, "durationMicroseconds": 48, "errorMessage": "Cannot access value for name paddingColumns", "functionName": "cellWidth", "line": 88}, {"codegenSuccessfull": true, "column": 24, "durationMicroseconds": 70, "errorMessage": "", "functionName": "displaced", "line": 90}, {"codegenSuccessfull": true, "column": 19, "durationMicroseconds": 64, "errorMessage": "", "functionName": "move", "line": 91}, {"codegenSuccessfull": true, "column": 28, "durationMicroseconds": 62, "errorMessage": "", "functionName": "moveDisplaced", "line": 92}, {"codegenSuccessfull": true, "column": 33, "durationMicroseconds": 79, "errorMessage": "", "functionName": "vertical", "line": 65}, {"codegenSuccessfull": true, "column": 31, "durationMicroseconds": 47, "errorMessage": "", "functionName": "centerIn", "line": 67}, {"codegenSuccessfull": false, "column": 28, "durationMicroseconds": 43, "errorMessage": "Cannot use shadowable base type for further lookups: QQuickItem::palette with type QQuickPalette (stored as QVariant)", "functionName": "color", "line": 101}, {"codegenSuccessfull": false, "column": 30, "durationMicroseconds": 25, "errorMessage": "Cannot access value for name gridView", "functionName": "visible", "line": 102}, {"codegenSuccessfull": true, "column": 31, "durationMicroseconds": 47, "errorMessage": "", "functionName": "fill", "line": 97}, {"codegenSuccessfull": false, "column": 28, "durationMicroseconds": 25, "errorMessage": "Cannot load property themeType from Dtk::Quick::DQMLGlobalObject.", "functionName": "color", "line": 110}, {"codegenSuccessfull": false, "column": 30, "durationMicroseconds": 23, "errorMessage": "Cannot access value for name alwaysShowHighlighted", "functionName": "visible", "line": 111}, {"codegenSuccessfull": true, "column": 31, "durationMicroseconds": 71, "errorMessage": "", "functionName": "fill", "line": 106}, {"codegenSuccessfull": true, "column": 29, "durationMicroseconds": 951, "errorMessage": "", "functionName": "onPressed", "line": 115}, {"codegenSuccessfull": true, "column": 29, "durationMicroseconds": 48, "errorMessage": "", "functionName": "onPressed", "line": 115}], "filepath": "/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/GridViewContainer.qml"}], "moduleId": "org.deepin.launchpad.windowed(launcher-qml-windowed)"}]