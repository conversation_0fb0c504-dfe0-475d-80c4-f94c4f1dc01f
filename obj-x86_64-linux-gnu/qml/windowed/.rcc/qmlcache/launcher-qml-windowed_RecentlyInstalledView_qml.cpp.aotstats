[{"moduleFiles": [{"entries": [{"codegenSuccessfull": true, "column": 33, "durationMicroseconds": 123, "errorMessage": "", "functionName": "keyTab<PERSON>arget", "line": 18}, {"codegenSuccessfull": true, "column": 34, "durationMicroseconds": 80, "errorMessage": "", "functionName": "count", "line": 20}, {"codegenSuccessfull": true, "column": 21, "durationMicroseconds": 86, "errorMessage": "", "functionName": "onFocusChanged", "line": 23}, {"codegenSuccessfull": true, "column": 21, "durationMicroseconds": 34, "errorMessage": "", "functionName": "onFocusChanged", "line": 23}, {"codegenSuccessfull": true, "column": 5, "durationMicroseconds": 90, "errorMessage": "", "functionName": "positionViewAtBeginning", "line": 27}, {"codegenSuccessfull": false, "column": 19, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type  for binding on font.", "functionName": "font", "line": 35}, {"codegenSuccessfull": true, "column": 31, "durationMicroseconds": 108, "errorMessage": "", "functionName": "activeFocusOnTab", "line": 67}, {"codegenSuccessfull": false, "column": 32, "durationMicroseconds": 37, "errorMessage": "Cannot access value for name nextKeyTabTarget", "functionName": "tab", "line": 41}, {"codegenSuccessfull": true, "column": 31, "durationMicroseconds": 94, "errorMessage": "", "functionName": "alignment", "line": 42}, {"codegenSuccessfull": true, "column": 37, "durationMicroseconds": 72, "errorMessage": "", "functionName": "preferredHeight", "line": 43}, {"codegenSuccessfull": true, "column": 36, "durationMicroseconds": 87, "errorMessage": "", "functionName": "preferredWidth", "line": 44}, {"codegenSuccessfull": false, "column": 29, "durationMicroseconds": 181, "errorMessage": "Cannot access value for name iconName", "functionName": "iconSource", "line": 53}, {"codegenSuccessfull": false, "column": 24, "durationMicroseconds": 28, "errorMessage": "Cannot access value for name recentlyInstalledViewContainer", "functionName": "width", "line": 54}, {"codegenSuccessfull": false, "column": 25, "durationMicroseconds": 23, "errorMessage": "Cannot access value for name recentlyInstalledViewContainer", "functionName": "height", "line": 55}, {"codegenSuccessfull": false, "column": 29, "durationMicroseconds": 228, "errorMessage": "Cannot access value for name folderGridViewPopup", "functionName": "dndEnabled", "line": 57}, {"codegenSuccessfull": false, "column": 32, "durationMicroseconds": 161, "errorMessage": "Cannot access value for name desktopId", "functionName": "onItemClicked", "line": 59}, {"codegenSuccessfull": false, "column": 34, "durationMicroseconds": 31, "errorMessage": "Cannot access value for name model", "functionName": "onMenuTriggered", "line": 62}, {"codegenSuccessfull": false, "column": 32, "durationMicroseconds": 154, "errorMessage": "Cannot access value for name Helper", "functionName": "mimeData", "line": 58}], "filepath": "/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/RecentlyInstalledView.qml"}], "moduleId": "org.deepin.launchpad.windowed(launcher-qml-windowed)"}]