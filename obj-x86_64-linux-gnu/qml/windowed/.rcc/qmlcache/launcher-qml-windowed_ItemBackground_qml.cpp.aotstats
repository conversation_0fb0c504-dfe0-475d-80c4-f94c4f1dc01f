[{"moduleFiles": [{"entries": [{"codegenSuccessfull": false, "column": 36, "durationMicroseconds": 200, "errorMessage": "Cannot access value for name Helper", "functionName": "background", "line": 14}, {"codegenSuccessfull": false, "column": 20, "durationMicroseconds": 112, "errorMessage": "Cannot access value for name DS", "functionName": "implicitWidth", "line": 16}, {"codegenSuccessfull": false, "column": 21, "durationMicroseconds": 111, "errorMessage": "Cannot access value for name DS", "functionName": "implicitHeight", "line": 17}, {"codegenSuccessfull": false, "column": 13, "durationMicroseconds": 155, "errorMessage": "Cannot access value for name DS", "functionName": "color1", "line": 18}, {"codegenSuccessfull": false, "column": 13, "durationMicroseconds": 137, "errorMessage": "Cannot access value for name DS", "functionName": "color2", "line": 19}, {"codegenSuccessfull": false, "column": 14, "durationMicroseconds": 75, "errorMessage": "Cannot load property checked from (component in /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/ItemBackground.qml)::button with type QQuickItem.", "functionName": "visible", "line": 22}, {"codegenSuccessfull": false, "column": 5, "durationMicroseconds": 0, "errorMessage": "Functions without type annotations won't be compiled", "functionName": "selectValue", "line": 24}, {"codegenSuccessfull": false, "column": 17, "durationMicroseconds": 40, "errorMessage": "Cannot access value for name button", "functionName": "active", "line": 36}, {"codegenSuccessfull": true, "column": 23, "durationMicroseconds": 94, "errorMessage": "", "functionName": "fill", "line": 35}, {"codegenSuccessfull": false, "column": 21, "durationMicroseconds": 35, "errorMessage": "Cannot access value for name control", "functionName": "radius", "line": 39}, {"codegenSuccessfull": false, "column": 20, "durationMicroseconds": 30, "errorMessage": "Cannot access value for name button", "functionName": "color", "line": 40}], "filepath": "/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/ItemBackground.qml"}], "moduleId": "org.deepin.launchpad.windowed(launcher-qml-windowed)"}]