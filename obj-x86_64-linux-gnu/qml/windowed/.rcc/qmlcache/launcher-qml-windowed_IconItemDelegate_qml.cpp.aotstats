[{"moduleFiles": [{"entries": [{"codegenSuccessfull": true, "column": 14, "durationMicroseconds": 141, "errorMessage": "", "functionName": "visible", "line": 15}, {"codegenSuccessfull": false, "column": 27, "durationMicroseconds": 173, "errorMessage": "Cannot access value for name display", "functionName": "text", "line": 17}, {"codegenSuccessfull": false, "column": 22, "durationMicroseconds": 23, "errorMessage": "Cannot retrieve a non-object type by ID: iconItemLabel", "functionName": "name", "line": 22}, {"codegenSuccessfull": true, "column": 20, "durationMicroseconds": 85, "errorMessage": "", "functionName": "dragType", "line": 27}, {"codegenSuccessfull": true, "column": 15, "durationMicroseconds": 79, "errorMessage": "", "functionName": "when", "line": 31}, {"codegenSuccessfull": true, "column": 21, "durationMicroseconds": 63, "errorMessage": "", "functionName": "target", "line": 35}, {"codegenSuccessfull": false, "column": 16, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type  for binding on x. You may want use ID-based grouped properties here.", "functionName": "x", "line": 36}, {"codegenSuccessfull": false, "column": 16, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type  for binding on y. You may want use ID-based grouped properties here.", "functionName": "y", "line": 37}, {"codegenSuccessfull": true, "column": 22, "durationMicroseconds": 96, "errorMessage": "", "functionName": "focusPolicy", "line": 43}, {"codegenSuccessfull": true, "column": 20, "durationMicroseconds": 127, "errorMessage": "", "functionName": "onClicked", "line": 133}, {"codegenSuccessfull": true, "column": 31, "durationMicroseconds": 101, "errorMessage": "", "functionName": "family", "line": 45}, {"codegenSuccessfull": true, "column": 27, "durationMicroseconds": 78, "errorMessage": "", "functionName": "fill", "line": 48}, {"codegenSuccessfull": false, "column": 23, "durationMicroseconds": 46, "errorMessage": "Cannot access value for name iconSource", "functionName": "name", "line": 60}, {"codegenSuccessfull": true, "column": 29, "durationMicroseconds": 310, "errorMessage": "", "functionName": "sourceSize", "line": 61}, {"codegenSuccessfull": false, "column": 26, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type Dtk::Gui::DDciIconPalette for binding on palette.", "functionName": "palette", "line": 62}, {"codegenSuccessfull": false, "column": 24, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type Dtk::Gui::DGuiApplicationHelper::ColorType for binding on theme.", "functionName": "theme", "line": 63}, {"codegenSuccessfull": false, "column": 43, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type  for binding on horizontalCenter.", "functionName": "horizontalCenter", "line": 59}, {"codegenSuccessfull": false, "column": 30, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type  for binding on visible.", "functionName": "visible", "line": 70}, {"codegenSuccessfull": true, "column": 33, "durationMicroseconds": 285, "errorMessage": "", "functionName": "sourceSize", "line": 71}, {"codegenSuccessfull": false, "column": 30, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type Dtk::Gui::DDciIconPalette for binding on palette.", "functionName": "palette", "line": 72}, {"codegenSuccessfull": false, "column": 28, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type Dtk::Gui::DGuiApplicationHelper::ColorType for binding on theme.", "functionName": "theme", "line": 73}, {"codegenSuccessfull": false, "column": 35, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type  for binding on left.", "functionName": "left", "line": 66}, {"codegenSuccessfull": false, "column": 37, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type  for binding on bottom.", "functionName": "bottom", "line": 67}, {"codegenSuccessfull": true, "column": 29, "durationMicroseconds": 73, "errorMessage": "", "functionName": "target", "line": 78}, {"codegenSuccessfull": true, "column": 38, "durationMicroseconds": 98, "errorMessage": "", "functionName": "acceptedButtons", "line": 79}, {"codegenSuccessfull": true, "column": 30, "durationMicroseconds": 73, "errorMessage": "", "functionName": "enabled", "line": 80}, {"codegenSuccessfull": false, "column": 38, "durationMicroseconds": 99, "errorMessage": "Cannot access value for name dndItem", "functionName": "onActiveChanged", "line": 82}, {"codegenSuccessfull": false, "column": 42, "durationMicroseconds": 38, "errorMessage": "Cannot access value for name font", "functionName": "singleRow", "line": 108}, {"codegenSuccessfull": false, "column": 23, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type  for binding on text.", "functionName": "text", "line": 110}, {"codegenSuccessfull": false, "column": 29, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type  for binding on textFormat.", "functionName": "textFormat", "line": 111}, {"codegenSuccessfull": false, "column": 24, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type  for binding on width.", "functionName": "width", "line": 112}, {"codegenSuccessfull": false, "column": 38, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type  for binding on horizontalAlignment.", "functionName": "horizontalAlignment", "line": 115}, {"codegenSuccessfull": false, "column": 36, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type  for binding on verticalAlignment.", "functionName": "verticalAlignment", "line": 116}, {"codegenSuccessfull": false, "column": 27, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type  for binding on wrapMode.", "functionName": "wrapMode", "line": 117}, {"codegenSuccessfull": false, "column": 24, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type  for binding on elide.", "functionName": "elide", "line": 118}, {"codegenSuccessfull": false, "column": 35, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type  for binding on maximumLineCount.", "functionName": "maximumLineCount", "line": 119}, {"codegenSuccessfull": false, "column": 23, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type  for binding on font.", "functionName": "font", "line": 120}, {"codegenSuccessfull": true, "column": 23, "durationMicroseconds": 78, "errorMessage": "", "functionName": "text", "line": 123}, {"codegenSuccessfull": false, "column": 26, "durationMicroseconds": 31, "errorMessage": "Cannot retrieve a non-object type by ID: iconItemLabel", "functionName": "visible", "line": 125}, {"codegenSuccessfull": true, "column": 21, "durationMicroseconds": 82, "errorMessage": "", "functionName": "button", "line": 128}, {"codegenSuccessfull": true, "column": 26, "durationMicroseconds": 106, "errorMessage": "", "functionName": "acceptedButtons", "line": 140}, {"codegenSuccessfull": true, "column": 19, "durationMicroseconds": 120, "errorMessage": "", "functionName": "onTapped", "line": 141}, {"codegenSuccessfull": true, "column": 26, "durationMicroseconds": 87, "errorMessage": "", "functionName": "acceptedButtons", "line": 147}, {"codegenSuccessfull": true, "column": 24, "durationMicroseconds": 65, "errorMessage": "", "functionName": "gesturePolicy", "line": 148}, {"codegenSuccessfull": true, "column": 19, "durationMicroseconds": 126, "errorMessage": "", "functionName": "onTapped", "line": 149}, {"codegenSuccessfull": true, "column": 26, "durationMicroseconds": 133, "errorMessage": "", "functionName": "onSpacePressed", "line": 154}, {"codegenSuccessfull": true, "column": 27, "durationMicroseconds": 135, "errorMessage": "", "functionName": "onReturnPressed", "line": 158}], "filepath": "/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/IconItemDelegate.qml"}], "moduleId": "org.deepin.launchpad.windowed(launcher-qml-windowed)"}]