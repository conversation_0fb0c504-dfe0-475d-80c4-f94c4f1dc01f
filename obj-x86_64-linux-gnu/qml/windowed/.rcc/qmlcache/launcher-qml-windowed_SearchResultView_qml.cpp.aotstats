[{"moduleFiles": [{"entries": [{"codegenSuccessfull": true, "column": 33, "durationMicroseconds": 134, "errorMessage": "", "functionName": "keyTab<PERSON>arget", "line": 18}, {"codegenSuccessfull": true, "column": 21, "durationMicroseconds": 113, "errorMessage": "", "functionName": "onFocusChanged", "line": 20}, {"codegenSuccessfull": true, "column": 21, "durationMicroseconds": 39, "errorMessage": "", "functionName": "onFocusChanged", "line": 20}, {"codegenSuccessfull": false, "column": 5, "durationMicroseconds": 88, "errorMessage": "Cannot load property itemClicked from GridViewContainer::currentItem with type QQuickItem.", "functionName": "launchCurrentItem", "line": 24}, {"codegenSuccessfull": true, "column": 5, "durationMicroseconds": 100, "errorMessage": "", "functionName": "positionViewAtBeginning", "line": 28}, {"codegenSuccessfull": true, "column": 18, "durationMicroseconds": 141, "errorMessage": "", "functionName": "visible", "line": 34}, {"codegenSuccessfull": false, "column": 19, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type  for binding on font.", "functionName": "font", "line": 38}, {"codegenSuccessfull": false, "column": 20, "durationMicroseconds": 149, "errorMessage": "Cannot access value for name SearchFilterProxyModel", "functionName": "model", "line": 43}, {"codegenSuccessfull": false, "column": 24, "durationMicroseconds": 53, "errorMessage": "Cannot access value for name searchResultViewContainer", "functionName": "width", "line": 45}, {"codegenSuccessfull": false, "column": 25, "durationMicroseconds": 33, "errorMessage": "Cannot access value for name searchResultViewContainer", "functionName": "height", "line": 46}, {"codegenSuccessfull": false, "column": 29, "durationMicroseconds": 283, "errorMessage": "Cannot access value for name iconName", "functionName": "iconSource", "line": 47}, {"codegenSuccessfull": false, "column": 32, "durationMicroseconds": 262, "errorMessage": "Cannot access value for name desktopId", "functionName": "onItemClicked", "line": 48}, {"codegenSuccessfull": false, "column": 34, "durationMicroseconds": 49, "errorMessage": "Cannot access value for name model", "functionName": "onMenuTriggered", "line": 51}, {"codegenSuccessfull": true, "column": 20, "durationMicroseconds": 128, "errorMessage": "", "functionName": "model", "line": 70}, {"codegenSuccessfull": true, "column": 31, "durationMicroseconds": 96, "errorMessage": "", "functionName": "activeFocusOnTab", "line": 72}, {"codegenSuccessfull": false, "column": 32, "durationMicroseconds": 50, "errorMessage": "Cannot access value for name nextKeyTabTarget", "functionName": "tab", "line": 61}, {"codegenSuccessfull": true, "column": 31, "durationMicroseconds": 121, "errorMessage": "", "functionName": "alignment", "line": 62}, {"codegenSuccessfull": true, "column": 37, "durationMicroseconds": 119, "errorMessage": "", "functionName": "preferredHeight", "line": 65}, {"codegenSuccessfull": true, "column": 36, "durationMicroseconds": 96, "errorMessage": "", "functionName": "preferredWidth", "line": 66}, {"codegenSuccessfull": false, "column": 26, "durationMicroseconds": 86, "errorMessage": "Cannot load property model from (component in /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/SearchResultView.qml)::parent with type QQuickItem.", "functionName": "visible", "line": 76}, {"codegenSuccessfull": false, "column": 25, "durationMicroseconds": 64, "errorMessage": "Cannot load property model from (component in /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/SearchResultView.qml)::parent with type QQuickItem.", "functionName": "active", "line": 77}, {"codegenSuccessfull": true, "column": 18, "durationMicroseconds": 196, "errorMessage": "", "functionName": "visible", "line": 89}, {"codegenSuccessfull": true, "column": 27, "durationMicroseconds": 77, "errorMessage": "", "functionName": "centerIn", "line": 88}, {"codegenSuccessfull": false, "column": 22, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type Dtk::Gui::DDciIconPalette for binding on palette.", "functionName": "palette", "line": 97}, {"codegenSuccessfull": false, "column": 20, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type Dtk::Gui::DGuiApplicationHelper::ColorType for binding on theme.", "functionName": "theme", "line": 98}, {"codegenSuccessfull": true, "column": 31, "durationMicroseconds": 140, "errorMessage": "", "functionName": "alignment", "line": 91}, {"codegenSuccessfull": false, "column": 25, "durationMicroseconds": 46, "errorMessage": "Cannot access value for name width", "functionName": "height", "line": 94}, {"codegenSuccessfull": true, "column": 31, "durationMicroseconds": 114, "errorMessage": "", "functionName": "alignment", "line": 102}], "filepath": "/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/SearchResultView.qml"}], "moduleId": "org.deepin.launchpad.windowed(launcher-qml-windowed)"}]