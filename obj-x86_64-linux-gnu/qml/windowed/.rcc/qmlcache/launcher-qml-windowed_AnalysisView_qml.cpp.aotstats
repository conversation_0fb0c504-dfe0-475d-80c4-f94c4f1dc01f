[{"moduleFiles": [{"entries": [{"codegenSuccessfull": true, "column": 33, "durationMicroseconds": 184, "errorMessage": "", "functionName": "keyTab<PERSON>arget", "line": 17}, {"codegenSuccessfull": true, "column": 21, "durationMicroseconds": 130, "errorMessage": "", "functionName": "onFocusChanged", "line": 20}, {"codegenSuccessfull": true, "column": 21, "durationMicroseconds": 51, "errorMessage": "", "functionName": "onFocusChanged", "line": 20}, {"codegenSuccessfull": true, "column": 5, "durationMicroseconds": 208, "errorMessage": "", "functionName": "positionViewAtBeginning", "line": 24}, {"codegenSuccessfull": false, "column": 26, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type  for binding on sourceModel.", "functionName": "sourceModel", "line": 34}, {"codegenSuccessfull": false, "column": 28, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type  for binding on desktopIdRole.", "functionName": "desktopIdRole", "line": 35}, {"codegenSuccessfull": false, "column": 32, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type  for binding on launchedTimesRole.", "functionName": "launchedTimesRole", "line": 36}, {"codegenSuccessfull": false, "column": 35, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type  for binding on lastLaunchedTimeRole.", "functionName": "lastLaunchedTimeRole", "line": 37}, {"codegenSuccessfull": false, "column": 20, "durationMicroseconds": 33, "errorMessage": "Cannot retrieve a non-object type by ID: freqUsedModel", "functionName": "model", "line": 42}, {"codegenSuccessfull": true, "column": 22, "durationMicroseconds": 156, "errorMessage": "", "functionName": "visible", "line": 43}, {"codegenSuccessfull": true, "column": 23, "durationMicroseconds": 195, "errorMessage": "", "functionName": "maxCount", "line": 44}, {"codegenSuccessfull": true, "column": 32, "durationMicroseconds": 304, "errorMessage": "", "functionName": "nextKeyTabTarget", "line": 45}, {"codegenSuccessfull": true, "column": 22, "durationMicroseconds": 153, "errorMessage": "", "functionName": "visible", "line": 55}, {"codegenSuccessfull": true, "column": 31, "durationMicroseconds": 104, "errorMessage": "", "functionName": "nextKeyTabTarget", "line": 57}, {"codegenSuccessfull": false, "column": 30, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type  for binding on sourceModel.", "functionName": "sourceModel", "line": 51}, {"codegenSuccessfull": false, "column": 36, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type  for binding on installedTimeRole.", "functionName": "installedTimeRole", "line": 52}, {"codegenSuccessfull": false, "column": 39, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type  for binding on lastLaunchedTimeRole.", "functionName": "lastLaunchedTimeRole", "line": 53}, {"codegenSuccessfull": false, "column": 31, "durationMicroseconds": 260, "errorMessage": "Cannot access value for name Helper", "functionName": "<PERSON><PERSON><PERSON><PERSON>", "line": 56}], "filepath": "/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/AnalysisView.qml"}], "moduleId": "org.deepin.launchpad.windowed(launcher-qml-windowed)"}]