[{"moduleFiles": [{"entries": [{"codegenSuccessfull": true, "column": 18, "durationMicroseconds": 320, "errorMessage": "", "functionName": "closePolicy", "line": 19}, {"codegenSuccessfull": true, "column": 12, "durationMicroseconds": 189, "errorMessage": "", "functionName": "width", "line": 31}, {"codegenSuccessfull": true, "column": 13, "durationMicroseconds": 190, "errorMessage": "", "functionName": "height", "line": 32}, {"codegenSuccessfull": false, "column": 5, "durationMicroseconds": 0, "errorMessage": "Functions without type annotations won't be compiled", "functionName": "setCurrentCategory", "line": 34}, {"codegenSuccessfull": false, "column": 20, "durationMicroseconds": 50, "errorMessage": "Cannot access value for name alphabetCategoryContainer", "functionName": "width", "line": 47}, {"codegenSuccessfull": false, "column": 21, "durationMicroseconds": 40, "errorMessage": "Cannot access value for name alphabetCategoryContainer", "functionName": "height", "line": 48}, {"codegenSuccessfull": false, "column": 19, "durationMicroseconds": 325, "errorMessage": "Cannot access value for name modelData", "functionName": "text", "line": 49}, {"codegenSuccessfull": false, "column": 24, "durationMicroseconds": 255, "errorMessage": "Cannot access value for name DStyle", "functionName": "textColor", "line": 50}, {"codegenSuccessfull": true, "column": 26, "durationMicroseconds": 134, "errorMessage": "", "functionName": "focusPolicy", "line": 51}, {"codegenSuccessfull": false, "column": 24, "durationMicroseconds": 300, "errorMessage": "Cannot access value for name modelData", "functionName": "onClicked", "line": 52}, {"codegenSuccessfull": true, "column": 25, "durationMicroseconds": 84, "errorMessage": "", "functionName": "button", "line": 57}, {"codegenSuccessfull": false, "column": 24, "durationMicroseconds": 44, "errorMessage": "Cannot access value for name root", "functionName": "width", "line": 59}, {"codegenSuccessfull": false, "column": 25, "durationMicroseconds": 38, "errorMessage": "Cannot access value for name root", "functionName": "height", "line": 60}, {"codegenSuccessfull": true, "column": 25, "durationMicroseconds": 154, "errorMessage": "", "functionName": "radius", "line": 63}, {"codegenSuccessfull": true, "column": 25, "durationMicroseconds": 74, "errorMessage": "", "functionName": "color1", "line": 79}, {"codegenSuccessfull": true, "column": 25, "durationMicroseconds": 76, "errorMessage": "", "functionName": "color2", "line": 80}, {"codegenSuccessfull": true, "column": 35, "durationMicroseconds": 71, "errorMessage": "", "functionName": "centerIn", "line": 58}, {"codegenSuccessfull": false, "column": 33, "durationMicroseconds": 336, "errorMessage": "Cannot generate efficient code for internal conversion with incompatible or ambiguous types: QVariant -> Dtk::Quick::DColor stored as QVariant", "functionName": "common", "line": 67}, {"codegenSuccessfull": false, "column": 34, "durationMicroseconds": 308, "errorMessage": "Cannot generate efficient code for internal conversion with incompatible or ambiguous types: QVariant -> Dtk::Quick::DColor stored as QVariant", "functionName": "crystal", "line": 68}, {"codegenSuccessfull": false, "column": 33, "durationMicroseconds": 320, "errorMessage": "Cannot generate efficient code for internal conversion with incompatible or ambiguous types: QVariant -> Dtk::Quick::DColor stored as QVariant", "functionName": "common", "line": 71}, {"codegenSuccessfull": false, "column": 34, "durationMicroseconds": 311, "errorMessage": "Cannot generate efficient code for internal conversion with incompatible or ambiguous types: QVariant -> Dtk::Quick::DColor stored as QVariant", "functionName": "crystal", "line": 72}, {"codegenSuccessfull": false, "column": 33, "durationMicroseconds": 521, "errorMessage": "Cannot generate efficient code for internal conversion with incompatible or ambiguous types: QVariant -> Dtk::Quick::DColor stored as QVariant", "functionName": "common", "line": 75}, {"codegenSuccessfull": false, "column": 34, "durationMicroseconds": 491, "errorMessage": "Cannot generate efficient code for internal conversion with incompatible or ambiguous types: QVariant -> Dtk::Quick::DColor stored as QVariant", "functionName": "crystal", "line": 76}, {"codegenSuccessfull": true, "column": 16, "durationMicroseconds": 104, "errorMessage": "", "functionName": "model", "line": 90}, {"codegenSuccessfull": true, "column": 18, "durationMicroseconds": 114, "errorMessage": "", "functionName": "columns", "line": 91}, {"codegenSuccessfull": false, "column": 15, "durationMicroseconds": 33, "errorMessage": "Cannot load property count from (component in /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/AlphabetCategoryPopup.qml)::model with type QVariant.", "functionName": "rows", "line": 92}, {"codegenSuccessfull": true, "column": 20, "durationMicroseconds": 113, "errorMessage": "", "functionName": "cellWidth", "line": 93}, {"codegenSuccessfull": true, "column": 21, "durationMicroseconds": 108, "errorMessage": "", "functionName": "cellHeight", "line": 94}, {"codegenSuccessfull": true, "column": 25, "durationMicroseconds": 99, "errorMessage": "", "functionName": "paddingColumns", "line": 95}, {"codegenSuccessfull": true, "column": 22, "durationMicroseconds": 98, "errorMessage": "", "functionName": "paddingRows", "line": 96}, {"codegenSuccessfull": true, "column": 26, "durationMicroseconds": 75, "errorMessage": "", "functionName": "activeFocusOnTab", "line": 109}, {"codegenSuccessfull": true, "column": 27, "durationMicroseconds": 77, "errorMessage": "", "functionName": "centerIn", "line": 88}, {"codegenSuccessfull": false, "column": 24, "durationMicroseconds": 52, "errorMessage": "Cannot access value for name root", "functionName": "width", "line": 101}, {"codegenSuccessfull": false, "column": 25, "durationMicroseconds": 43, "errorMessage": "Cannot access value for name root", "functionName": "height", "line": 102}, {"codegenSuccessfull": false, "column": 25, "durationMicroseconds": 33, "errorMessage": "Cannot access value for name root", "functionName": "radius", "line": 103}, {"codegenSuccessfull": false, "column": 24, "durationMicroseconds": 81, "errorMessage": "Cannot use shadowable base type for further lookups: QQuickItem::palette with type QQuickPalette (stored as QVariant)", "functionName": "color", "line": 104}, {"codegenSuccessfull": false, "column": 26, "durationMicroseconds": 36, "errorMessage": "Cannot access value for name alphabetCategoryContainer", "functionName": "visible", "line": 105}, {"codegenSuccessfull": true, "column": 35, "durationMicroseconds": 79, "errorMessage": "", "functionName": "centerIn", "line": 100}, {"codegenSuccessfull": true, "column": 16, "durationMicroseconds": 116, "errorMessage": "", "functionName": "focus", "line": 113}, {"codegenSuccessfull": true, "column": 25, "durationMicroseconds": 1282, "errorMessage": "", "functionName": "onPressed", "line": 114}, {"codegenSuccessfull": true, "column": 25, "durationMicroseconds": 67, "errorMessage": "", "functionName": "onPressed", "line": 114}, {"codegenSuccessfull": false, "column": 17, "durationMicroseconds": 244, "errorMessage": "Cannot access value for name DStyle", "functionName": "radius", "line": 125}], "filepath": "/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/AlphabetCategoryPopup.qml"}], "moduleId": "org.deepin.launchpad.windowed(launcher-qml-windowed)"}]