[{"moduleFiles": [{"entries": [{"codegenSuccessfull": true, "column": 18, "durationMicroseconds": 360, "errorMessage": "", "functionName": "closePolicy", "line": 19}, {"codegenSuccessfull": true, "column": 12, "durationMicroseconds": 223, "errorMessage": "", "functionName": "width", "line": 31}, {"codegenSuccessfull": true, "column": 13, "durationMicroseconds": 189, "errorMessage": "", "functionName": "height", "line": 32}, {"codegenSuccessfull": false, "column": 5, "durationMicroseconds": 0, "errorMessage": "Functions without type annotations won't be compiled", "functionName": "setCurrentCategory", "line": 34}, {"codegenSuccessfull": false, "column": 20, "durationMicroseconds": 62, "errorMessage": "Cannot access value for name alphabetCategoryContainer", "functionName": "width", "line": 47}, {"codegenSuccessfull": false, "column": 21, "durationMicroseconds": 43, "errorMessage": "Cannot access value for name alphabetCategoryContainer", "functionName": "height", "line": 48}, {"codegenSuccessfull": false, "column": 19, "durationMicroseconds": 360, "errorMessage": "Cannot access value for name modelData", "functionName": "text", "line": 49}, {"codegenSuccessfull": false, "column": 24, "durationMicroseconds": 272, "errorMessage": "Cannot access value for name DStyle", "functionName": "textColor", "line": 50}, {"codegenSuccessfull": true, "column": 26, "durationMicroseconds": 152, "errorMessage": "", "functionName": "focusPolicy", "line": 51}, {"codegenSuccessfull": false, "column": 24, "durationMicroseconds": 335, "errorMessage": "Cannot access value for name modelData", "functionName": "onClicked", "line": 52}, {"codegenSuccessfull": true, "column": 25, "durationMicroseconds": 92, "errorMessage": "", "functionName": "button", "line": 57}, {"codegenSuccessfull": false, "column": 24, "durationMicroseconds": 53, "errorMessage": "Cannot access value for name root", "functionName": "width", "line": 59}, {"codegenSuccessfull": false, "column": 25, "durationMicroseconds": 48, "errorMessage": "Cannot access value for name root", "functionName": "height", "line": 60}, {"codegenSuccessfull": true, "column": 25, "durationMicroseconds": 160, "errorMessage": "", "functionName": "radius", "line": 63}, {"codegenSuccessfull": true, "column": 25, "durationMicroseconds": 76, "errorMessage": "", "functionName": "color1", "line": 79}, {"codegenSuccessfull": true, "column": 25, "durationMicroseconds": 68, "errorMessage": "", "functionName": "color2", "line": 80}, {"codegenSuccessfull": true, "column": 35, "durationMicroseconds": 71, "errorMessage": "", "functionName": "centerIn", "line": 58}, {"codegenSuccessfull": false, "column": 33, "durationMicroseconds": 353, "errorMessage": "Cannot generate efficient code for internal conversion with incompatible or ambiguous types: QVariant -> Dtk::Quick::DColor stored as QVariant", "functionName": "common", "line": 67}, {"codegenSuccessfull": false, "column": 34, "durationMicroseconds": 323, "errorMessage": "Cannot generate efficient code for internal conversion with incompatible or ambiguous types: QVariant -> Dtk::Quick::DColor stored as QVariant", "functionName": "crystal", "line": 68}, {"codegenSuccessfull": false, "column": 33, "durationMicroseconds": 317, "errorMessage": "Cannot generate efficient code for internal conversion with incompatible or ambiguous types: QVariant -> Dtk::Quick::DColor stored as QVariant", "functionName": "common", "line": 71}, {"codegenSuccessfull": false, "column": 34, "durationMicroseconds": 308, "errorMessage": "Cannot generate efficient code for internal conversion with incompatible or ambiguous types: QVariant -> Dtk::Quick::DColor stored as QVariant", "functionName": "crystal", "line": 72}, {"codegenSuccessfull": false, "column": 33, "durationMicroseconds": 527, "errorMessage": "Cannot generate efficient code for internal conversion with incompatible or ambiguous types: QVariant -> Dtk::Quick::DColor stored as QVariant", "functionName": "common", "line": 75}, {"codegenSuccessfull": false, "column": 34, "durationMicroseconds": 528, "errorMessage": "Cannot generate efficient code for internal conversion with incompatible or ambiguous types: QVariant -> Dtk::Quick::DColor stored as QVariant", "functionName": "crystal", "line": 76}, {"codegenSuccessfull": true, "column": 16, "durationMicroseconds": 104, "errorMessage": "", "functionName": "model", "line": 90}, {"codegenSuccessfull": true, "column": 18, "durationMicroseconds": 106, "errorMessage": "", "functionName": "columns", "line": 91}, {"codegenSuccessfull": false, "column": 15, "durationMicroseconds": 32, "errorMessage": "Cannot load property count from (component in /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/AlphabetCategoryPopup.qml)::model with type QVariant.", "functionName": "rows", "line": 92}, {"codegenSuccessfull": true, "column": 20, "durationMicroseconds": 127, "errorMessage": "", "functionName": "cellWidth", "line": 93}, {"codegenSuccessfull": true, "column": 21, "durationMicroseconds": 122, "errorMessage": "", "functionName": "cellHeight", "line": 94}, {"codegenSuccessfull": true, "column": 25, "durationMicroseconds": 95, "errorMessage": "", "functionName": "paddingColumns", "line": 95}, {"codegenSuccessfull": true, "column": 22, "durationMicroseconds": 104, "errorMessage": "", "functionName": "paddingRows", "line": 96}, {"codegenSuccessfull": true, "column": 26, "durationMicroseconds": 73, "errorMessage": "", "functionName": "activeFocusOnTab", "line": 109}, {"codegenSuccessfull": true, "column": 27, "durationMicroseconds": 78, "errorMessage": "", "functionName": "centerIn", "line": 88}, {"codegenSuccessfull": false, "column": 24, "durationMicroseconds": 49, "errorMessage": "Cannot access value for name root", "functionName": "width", "line": 101}, {"codegenSuccessfull": false, "column": 25, "durationMicroseconds": 33, "errorMessage": "Cannot access value for name root", "functionName": "height", "line": 102}, {"codegenSuccessfull": false, "column": 25, "durationMicroseconds": 34, "errorMessage": "Cannot access value for name root", "functionName": "radius", "line": 103}, {"codegenSuccessfull": false, "column": 24, "durationMicroseconds": 85, "errorMessage": "Cannot use shadowable base type for further lookups: QQuickItem::palette with type QQuickPalette (stored as QVariant)", "functionName": "color", "line": 104}, {"codegenSuccessfull": false, "column": 26, "durationMicroseconds": 52, "errorMessage": "Cannot access value for name alphabetCategoryContainer", "functionName": "visible", "line": 105}, {"codegenSuccessfull": true, "column": 35, "durationMicroseconds": 78, "errorMessage": "", "functionName": "centerIn", "line": 100}, {"codegenSuccessfull": true, "column": 16, "durationMicroseconds": 136, "errorMessage": "", "functionName": "focus", "line": 113}, {"codegenSuccessfull": true, "column": 25, "durationMicroseconds": 1260, "errorMessage": "", "functionName": "onPressed", "line": 114}, {"codegenSuccessfull": true, "column": 25, "durationMicroseconds": 69, "errorMessage": "", "functionName": "onPressed", "line": 114}, {"codegenSuccessfull": false, "column": 17, "durationMicroseconds": 265, "errorMessage": "Cannot access value for name DStyle", "functionName": "radius", "line": 125}], "filepath": "/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/AlphabetCategoryPopup.qml"}], "moduleId": "org.deepin.launchpad.windowed(launcher-qml-windowed)"}]