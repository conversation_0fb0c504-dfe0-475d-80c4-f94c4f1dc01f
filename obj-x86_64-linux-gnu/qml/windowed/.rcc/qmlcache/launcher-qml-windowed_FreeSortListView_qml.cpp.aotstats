[{"moduleFiles": [{"entries": [{"codegenSuccessfull": true, "column": 33, "durationMicroseconds": 166, "errorMessage": "", "functionName": "keyTab<PERSON>arget", "line": 19}, {"codegenSuccessfull": true, "column": 21, "durationMicroseconds": 133, "errorMessage": "", "functionName": "onFocusChanged", "line": 34}, {"codegenSuccessfull": true, "column": 21, "durationMicroseconds": 44, "errorMessage": "", "functionName": "onFocusChanged", "line": 34}, {"codegenSuccessfull": true, "column": 5, "durationMicroseconds": 124, "errorMessage": "", "functionName": "positionViewAtBeginning", "line": 38}, {"codegenSuccessfull": true, "column": 22, "durationMicroseconds": 164, "errorMessage": "", "functionName": "onTriggered", "line": 25}, {"codegenSuccessfull": true, "column": 28, "durationMicroseconds": 131, "errorMessage": "", "functionName": "onCompleted", "line": 30}, {"codegenSuccessfull": true, "column": 15, "durationMicroseconds": 85, "errorMessage": "", "functionName": "move", "line": 51}, {"codegenSuccessfull": true, "column": 24, "durationMicroseconds": 72, "errorMessage": "", "functionName": "moveDisplaced", "line": 52}, {"codegenSuccessfull": true, "column": 31, "durationMicroseconds": 521, "errorMessage": "", "functionName": "onActiveFocusChanged", "line": 72}, {"codegenSuccessfull": true, "column": 23, "durationMicroseconds": 86, "errorMessage": "", "functionName": "fill", "line": 44}, {"codegenSuccessfull": true, "column": 22, "durationMicroseconds": 104, "errorMessage": "", "functionName": "enabled", "line": 48}, {"codegenSuccessfull": false, "column": 24, "durationMicroseconds": 71, "errorMessage": "Cannot use shadowable base type for further lookups: QQuickItem::palette with type QQuickPalette (stored as QVariant)", "functionName": "color", "line": 62}, {"codegenSuccessfull": false, "column": 26, "durationMicroseconds": 47, "errorMessage": "Cannot access value for name listView", "functionName": "visible", "line": 63}, {"codegenSuccessfull": true, "column": 27, "durationMicroseconds": 74, "errorMessage": "", "functionName": "fill", "line": 58}, {"codegenSuccessfull": true, "column": 26, "durationMicroseconds": 74, "errorMessage": "", "functionName": "onTriggered", "line": 89}, {"codegenSuccessfull": true, "column": 26, "durationMicroseconds": 50, "errorMessage": "", "functionName": "onTriggered", "line": 89}, {"codegenSuccessfull": true, "column": 13, "durationMicroseconds": 964, "errorMessage": "", "functionName": "scroll", "line": 93}, {"codegenSuccessfull": false, "column": 13, "durationMicroseconds": 0, "errorMessage": "Functions without type annotations won't be compiled", "functionName": "startScroll", "line": 99}, {"codegenSuccessfull": true, "column": 13, "durationMicroseconds": 264, "errorMessage": "", "functionName": "stopScroll", "line": 104}, {"codegenSuccessfull": false, "column": 23, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type  for binding on sortRole.", "functionName": "sortRole", "line": 120}, {"codegenSuccessfull": false, "column": 30, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type  for binding on sourceModel.", "functionName": "sourceModel", "line": 116}, {"codegenSuccessfull": false, "column": 36, "durationMicroseconds": 35, "errorMessage": "Cannot retrieve a non-object type by ID: freeSortProxyModel", "functionName": "onCompleted", "line": 121}, {"codegenSuccessfull": false, "column": 20, "durationMicroseconds": 59, "errorMessage": "Cannot access value for name listView", "functionName": "width", "line": 126}, {"codegenSuccessfull": true, "column": 21, "durationMicroseconds": 121, "errorMessage": "", "functionName": "height", "line": 127}, {"codegenSuccessfull": true, "column": 19, "durationMicroseconds": 149, "errorMessage": "", "functionName": "keys", "line": 128}, {"codegenSuccessfull": false, "column": 13, "durationMicroseconds": 0, "errorMessage": "Functions without type annotations won't be compiled", "functionName": "scrollViewWhenNeeded", "line": 158}, {"codegenSuccessfull": false, "column": 13, "durationMicroseconds": 0, "errorMessage": "Functions without type annotations won't be compiled", "functionName": "dropPositionCheck", "line": 169}, {"codegenSuccessfull": false, "column": 13, "durationMicroseconds": 275, "errorMessage": "Cannot access value for name itemType", "functionName": "launchItem", "line": 186}, {"codegenSuccessfull": false, "column": 32, "durationMicroseconds": 216, "errorMessage": "Cannot access value for name desktopId", "functionName": "onPositionChanged", "line": 199}, {"codegenSuccessfull": true, "column": 32, "durationMicroseconds": 72, "errorMessage": "", "functionName": "onPositionChanged", "line": 199}, {"codegenSuccessfull": false, "column": 24, "durationMicroseconds": 310, "errorMessage": "Cannot access value for name folderGridViewPopup", "functionName": "onEntered", "line": 214}, {"codegenSuccessfull": true, "column": 24, "durationMicroseconds": 69, "errorMessage": "", "functionName": "onEntered", "line": 214}, {"codegenSuccessfull": false, "column": 23, "durationMicroseconds": 85, "errorMessage": "Cannot access value for name listViewDragScroller", "functionName": "onExited", "line": 221}, {"codegenSuccessfull": false, "column": 24, "durationMicroseconds": 167, "errorMessage": "Cannot access value for name model", "functionName": "onDropped", "line": 227}, {"codegenSuccessfull": true, "column": 24, "durationMicroseconds": 63, "errorMessage": "", "functionName": "onDropped", "line": 227}, {"codegenSuccessfull": true, "column": 35, "durationMicroseconds": 155, "errorMessage": "", "functionName": "delayRemove", "line": 129}, {"codegenSuccessfull": false, "column": 30, "durationMicroseconds": 106, "errorMessage": "Cannot load property op from (component in /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/FreeSortListView.qml)::parent with type QObject.", "functionName": "onTriggered", "line": 140}, {"codegenSuccessfull": true, "column": 30, "durationMicroseconds": 53, "errorMessage": "", "functionName": "onTriggered", "line": 140}, {"codegenSuccessfull": false, "column": 17, "durationMicroseconds": 0, "errorMessage": "Functions without type annotations won't be compiled", "functionName": "startTimer", "line": 147}, {"codegenSuccessfull": true, "column": 17, "durationMicroseconds": 100, "errorMessage": "", "functionName": "stopTimer", "line": 152}, {"codegenSuccessfull": false, "column": 53, "durationMicroseconds": 324, "errorMessage": "Cannot access value for name Helper", "functionName": "backgroundP<PERSON><PERSON>", "line": 239}, {"codegenSuccessfull": false, "column": 26, "durationMicroseconds": 44, "errorMessage": "Cannot access value for name showDropIndicator", "functionName": "visible", "line": 240}, {"codegenSuccessfull": true, "column": 28, "durationMicroseconds": 124, "errorMessage": "", "functionName": "width", "line": 242}, {"codegenSuccessfull": true, "column": 29, "durationMicroseconds": 117, "errorMessage": "", "functionName": "height", "line": 243}, {"codegenSuccessfull": false, "column": 28, "durationMicroseconds": 65, "errorMessage": "Cannot load property backgroundPalette from Dtk::Quick::DQuickControlColorSelector.", "functionName": "color", "line": 245}, {"codegenSuccessfull": false, "column": 23, "durationMicroseconds": 107, "errorMessage": "Cannot access value for name model", "functionName": "text", "line": 251}, {"codegenSuccessfull": false, "column": 23, "durationMicroseconds": 29, "errorMessage": "Cannot load property fontManager from Dtk::Quick::DQMLGlobalObject.", "functionName": "font", "line": 288}, {"codegenSuccessfull": false, "column": 45, "durationMicroseconds": 382, "errorMessage": "Cannot access value for name DStyle", "functionName": "textColor", "line": 290}, {"codegenSuccessfull": true, "column": 26, "durationMicroseconds": 145, "errorMessage": "", "functionName": "visible", "line": 292}, {"codegenSuccessfull": false, "column": 28, "durationMicroseconds": 366, "errorMessage": "Cannot access value for name itemType", "functionName": "name", "line": 253}, {"codegenSuccessfull": true, "column": 31, "durationMicroseconds": 147, "errorMessage": "", "functionName": "mode", "line": 254}, {"codegenSuccessfull": false, "column": 30, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type  for binding on visible.", "functionName": "visible", "line": 259}, {"codegenSuccessfull": true, "column": 33, "durationMicroseconds": 351, "errorMessage": "", "functionName": "sourceSize", "line": 260}, {"codegenSuccessfull": false, "column": 30, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type Dtk::Gui::DDciIconPalette for binding on palette.", "functionName": "palette", "line": 261}, {"codegenSuccessfull": false, "column": 28, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type Dtk::Gui::DGuiApplicationHelper::ColorType for binding on theme.", "functionName": "theme", "line": 262}, {"codegenSuccessfull": false, "column": 35, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type  for binding on left.", "functionName": "left", "line": 256}, {"codegenSuccessfull": false, "column": 37, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type  for binding on bottom.", "functionName": "bottom", "line": 257}, {"codegenSuccessfull": true, "column": 27, "durationMicroseconds": 164, "errorMessage": "", "functionName": "left", "line": 266}, {"codegenSuccessfull": true, "column": 28, "durationMicroseconds": 181, "errorMessage": "", "functionName": "right", "line": 267}, {"codegenSuccessfull": false, "column": 28, "durationMicroseconds": 33, "errorMessage": "Cannot load property themeType from Dtk::Quick::DQMLGlobalObject.", "functionName": "color", "line": 275}, {"codegenSuccessfull": false, "column": 30, "durationMicroseconds": 65, "errorMessage": "Cannot access value for name model", "functionName": "visible", "line": 279}, {"codegenSuccessfull": false, "column": 35, "durationMicroseconds": 50, "errorMessage": "Cannot load property themeType from Dtk::Quick::DQMLGlobalObject.", "functionName": "color", "line": 277}, {"codegenSuccessfull": true, "column": 32, "durationMicroseconds": 167, "errorMessage": "", "functionName": "right", "line": 281}, {"codegenSuccessfull": true, "column": 41, "durationMicroseconds": 159, "errorMessage": "", "functionName": "verticalCenter", "line": 282}, {"codegenSuccessfull": false, "column": 37, "durationMicroseconds": 64, "errorMessage": "Cannot load property textColor from Dtk::Quick::DQuickControlColorSelector.", "functionName": "windowText", "line": 291}, {"codegenSuccessfull": true, "column": 31, "durationMicroseconds": 86, "errorMessage": "", "functionName": "text", "line": 294}, {"codegenSuccessfull": true, "column": 34, "durationMicroseconds": 422, "errorMessage": "", "functionName": "visible", "line": 296}, {"codegenSuccessfull": true, "column": 32, "durationMicroseconds": 117, "errorMessage": "", "functionName": "dragType", "line": 300}, {"codegenSuccessfull": true, "column": 30, "durationMicroseconds": 175, "errorMessage": "", "functionName": "active", "line": 301}, {"codegenSuccessfull": false, "column": 32, "durationMicroseconds": 411, "errorMessage": "Cannot access value for name Helper", "functionName": "mimeData", "line": 302}, {"codegenSuccessfull": false, "column": 39, "durationMicroseconds": 127, "errorMessage": "Cannot access value for name listViewDragScroller", "functionName": "onActiveChanged", "line": 303}, {"codegenSuccessfull": true, "column": 39, "durationMicroseconds": 56, "errorMessage": "", "functionName": "onActiveChanged", "line": 303}, {"codegenSuccessfull": true, "column": 33, "durationMicroseconds": 149, "errorMessage": "", "functionName": "x", "line": 298}, {"codegenSuccessfull": true, "column": 33, "durationMicroseconds": 162, "errorMessage": "", "functionName": "y", "line": 299}, {"codegenSuccessfull": false, "column": 36, "durationMicroseconds": 195, "errorMessage": "Cannot access value for name DStyle", "functionName": "implicitWidth", "line": 311}, {"codegenSuccessfull": false, "column": 37, "durationMicroseconds": 159, "errorMessage": "Cannot access value for name Helper", "functionName": "implicitHeight", "line": 312}, {"codegenSuccessfull": true, "column": 29, "durationMicroseconds": 112, "errorMessage": "", "functionName": "button", "line": 313}, {"codegenSuccessfull": true, "column": 38, "durationMicroseconds": 299, "errorMessage": "", "functionName": "acceptedButtons", "line": 320}, {"codegenSuccessfull": false, "column": 32, "durationMicroseconds": 651, "errorMessage": "Cannot generate efficient code for LoadClosure", "functionName": "onPressed", "line": 323}, {"codegenSuccessfull": true, "column": 32, "durationMicroseconds": 63, "errorMessage": "", "functionName": "onPressed", "line": 323}, {"codegenSuccessfull": false, "column": 32, "durationMicroseconds": 169, "errorMessage": "Cannot access value for name model", "functionName": "onClicked", "line": 335}, {"codegenSuccessfull": true, "column": 32, "durationMicroseconds": 62, "errorMessage": "", "functionName": "onClicked", "line": 335}, {"codegenSuccessfull": true, "column": 35, "durationMicroseconds": 100, "errorMessage": "", "functionName": "fill", "line": 318}, {"codegenSuccessfull": true, "column": 34, "durationMicroseconds": 113, "errorMessage": "", "functionName": "target", "line": 321}, {"codegenSuccessfull": true, "column": 35, "durationMicroseconds": 99, "errorMessage": "", "functionName": "onReturnPressed", "line": 348}, {"codegenSuccessfull": true, "column": 34, "durationMicroseconds": 88, "errorMessage": "", "functionName": "onSpacePressed", "line": 350}], "filepath": "/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/FreeSortListView.qml"}], "moduleId": "org.deepin.launchpad.windowed(launcher-qml-windowed)"}]