[{"moduleFiles": [{"entries": [{"codegenSuccessfull": true, "column": 33, "durationMicroseconds": 143, "errorMessage": "", "functionName": "keyTab<PERSON>arget", "line": 19}, {"codegenSuccessfull": true, "column": 21, "durationMicroseconds": 123, "errorMessage": "", "functionName": "onFocusChanged", "line": 34}, {"codegenSuccessfull": true, "column": 21, "durationMicroseconds": 47, "errorMessage": "", "functionName": "onFocusChanged", "line": 34}, {"codegenSuccessfull": true, "column": 5, "durationMicroseconds": 115, "errorMessage": "", "functionName": "positionViewAtBeginning", "line": 38}, {"codegenSuccessfull": true, "column": 22, "durationMicroseconds": 137, "errorMessage": "", "functionName": "onTriggered", "line": 25}, {"codegenSuccessfull": true, "column": 28, "durationMicroseconds": 117, "errorMessage": "", "functionName": "onCompleted", "line": 30}, {"codegenSuccessfull": true, "column": 15, "durationMicroseconds": 77, "errorMessage": "", "functionName": "move", "line": 51}, {"codegenSuccessfull": true, "column": 24, "durationMicroseconds": 63, "errorMessage": "", "functionName": "moveDisplaced", "line": 52}, {"codegenSuccessfull": true, "column": 31, "durationMicroseconds": 464, "errorMessage": "", "functionName": "onActiveFocusChanged", "line": 72}, {"codegenSuccessfull": true, "column": 23, "durationMicroseconds": 95, "errorMessage": "", "functionName": "fill", "line": 44}, {"codegenSuccessfull": true, "column": 22, "durationMicroseconds": 101, "errorMessage": "", "functionName": "enabled", "line": 48}, {"codegenSuccessfull": false, "column": 24, "durationMicroseconds": 65, "errorMessage": "Cannot use shadowable base type for further lookups: QQuickItem::palette with type QQuickPalette (stored as QVariant)", "functionName": "color", "line": 62}, {"codegenSuccessfull": false, "column": 26, "durationMicroseconds": 42, "errorMessage": "Cannot access value for name listView", "functionName": "visible", "line": 63}, {"codegenSuccessfull": true, "column": 27, "durationMicroseconds": 68, "errorMessage": "", "functionName": "fill", "line": 58}, {"codegenSuccessfull": true, "column": 26, "durationMicroseconds": 68, "errorMessage": "", "functionName": "onTriggered", "line": 89}, {"codegenSuccessfull": true, "column": 26, "durationMicroseconds": 46, "errorMessage": "", "functionName": "onTriggered", "line": 89}, {"codegenSuccessfull": true, "column": 13, "durationMicroseconds": 877, "errorMessage": "", "functionName": "scroll", "line": 93}, {"codegenSuccessfull": false, "column": 13, "durationMicroseconds": 0, "errorMessage": "Functions without type annotations won't be compiled", "functionName": "startScroll", "line": 99}, {"codegenSuccessfull": true, "column": 13, "durationMicroseconds": 258, "errorMessage": "", "functionName": "stopScroll", "line": 104}, {"codegenSuccessfull": false, "column": 23, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type  for binding on sortRole.", "functionName": "sortRole", "line": 120}, {"codegenSuccessfull": false, "column": 30, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type  for binding on sourceModel.", "functionName": "sourceModel", "line": 116}, {"codegenSuccessfull": false, "column": 36, "durationMicroseconds": 31, "errorMessage": "Cannot retrieve a non-object type by ID: freeSortProxyModel", "functionName": "onCompleted", "line": 121}, {"codegenSuccessfull": false, "column": 20, "durationMicroseconds": 53, "errorMessage": "Cannot access value for name listView", "functionName": "width", "line": 126}, {"codegenSuccessfull": true, "column": 21, "durationMicroseconds": 124, "errorMessage": "", "functionName": "height", "line": 127}, {"codegenSuccessfull": true, "column": 19, "durationMicroseconds": 126, "errorMessage": "", "functionName": "keys", "line": 128}, {"codegenSuccessfull": false, "column": 13, "durationMicroseconds": 0, "errorMessage": "Functions without type annotations won't be compiled", "functionName": "scrollViewWhenNeeded", "line": 158}, {"codegenSuccessfull": false, "column": 13, "durationMicroseconds": 0, "errorMessage": "Functions without type annotations won't be compiled", "functionName": "dropPositionCheck", "line": 169}, {"codegenSuccessfull": false, "column": 13, "durationMicroseconds": 222, "errorMessage": "Cannot access value for name itemType", "functionName": "launchItem", "line": 186}, {"codegenSuccessfull": false, "column": 32, "durationMicroseconds": 229, "errorMessage": "Cannot access value for name desktopId", "functionName": "onPositionChanged", "line": 199}, {"codegenSuccessfull": true, "column": 32, "durationMicroseconds": 62, "errorMessage": "", "functionName": "onPositionChanged", "line": 199}, {"codegenSuccessfull": false, "column": 24, "durationMicroseconds": 327, "errorMessage": "Cannot access value for name folderGridViewPopup", "functionName": "onEntered", "line": 214}, {"codegenSuccessfull": true, "column": 24, "durationMicroseconds": 60, "errorMessage": "", "functionName": "onEntered", "line": 214}, {"codegenSuccessfull": false, "column": 23, "durationMicroseconds": 72, "errorMessage": "Cannot access value for name listViewDragScroller", "functionName": "onExited", "line": 221}, {"codegenSuccessfull": false, "column": 24, "durationMicroseconds": 125, "errorMessage": "Cannot access value for name model", "functionName": "onDropped", "line": 227}, {"codegenSuccessfull": true, "column": 24, "durationMicroseconds": 55, "errorMessage": "", "functionName": "onDropped", "line": 227}, {"codegenSuccessfull": true, "column": 35, "durationMicroseconds": 142, "errorMessage": "", "functionName": "delayRemove", "line": 129}, {"codegenSuccessfull": false, "column": 30, "durationMicroseconds": 77, "errorMessage": "Cannot load property op from (component in /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/FreeSortListView.qml)::parent with type QObject.", "functionName": "onTriggered", "line": 140}, {"codegenSuccessfull": true, "column": 30, "durationMicroseconds": 45, "errorMessage": "", "functionName": "onTriggered", "line": 140}, {"codegenSuccessfull": false, "column": 17, "durationMicroseconds": 0, "errorMessage": "Functions without type annotations won't be compiled", "functionName": "startTimer", "line": 147}, {"codegenSuccessfull": true, "column": 17, "durationMicroseconds": 89, "errorMessage": "", "functionName": "stopTimer", "line": 152}, {"codegenSuccessfull": false, "column": 53, "durationMicroseconds": 217, "errorMessage": "Cannot access value for name Helper", "functionName": "backgroundP<PERSON><PERSON>", "line": 239}, {"codegenSuccessfull": false, "column": 26, "durationMicroseconds": 33, "errorMessage": "Cannot access value for name showDropIndicator", "functionName": "visible", "line": 240}, {"codegenSuccessfull": true, "column": 28, "durationMicroseconds": 113, "errorMessage": "", "functionName": "width", "line": 242}, {"codegenSuccessfull": true, "column": 29, "durationMicroseconds": 93, "errorMessage": "", "functionName": "height", "line": 243}, {"codegenSuccessfull": false, "column": 28, "durationMicroseconds": 56, "errorMessage": "Cannot load property backgroundPalette from Dtk::Quick::DQuickControlColorSelector.", "functionName": "color", "line": 245}, {"codegenSuccessfull": false, "column": 23, "durationMicroseconds": 99, "errorMessage": "Cannot access value for name model", "functionName": "text", "line": 251}, {"codegenSuccessfull": false, "column": 23, "durationMicroseconds": 24, "errorMessage": "Cannot load property fontManager from Dtk::Quick::DQMLGlobalObject.", "functionName": "font", "line": 288}, {"codegenSuccessfull": false, "column": 45, "durationMicroseconds": 327, "errorMessage": "Cannot access value for name DStyle", "functionName": "textColor", "line": 290}, {"codegenSuccessfull": true, "column": 26, "durationMicroseconds": 122, "errorMessage": "", "functionName": "visible", "line": 292}, {"codegenSuccessfull": false, "column": 28, "durationMicroseconds": 341, "errorMessage": "Cannot access value for name itemType", "functionName": "name", "line": 253}, {"codegenSuccessfull": true, "column": 31, "durationMicroseconds": 102, "errorMessage": "", "functionName": "mode", "line": 254}, {"codegenSuccessfull": false, "column": 30, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type  for binding on visible.", "functionName": "visible", "line": 259}, {"codegenSuccessfull": true, "column": 33, "durationMicroseconds": 315, "errorMessage": "", "functionName": "sourceSize", "line": 260}, {"codegenSuccessfull": false, "column": 30, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type Dtk::Gui::DDciIconPalette for binding on palette.", "functionName": "palette", "line": 261}, {"codegenSuccessfull": false, "column": 28, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type Dtk::Gui::DGuiApplicationHelper::ColorType for binding on theme.", "functionName": "theme", "line": 262}, {"codegenSuccessfull": false, "column": 35, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type  for binding on left.", "functionName": "left", "line": 256}, {"codegenSuccessfull": false, "column": 37, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type  for binding on bottom.", "functionName": "bottom", "line": 257}, {"codegenSuccessfull": true, "column": 27, "durationMicroseconds": 167, "errorMessage": "", "functionName": "left", "line": 266}, {"codegenSuccessfull": true, "column": 28, "durationMicroseconds": 119, "errorMessage": "", "functionName": "right", "line": 267}, {"codegenSuccessfull": false, "column": 28, "durationMicroseconds": 22, "errorMessage": "Cannot load property themeType from Dtk::Quick::DQMLGlobalObject.", "functionName": "color", "line": 275}, {"codegenSuccessfull": false, "column": 30, "durationMicroseconds": 56, "errorMessage": "Cannot access value for name model", "functionName": "visible", "line": 279}, {"codegenSuccessfull": false, "column": 35, "durationMicroseconds": 29, "errorMessage": "Cannot load property themeType from Dtk::Quick::DQMLGlobalObject.", "functionName": "color", "line": 277}, {"codegenSuccessfull": true, "column": 32, "durationMicroseconds": 116, "errorMessage": "", "functionName": "right", "line": 281}, {"codegenSuccessfull": true, "column": 41, "durationMicroseconds": 119, "errorMessage": "", "functionName": "verticalCenter", "line": 282}, {"codegenSuccessfull": false, "column": 37, "durationMicroseconds": 38, "errorMessage": "Cannot load property textColor from Dtk::Quick::DQuickControlColorSelector.", "functionName": "windowText", "line": 291}, {"codegenSuccessfull": true, "column": 31, "durationMicroseconds": 54, "errorMessage": "", "functionName": "text", "line": 294}, {"codegenSuccessfull": true, "column": 34, "durationMicroseconds": 272, "errorMessage": "", "functionName": "visible", "line": 296}, {"codegenSuccessfull": true, "column": 32, "durationMicroseconds": 79, "errorMessage": "", "functionName": "dragType", "line": 300}, {"codegenSuccessfull": true, "column": 30, "durationMicroseconds": 97, "errorMessage": "", "functionName": "active", "line": 301}, {"codegenSuccessfull": false, "column": 32, "durationMicroseconds": 269, "errorMessage": "Cannot access value for name Helper", "functionName": "mimeData", "line": 302}, {"codegenSuccessfull": false, "column": 39, "durationMicroseconds": 99, "errorMessage": "Cannot access value for name listViewDragScroller", "functionName": "onActiveChanged", "line": 303}, {"codegenSuccessfull": true, "column": 39, "durationMicroseconds": 36, "errorMessage": "", "functionName": "onActiveChanged", "line": 303}, {"codegenSuccessfull": true, "column": 33, "durationMicroseconds": 105, "errorMessage": "", "functionName": "x", "line": 298}, {"codegenSuccessfull": true, "column": 33, "durationMicroseconds": 96, "errorMessage": "", "functionName": "y", "line": 299}, {"codegenSuccessfull": false, "column": 36, "durationMicroseconds": 126, "errorMessage": "Cannot access value for name DStyle", "functionName": "implicitWidth", "line": 311}, {"codegenSuccessfull": false, "column": 37, "durationMicroseconds": 110, "errorMessage": "Cannot access value for name Helper", "functionName": "implicitHeight", "line": 312}, {"codegenSuccessfull": true, "column": 29, "durationMicroseconds": 75, "errorMessage": "", "functionName": "button", "line": 313}, {"codegenSuccessfull": true, "column": 38, "durationMicroseconds": 194, "errorMessage": "", "functionName": "acceptedButtons", "line": 320}, {"codegenSuccessfull": false, "column": 32, "durationMicroseconds": 447, "errorMessage": "Cannot generate efficient code for LoadClosure", "functionName": "onPressed", "line": 323}, {"codegenSuccessfull": true, "column": 32, "durationMicroseconds": 44, "errorMessage": "", "functionName": "onPressed", "line": 323}, {"codegenSuccessfull": false, "column": 32, "durationMicroseconds": 91, "errorMessage": "Cannot access value for name model", "functionName": "onClicked", "line": 335}, {"codegenSuccessfull": true, "column": 32, "durationMicroseconds": 49, "errorMessage": "", "functionName": "onClicked", "line": 335}, {"codegenSuccessfull": true, "column": 35, "durationMicroseconds": 51, "errorMessage": "", "functionName": "fill", "line": 318}, {"codegenSuccessfull": true, "column": 34, "durationMicroseconds": 68, "errorMessage": "", "functionName": "target", "line": 321}, {"codegenSuccessfull": true, "column": 35, "durationMicroseconds": 66, "errorMessage": "", "functionName": "onReturnPressed", "line": 348}, {"codegenSuccessfull": true, "column": 34, "durationMicroseconds": 59, "errorMessage": "", "functionName": "onSpacePressed", "line": 350}], "filepath": "/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/FreeSortListView.qml"}], "moduleId": "org.deepin.launchpad.windowed(launcher-qml-windowed)"}]