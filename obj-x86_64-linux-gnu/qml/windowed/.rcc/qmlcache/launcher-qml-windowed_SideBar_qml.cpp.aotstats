[{"moduleFiles": [{"entries": [{"codegenSuccessfull": false, "column": 31, "durationMicroseconds": 385, "errorMessage": "Cannot access value for name CategorizedSortProxyModel", "functionName": "isFreeSort", "line": 20}, {"codegenSuccessfull": true, "column": 33, "durationMicroseconds": 160, "errorMessage": "", "functionName": "keyTab<PERSON>arget", "line": 21}, {"codegenSuccessfull": false, "column": 5, "durationMicroseconds": 0, "errorMessage": "Functions without type annotations won't be compiled", "functionName": "categorizedIcon", "line": 26}, {"codegenSuccessfull": true, "column": 26, "durationMicroseconds": 137, "errorMessage": "", "functionName": "display", "line": 42}, {"codegenSuccessfull": false, "column": 26, "durationMicroseconds": 53, "errorMessage": "Cannot access value for name isFreeSort", "functionName": "checked", "line": 43}, {"codegenSuccessfull": false, "column": 30, "durationMicroseconds": 60, "errorMessage": "Cannot access value for name isFreeSort", "functionName": "onTriggered", "line": 44}, {"codegenSuccessfull": false, "column": 28, "durationMicroseconds": 456, "errorMessage": "method categorizedIcon cannot be resolved.", "functionName": "name", "line": 41}, {"codegenSuccessfull": true, "column": 26, "durationMicroseconds": 142, "errorMessage": "", "functionName": "display", "line": 56}, {"codegenSuccessfull": false, "column": 26, "durationMicroseconds": 593, "errorMessage": "Cannot access value for name CategorizedSortProxyModel", "functionName": "checked", "line": 57}, {"codegenSuccessfull": false, "column": 30, "durationMicroseconds": 58, "errorMessage": "Cannot find name isFreeSort", "functionName": "onTriggered", "line": 58}, {"codegenSuccessfull": false, "column": 28, "durationMicroseconds": 599, "errorMessage": "Cannot access value for name CategorizedSortProxyModel", "functionName": "name", "line": 55}, {"codegenSuccessfull": true, "column": 26, "durationMicroseconds": 146, "errorMessage": "", "functionName": "display", "line": 68}, {"codegenSuccessfull": false, "column": 26, "durationMicroseconds": 587, "errorMessage": "Cannot access value for name CategorizedSortProxyModel", "functionName": "checked", "line": 69}, {"codegenSuccessfull": false, "column": 30, "durationMicroseconds": 59, "errorMessage": "Cannot find name isFreeSort", "functionName": "onTriggered", "line": 70}, {"codegenSuccessfull": false, "column": 28, "durationMicroseconds": 569, "errorMessage": "Cannot access value for name CategorizedSortProxyModel", "functionName": "name", "line": 67}, {"codegenSuccessfull": false, "column": 25, "durationMicroseconds": 72, "errorMessage": "Cannot access value for name LauncherController", "functionName": "target", "line": 78}, {"codegenSuccessfull": false, "column": 17, "durationMicroseconds": 0, "errorMessage": "Functions without type annotations won't be compiled", "functionName": "onVisibleChanged", "line": 79}, {"codegenSuccessfull": false, "column": 20, "durationMicroseconds": 190, "errorMessage": "Type QObject does not have a property popup for calling", "functionName": "onClicked", "line": 124}, {"codegenSuccessfull": true, "column": 27, "durationMicroseconds": 122, "errorMessage": "", "functionName": "alignment", "line": 91}, {"codegenSuccessfull": true, "column": 29, "durationMicroseconds": 117, "errorMessage": "", "functionName": "down", "line": 92}, {"codegenSuccessfull": true, "column": 27, "durationMicroseconds": 101, "errorMessage": "", "functionName": "up", "line": 93}, {"codegenSuccessfull": false, "column": 28, "durationMicroseconds": 49, "errorMessage": "Cannot access value for name nextKeyTabTarget", "functionName": "tab", "line": 94}, {"codegenSuccessfull": false, "column": 26, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type  for binding on visible.", "functionName": "visible", "line": 97}, {"codegenSuccessfull": true, "column": 29, "durationMicroseconds": 348, "errorMessage": "", "functionName": "sourceSize", "line": 104}, {"codegenSuccessfull": false, "column": 23, "durationMicroseconds": 47, "errorMessage": "Cannot access value for name isFreeSort", "functionName": "name", "line": 105}, {"codegenSuccessfull": false, "column": 26, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type Dtk::Gui::DDciIconPalette for binding on palette.", "functionName": "palette", "line": 106}, {"codegenSuccessfull": false, "column": 24, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type Dtk::Gui::DGuiApplicationHelper::ColorType for binding on theme.", "functionName": "theme", "line": 107}, {"codegenSuccessfull": true, "column": 35, "durationMicroseconds": 109, "errorMessage": "", "functionName": "alignment", "line": 108}, {"codegenSuccessfull": true, "column": 29, "durationMicroseconds": 342, "errorMessage": "", "functionName": "sourceSize", "line": 113}, {"codegenSuccessfull": false, "column": 26, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type Dtk::Gui::DDciIconPalette for binding on palette.", "functionName": "palette", "line": 114}, {"codegenSuccessfull": false, "column": 24, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type Dtk::Gui::DGuiApplicationHelper::ColorType for binding on theme.", "functionName": "theme", "line": 115}, {"codegenSuccessfull": true, "column": 35, "durationMicroseconds": 103, "errorMessage": "", "functionName": "alignment", "line": 116}, {"codegenSuccessfull": true, "column": 21, "durationMicroseconds": 118, "errorMessage": "", "functionName": "button", "line": 121}, {"codegenSuccessfull": true, "column": 22, "durationMicroseconds": 126, "errorMessage": "", "functionName": "focusPolicy", "line": 139}, {"codegenSuccessfull": false, "column": 26, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type  for binding on visible.", "functionName": "visible", "line": 136}, {"codegenSuccessfull": true, "column": 27, "durationMicroseconds": 120, "errorMessage": "", "functionName": "alignment", "line": 138}, {"codegenSuccessfull": false, "column": 29, "durationMicroseconds": 59, "errorMessage": "Cannot load property textColor from Dtk::Quick::DQuickControlColorSelector.", "functionName": "windowText", "line": 141}, {"codegenSuccessfull": true, "column": 21, "durationMicroseconds": 105, "errorMessage": "", "functionName": "button", "line": 147}, {"codegenSuccessfull": false, "column": 20, "durationMicroseconds": 522, "errorMessage": "Cannot access value for name DesktopIntegration", "functionName": "onClicked", "line": 157}, {"codegenSuccessfull": true, "column": 29, "durationMicroseconds": 111, "errorMessage": "", "functionName": "down", "line": 155}, {"codegenSuccessfull": true, "column": 27, "durationMicroseconds": 104, "errorMessage": "", "functionName": "up", "line": 156}, {"codegenSuccessfull": false, "column": 20, "durationMicroseconds": 504, "errorMessage": "Cannot access value for name DesktopIntegration", "functionName": "onClicked", "line": 168}, {"codegenSuccessfull": true, "column": 29, "durationMicroseconds": 113, "errorMessage": "", "functionName": "down", "line": 166}, {"codegenSuccessfull": true, "column": 27, "durationMicroseconds": 102, "errorMessage": "", "functionName": "up", "line": 167}, {"codegenSuccessfull": false, "column": 20, "durationMicroseconds": 450, "errorMessage": "Cannot access value for name DesktopIntegration", "functionName": "onClicked", "line": 179}, {"codegenSuccessfull": true, "column": 29, "durationMicroseconds": 118, "errorMessage": "", "functionName": "down", "line": 177}, {"codegenSuccessfull": true, "column": 27, "durationMicroseconds": 113, "errorMessage": "", "functionName": "up", "line": 178}, {"codegenSuccessfull": false, "column": 20, "durationMicroseconds": 461, "errorMessage": "Cannot access value for name DesktopIntegration", "functionName": "onClicked", "line": 190}, {"codegenSuccessfull": true, "column": 29, "durationMicroseconds": 108, "errorMessage": "", "functionName": "down", "line": 188}, {"codegenSuccessfull": true, "column": 27, "durationMicroseconds": 121, "errorMessage": "", "functionName": "up", "line": 189}, {"codegenSuccessfull": false, "column": 20, "durationMicroseconds": 502, "errorMessage": "Cannot access value for name DesktopIntegration", "functionName": "onClicked", "line": 201}, {"codegenSuccessfull": true, "column": 29, "durationMicroseconds": 106, "errorMessage": "", "functionName": "down", "line": 199}, {"codegenSuccessfull": true, "column": 27, "durationMicroseconds": 94, "errorMessage": "", "functionName": "up", "line": 200}, {"codegenSuccessfull": true, "column": 17, "durationMicroseconds": 113, "errorMessage": "", "functionName": "height", "line": 211}], "filepath": "/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/SideBar.qml"}], "moduleId": "org.deepin.launchpad.windowed(launcher-qml-windowed)"}]