[{"moduleFiles": [{"entries": [{"codegenSuccessfull": false, "column": 31, "durationMicroseconds": 257, "errorMessage": "Cannot access value for name CategorizedSortProxyModel", "functionName": "isFreeSort", "line": 20}, {"codegenSuccessfull": true, "column": 33, "durationMicroseconds": 108, "errorMessage": "", "functionName": "keyTab<PERSON>arget", "line": 21}, {"codegenSuccessfull": false, "column": 5, "durationMicroseconds": 0, "errorMessage": "Functions without type annotations won't be compiled", "functionName": "categorizedIcon", "line": 26}, {"codegenSuccessfull": true, "column": 26, "durationMicroseconds": 97, "errorMessage": "", "functionName": "display", "line": 42}, {"codegenSuccessfull": false, "column": 26, "durationMicroseconds": 32, "errorMessage": "Cannot access value for name isFreeSort", "functionName": "checked", "line": 43}, {"codegenSuccessfull": false, "column": 30, "durationMicroseconds": 36, "errorMessage": "Cannot access value for name isFreeSort", "functionName": "onTriggered", "line": 44}, {"codegenSuccessfull": false, "column": 28, "durationMicroseconds": 317, "errorMessage": "method categorizedIcon cannot be resolved.", "functionName": "name", "line": 41}, {"codegenSuccessfull": true, "column": 26, "durationMicroseconds": 94, "errorMessage": "", "functionName": "display", "line": 56}, {"codegenSuccessfull": false, "column": 26, "durationMicroseconds": 399, "errorMessage": "Cannot access value for name CategorizedSortProxyModel", "functionName": "checked", "line": 57}, {"codegenSuccessfull": false, "column": 30, "durationMicroseconds": 37, "errorMessage": "Cannot find name isFreeSort", "functionName": "onTriggered", "line": 58}, {"codegenSuccessfull": false, "column": 28, "durationMicroseconds": 396, "errorMessage": "Cannot access value for name CategorizedSortProxyModel", "functionName": "name", "line": 55}, {"codegenSuccessfull": true, "column": 26, "durationMicroseconds": 91, "errorMessage": "", "functionName": "display", "line": 68}, {"codegenSuccessfull": false, "column": 26, "durationMicroseconds": 402, "errorMessage": "Cannot access value for name CategorizedSortProxyModel", "functionName": "checked", "line": 69}, {"codegenSuccessfull": false, "column": 30, "durationMicroseconds": 33, "errorMessage": "Cannot find name isFreeSort", "functionName": "onTriggered", "line": 70}, {"codegenSuccessfull": false, "column": 28, "durationMicroseconds": 393, "errorMessage": "Cannot access value for name CategorizedSortProxyModel", "functionName": "name", "line": 67}, {"codegenSuccessfull": false, "column": 25, "durationMicroseconds": 47, "errorMessage": "Cannot access value for name LauncherController", "functionName": "target", "line": 78}, {"codegenSuccessfull": false, "column": 17, "durationMicroseconds": 0, "errorMessage": "Functions without type annotations won't be compiled", "functionName": "onVisibleChanged", "line": 79}, {"codegenSuccessfull": false, "column": 20, "durationMicroseconds": 133, "errorMessage": "Type QObject does not have a property popup for calling", "functionName": "onClicked", "line": 124}, {"codegenSuccessfull": true, "column": 27, "durationMicroseconds": 106, "errorMessage": "", "functionName": "alignment", "line": 91}, {"codegenSuccessfull": true, "column": 29, "durationMicroseconds": 81, "errorMessage": "", "functionName": "down", "line": 92}, {"codegenSuccessfull": true, "column": 27, "durationMicroseconds": 70, "errorMessage": "", "functionName": "up", "line": 93}, {"codegenSuccessfull": false, "column": 28, "durationMicroseconds": 32, "errorMessage": "Cannot access value for name nextKeyTabTarget", "functionName": "tab", "line": 94}, {"codegenSuccessfull": false, "column": 26, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type  for binding on visible.", "functionName": "visible", "line": 97}, {"codegenSuccessfull": true, "column": 29, "durationMicroseconds": 271, "errorMessage": "", "functionName": "sourceSize", "line": 104}, {"codegenSuccessfull": false, "column": 23, "durationMicroseconds": 48, "errorMessage": "Cannot access value for name isFreeSort", "functionName": "name", "line": 105}, {"codegenSuccessfull": false, "column": 26, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type Dtk::Gui::DDciIconPalette for binding on palette.", "functionName": "palette", "line": 106}, {"codegenSuccessfull": false, "column": 24, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type Dtk::Gui::DGuiApplicationHelper::ColorType for binding on theme.", "functionName": "theme", "line": 107}, {"codegenSuccessfull": true, "column": 35, "durationMicroseconds": 79, "errorMessage": "", "functionName": "alignment", "line": 108}, {"codegenSuccessfull": true, "column": 29, "durationMicroseconds": 258, "errorMessage": "", "functionName": "sourceSize", "line": 113}, {"codegenSuccessfull": false, "column": 26, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type Dtk::Gui::DDciIconPalette for binding on palette.", "functionName": "palette", "line": 114}, {"codegenSuccessfull": false, "column": 24, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type Dtk::Gui::DGuiApplicationHelper::ColorType for binding on theme.", "functionName": "theme", "line": 115}, {"codegenSuccessfull": true, "column": 35, "durationMicroseconds": 73, "errorMessage": "", "functionName": "alignment", "line": 116}, {"codegenSuccessfull": true, "column": 21, "durationMicroseconds": 76, "errorMessage": "", "functionName": "button", "line": 121}, {"codegenSuccessfull": true, "column": 22, "durationMicroseconds": 83, "errorMessage": "", "functionName": "focusPolicy", "line": 139}, {"codegenSuccessfull": false, "column": 26, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type  for binding on visible.", "functionName": "visible", "line": 136}, {"codegenSuccessfull": true, "column": 27, "durationMicroseconds": 183, "errorMessage": "", "functionName": "alignment", "line": 138}, {"codegenSuccessfull": false, "column": 29, "durationMicroseconds": 75, "errorMessage": "Cannot load property textColor from Dtk::Quick::DQuickControlColorSelector.", "functionName": "windowText", "line": 141}, {"codegenSuccessfull": true, "column": 21, "durationMicroseconds": 123, "errorMessage": "", "functionName": "button", "line": 147}, {"codegenSuccessfull": false, "column": 20, "durationMicroseconds": 366, "errorMessage": "Cannot access value for name DesktopIntegration", "functionName": "onClicked", "line": 157}, {"codegenSuccessfull": true, "column": 29, "durationMicroseconds": 96, "errorMessage": "", "functionName": "down", "line": 155}, {"codegenSuccessfull": true, "column": 27, "durationMicroseconds": 99, "errorMessage": "", "functionName": "up", "line": 156}, {"codegenSuccessfull": false, "column": 20, "durationMicroseconds": 413, "errorMessage": "Cannot access value for name DesktopIntegration", "functionName": "onClicked", "line": 168}, {"codegenSuccessfull": true, "column": 29, "durationMicroseconds": 105, "errorMessage": "", "functionName": "down", "line": 166}, {"codegenSuccessfull": true, "column": 27, "durationMicroseconds": 93, "errorMessage": "", "functionName": "up", "line": 167}, {"codegenSuccessfull": false, "column": 20, "durationMicroseconds": 325, "errorMessage": "Cannot access value for name DesktopIntegration", "functionName": "onClicked", "line": 179}, {"codegenSuccessfull": true, "column": 29, "durationMicroseconds": 78, "errorMessage": "", "functionName": "down", "line": 177}, {"codegenSuccessfull": true, "column": 27, "durationMicroseconds": 98, "errorMessage": "", "functionName": "up", "line": 178}, {"codegenSuccessfull": false, "column": 20, "durationMicroseconds": 332, "errorMessage": "Cannot access value for name DesktopIntegration", "functionName": "onClicked", "line": 190}, {"codegenSuccessfull": true, "column": 29, "durationMicroseconds": 105, "errorMessage": "", "functionName": "down", "line": 188}, {"codegenSuccessfull": true, "column": 27, "durationMicroseconds": 104, "errorMessage": "", "functionName": "up", "line": 189}, {"codegenSuccessfull": false, "column": 20, "durationMicroseconds": 346, "errorMessage": "Cannot access value for name DesktopIntegration", "functionName": "onClicked", "line": 201}, {"codegenSuccessfull": true, "column": 29, "durationMicroseconds": 82, "errorMessage": "", "functionName": "down", "line": 199}, {"codegenSuccessfull": true, "column": 27, "durationMicroseconds": 70, "errorMessage": "", "functionName": "up", "line": 200}, {"codegenSuccessfull": true, "column": 17, "durationMicroseconds": 79, "errorMessage": "", "functionName": "height", "line": 211}], "filepath": "/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/SideBar.qml"}], "moduleId": "org.deepin.launchpad.windowed(launcher-qml-windowed)"}]