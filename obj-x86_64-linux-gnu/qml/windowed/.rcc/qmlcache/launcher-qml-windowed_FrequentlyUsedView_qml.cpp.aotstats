[{"moduleFiles": [{"entries": [{"codegenSuccessfull": true, "column": 21, "durationMicroseconds": 187, "errorMessage": "", "functionName": "onFocusChanged", "line": 17}, {"codegenSuccessfull": true, "column": 21, "durationMicroseconds": 56, "errorMessage": "", "functionName": "onFocusChanged", "line": 17}, {"codegenSuccessfull": true, "column": 33, "durationMicroseconds": 96, "errorMessage": "", "functionName": "keyTab<PERSON>arget", "line": 22}, {"codegenSuccessfull": true, "column": 25, "durationMicroseconds": 108, "errorMessage": "", "functionName": "count", "line": 25}, {"codegenSuccessfull": true, "column": 5, "durationMicroseconds": 130, "errorMessage": "", "functionName": "positionViewAtBeginning", "line": 28}, {"codegenSuccessfull": false, "column": 19, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type  for binding on font.", "functionName": "font", "line": 37}, {"codegenSuccessfull": true, "column": 31, "durationMicroseconds": 93, "errorMessage": "", "functionName": "activeFocusOnTab", "line": 71}, {"codegenSuccessfull": true, "column": 32, "durationMicroseconds": 109, "errorMessage": "", "functionName": "tab", "line": 43}, {"codegenSuccessfull": true, "column": 31, "durationMicroseconds": 105, "errorMessage": "", "functionName": "alignment", "line": 44}, {"codegenSuccessfull": true, "column": 37, "durationMicroseconds": 113, "errorMessage": "", "functionName": "preferredHeight", "line": 46}, {"codegenSuccessfull": true, "column": 36, "durationMicroseconds": 101, "errorMessage": "", "functionName": "preferredWidth", "line": 47}, {"codegenSuccessfull": false, "column": 30, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type  for binding on sourceModel.", "functionName": "sourceModel", "line": 51}, {"codegenSuccessfull": false, "column": 30, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type  for binding on maxRowCount.", "functionName": "maxRowCount", "line": 52}, {"codegenSuccessfull": false, "column": 24, "durationMicroseconds": 50, "errorMessage": "Cannot access value for name frequentlyUsedViewContainer", "functionName": "width", "line": 56}, {"codegenSuccessfull": false, "column": 25, "durationMicroseconds": 40, "errorMessage": "Cannot access value for name frequentlyUsedViewContainer", "functionName": "height", "line": 57}, {"codegenSuccessfull": false, "column": 29, "durationMicroseconds": 258, "errorMessage": "Cannot access value for name iconName", "functionName": "iconSource", "line": 58}, {"codegenSuccessfull": false, "column": 29, "durationMicroseconds": 355, "errorMessage": "Cannot access value for name folderGridViewPopup", "functionName": "dndEnabled", "line": 60}, {"codegenSuccessfull": false, "column": 32, "durationMicroseconds": 276, "errorMessage": "Cannot access value for name desktopId", "functionName": "onItemClicked", "line": 62}, {"codegenSuccessfull": false, "column": 34, "durationMicroseconds": 51, "errorMessage": "Cannot access value for name model", "functionName": "onMenuTriggered", "line": 65}, {"codegenSuccessfull": false, "column": 32, "durationMicroseconds": 218, "errorMessage": "Cannot access value for name Helper", "functionName": "mimeData", "line": 61}], "filepath": "/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/FrequentlyUsedView.qml"}], "moduleId": "org.deepin.launchpad.windowed(launcher-qml-windowed)"}]