[{"moduleFiles": [{"entries": [{"codegenSuccessfull": true, "column": 21, "durationMicroseconds": 131, "errorMessage": "", "functionName": "onFocusChanged", "line": 17}, {"codegenSuccessfull": true, "column": 21, "durationMicroseconds": 37, "errorMessage": "", "functionName": "onFocusChanged", "line": 17}, {"codegenSuccessfull": true, "column": 33, "durationMicroseconds": 68, "errorMessage": "", "functionName": "keyTab<PERSON>arget", "line": 22}, {"codegenSuccessfull": true, "column": 25, "durationMicroseconds": 85, "errorMessage": "", "functionName": "count", "line": 25}, {"codegenSuccessfull": true, "column": 5, "durationMicroseconds": 96, "errorMessage": "", "functionName": "positionViewAtBeginning", "line": 28}, {"codegenSuccessfull": false, "column": 19, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type  for binding on font.", "functionName": "font", "line": 37}, {"codegenSuccessfull": true, "column": 31, "durationMicroseconds": 52, "errorMessage": "", "functionName": "activeFocusOnTab", "line": 71}, {"codegenSuccessfull": true, "column": 32, "durationMicroseconds": 76, "errorMessage": "", "functionName": "tab", "line": 43}, {"codegenSuccessfull": true, "column": 31, "durationMicroseconds": 82, "errorMessage": "", "functionName": "alignment", "line": 44}, {"codegenSuccessfull": true, "column": 37, "durationMicroseconds": 71, "errorMessage": "", "functionName": "preferredHeight", "line": 46}, {"codegenSuccessfull": true, "column": 36, "durationMicroseconds": 67, "errorMessage": "", "functionName": "preferredWidth", "line": 47}, {"codegenSuccessfull": false, "column": 30, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type  for binding on sourceModel.", "functionName": "sourceModel", "line": 51}, {"codegenSuccessfull": false, "column": 30, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type  for binding on maxRowCount.", "functionName": "maxRowCount", "line": 52}, {"codegenSuccessfull": false, "column": 24, "durationMicroseconds": 38, "errorMessage": "Cannot access value for name frequentlyUsedViewContainer", "functionName": "width", "line": 56}, {"codegenSuccessfull": false, "column": 25, "durationMicroseconds": 24, "errorMessage": "Cannot access value for name frequentlyUsedViewContainer", "functionName": "height", "line": 57}, {"codegenSuccessfull": false, "column": 29, "durationMicroseconds": 166, "errorMessage": "Cannot access value for name iconName", "functionName": "iconSource", "line": 58}, {"codegenSuccessfull": false, "column": 29, "durationMicroseconds": 244, "errorMessage": "Cannot access value for name folderGridViewPopup", "functionName": "dndEnabled", "line": 60}, {"codegenSuccessfull": false, "column": 32, "durationMicroseconds": 161, "errorMessage": "Cannot access value for name desktopId", "functionName": "onItemClicked", "line": 62}, {"codegenSuccessfull": false, "column": 34, "durationMicroseconds": 31, "errorMessage": "Cannot access value for name model", "functionName": "onMenuTriggered", "line": 65}, {"codegenSuccessfull": false, "column": 32, "durationMicroseconds": 141, "errorMessage": "Cannot access value for name Helper", "functionName": "mimeData", "line": 61}], "filepath": "/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/FrequentlyUsedView.qml"}], "moduleId": "org.deepin.launchpad.windowed(launcher-qml-windowed)"}]