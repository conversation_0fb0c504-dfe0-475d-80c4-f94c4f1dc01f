launcher-qml-windowed_init_autogen/timestamp: \
	/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/launcher-qml-windowed_org_deepin_launchpad_windowedPlugin_in.cpp \
	/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/launcher_qml_windowed_init.cpp \
	/home/<USER>/Desktop/myrepo/dde-launchpad/qml/windowed/CMakeLists.txt \
	/usr/lib/x86_64-linux-gnu/cmake/Qt6Core/Qt6CoreConfigureFileTemplate.in \
	/usr/lib/x86_64-linux-gnu/cmake/Qt6Core/Qt6CoreResourceInit.in.cpp \
	/usr/lib/x86_64-linux-gnu/cmake/Qt6Qml/Qt6QmlModuleDirMappingTemplate.qrc.in \
	/usr/lib/x86_64-linux-gnu/cmake/Qt6Qml/Qt6QmlPluginTemplate.cpp.in \
	/usr/lib/x86_64-linux-gnu/cmake/Qt6Qml/Qt6qmldirTemplate.cmake.in \
	/usr/bin/cmake
