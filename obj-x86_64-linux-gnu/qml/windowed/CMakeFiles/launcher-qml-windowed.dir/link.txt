/usr/bin/ar qc liblauncher-qml-windowed.a "CMakeFiles/launcher-qml-windowed.dir/launcher-qml-windowed_autogen/mocs_compilation.cpp.o" "CMakeFiles/launcher-qml-windowed.dir/launcher-qml-windowed_org_deepin_launchpad_windowedPlugin.cpp.o" "CMakeFiles/launcher-qml-windowed.dir/launcher-qml-windowed_qmltyperegistrations.cpp.o" "CMakeFiles/launcher-qml-windowed.dir/.qt/rcc/qrc_qmake_org_deepin_launchpad_windowed.cpp.o" "CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_qmlcache_loader.cpp.o" "CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_WindowedFrame_qml.cpp.o" "CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_SideBar_qml.cpp.o" "CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_BottomBar_qml.cpp.o" "CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_AlphabetCategoryPopup_qml.cpp.o" "CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_AppList_qml.cpp.o" "CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_AppListView_qml.cpp.o" "CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_IconItemDelegate_qml.cpp.o" "CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_FreeSortListView_qml.cpp.o" "CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_GridViewContainer_qml.cpp.o" "CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_AnalysisView_qml.cpp.o" "CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_RecentlyInstalledView_qml.cpp.o" "CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_FrequentlyUsedView_qml.cpp.o" "CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_SearchResultView_qml.cpp.o" "CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_ItemBackground_qml.cpp.o" "CMakeFiles/launcher-qml-windowed.dir/.qt/rcc/qrc_launcher-qml-windowed_raw_qml_0.cpp.o"
/usr/bin/ranlib liblauncher-qml-windowed.a
