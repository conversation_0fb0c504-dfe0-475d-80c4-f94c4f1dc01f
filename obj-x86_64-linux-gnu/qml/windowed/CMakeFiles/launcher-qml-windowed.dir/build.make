# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.31

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Produce verbose output by default.
VERBOSE = 1

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/Desktop/myrepo/dde-launchpad

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu

# Include any dependencies generated for this target.
include qml/windowed/CMakeFiles/launcher-qml-windowed.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include qml/windowed/CMakeFiles/launcher-qml-windowed.dir/compiler_depend.make

# Include the progress variables for this target.
include qml/windowed/CMakeFiles/launcher-qml-windowed.dir/progress.make

# Include the compile flags for this target's objects.
include qml/windowed/CMakeFiles/launcher-qml-windowed.dir/flags.make

qml/windowed/meta_types/qt6launcher-qml-windowed_none_metatypes.json.gen: /usr/lib/qt6/libexec/moc
qml/windowed/meta_types/qt6launcher-qml-windowed_none_metatypes.json.gen: qml/windowed/meta_types/launcher-qml-windowed_json_file_list.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Running moc --collect-json for target launcher-qml-windowed"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed && /usr/lib/qt6/libexec/moc -o /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/meta_types/qt6launcher-qml-windowed_none_metatypes.json.gen --collect-json @/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/meta_types/launcher-qml-windowed_json_file_list.txt
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed && /usr/bin/cmake -E copy_if_different /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/meta_types/qt6launcher-qml-windowed_none_metatypes.json.gen /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/meta_types/qt6launcher-qml-windowed_none_metatypes.json

qml/windowed/launcher-qml-windowed_qmltyperegistrations.cpp: qml/windowed/qmltypes/launcher-qml-windowed_foreign_types.txt
qml/windowed/launcher-qml-windowed_qmltyperegistrations.cpp: qml/windowed/meta_types/qt6launcher-qml-windowed_none_metatypes.json
qml/windowed/launcher-qml-windowed_qmltyperegistrations.cpp: /usr/lib/qt6/libexec/qmltyperegistrar
qml/windowed/launcher-qml-windowed_qmltyperegistrations.cpp: /usr/lib/x86_64-linux-gnu/qt6/metatypes/qt6qml_none_metatypes.json
qml/windowed/launcher-qml-windowed_qmltyperegistrations.cpp: /usr/lib/x86_64-linux-gnu/qt6/metatypes/qt6core_none_metatypes.json
qml/windowed/launcher-qml-windowed_qmltyperegistrations.cpp: /usr/lib/x86_64-linux-gnu/qt6/metatypes/qt6network_none_metatypes.json
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Automatic QML type registration for target launcher-qml-windowed"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed && /usr/lib/qt6/libexec/qmltyperegistrar --generate-qmltypes=/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/launcher-qml-windowed.qmltypes --import-name=org.deepin.launchpad.windowed --major-version=1 --minor-version=0 @/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/qmltypes/launcher-qml-windowed_foreign_types.txt -o /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/launcher-qml-windowed_qmltyperegistrations.cpp /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/meta_types/qt6launcher-qml-windowed_none_metatypes.json
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed && /usr/bin/cmake -E make_directory /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/.qt/qmltypes
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed && /usr/bin/cmake -E touch /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/.qt/qmltypes/launcher-qml-windowed.qmltypes

qml/windowed/launcher-qml-windowed.qmltypes: qml/windowed/launcher-qml-windowed_qmltyperegistrations.cpp
	@$(CMAKE_COMMAND) -E touch_nocreate qml/windowed/launcher-qml-windowed.qmltypes

qml/windowed/.qt/rcc/qrc_qmake_org_deepin_launchpad_windowed.cpp: qml/windowed/qmldir
qml/windowed/.qt/rcc/qrc_qmake_org_deepin_launchpad_windowed.cpp: qml/windowed/.qt/rcc/qmake_org_deepin_launchpad_windowed.qrc
qml/windowed/.qt/rcc/qrc_qmake_org_deepin_launchpad_windowed.cpp: /usr/lib/qt6/libexec/rcc
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Running rcc for resource qmake_org_deepin_launchpad_windowed"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed && /usr/lib/qt6/libexec/rcc --output /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/.qt/rcc/qrc_qmake_org_deepin_launchpad_windowed.cpp --name qmake_org_deepin_launchpad_windowed /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/.qt/rcc/qmake_org_deepin_launchpad_windowed.qrc

qml/windowed/.rcc/qmlcache/launcher-qml-windowed_qmlcache_loader.cpp: /usr/lib/qt6/libexec/qmlcachegen
qml/windowed/.rcc/qmlcache/launcher-qml-windowed_qmlcache_loader.cpp: qml/windowed/.rcc/qmlcache/launcher-qml-windowed_qml_loader_file_list.rsp
qml/windowed/.rcc/qmlcache/launcher-qml-windowed_qmlcache_loader.cpp: qml/windowed/.qt/rcc/qmake_org_deepin_launchpad_windowed.qrc
qml/windowed/.rcc/qmlcache/launcher-qml-windowed_qmlcache_loader.cpp: qml/windowed/.qt/rcc/launcher-qml-windowed_raw_qml_0.qrc
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Generating .rcc/qmlcache/launcher-qml-windowed_qmlcache_loader.cpp"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed && /usr/lib/qt6/libexec/qmlcachegen --resource-name qmlcache_launcher-qml-windowed -o /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/.rcc/qmlcache/launcher-qml-windowed_qmlcache_loader.cpp @/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/.rcc/qmlcache/launcher-qml-windowed_qml_loader_file_list.rsp

qml/windowed/.rcc/qmlcache/launcher-qml-windowed_WindowedFrame_qml.cpp: /usr/lib/qt6/libexec/qmlcachegen
qml/windowed/.rcc/qmlcache/launcher-qml-windowed_WindowedFrame_qml.cpp: /home/<USER>/Desktop/myrepo/dde-launchpad/qml/windowed/WindowedFrame.qml
qml/windowed/.rcc/qmlcache/launcher-qml-windowed_WindowedFrame_qml.cpp: qml/windowed/.qt/rcc/qmake_org_deepin_launchpad_windowed.qrc
qml/windowed/.rcc/qmlcache/launcher-qml-windowed_WindowedFrame_qml.cpp: qml/windowed/.qt/rcc/launcher-qml-windowed_raw_qml_0.qrc
qml/windowed/.rcc/qmlcache/launcher-qml-windowed_WindowedFrame_qml.cpp: qml/windowed/launcher-qml-windowed.qmltypes
qml/windowed/.rcc/qmlcache/launcher-qml-windowed_WindowedFrame_qml.cpp: qml/windowed/qmldir
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Generating .rcc/qmlcache/launcher-qml-windowed_WindowedFrame_qml.cpp, .rcc/qmlcache/launcher-qml-windowed_WindowedFrame_qml.cpp.aotstats"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed && /usr/bin/cmake -E make_directory /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/.rcc/qmlcache
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed && /usr/lib/qt6/libexec/qmlcachegen --bare --resource-path /qt/qml/org/deepin/launchpad/windowed/WindowedFrame.qml -I /usr/lib/x86_64-linux-gnu/qt6/qml -i /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/qmldir --resource /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/.qt/rcc/qmake_org_deepin_launchpad_windowed.qrc --resource /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/.qt/rcc/launcher-qml-windowed_raw_qml_0.qrc --dump-aot-stats "--module-id=org.deepin.launchpad.windowed(launcher-qml-windowed)" -o /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/.rcc/qmlcache/launcher-qml-windowed_WindowedFrame_qml.cpp /home/<USER>/Desktop/myrepo/dde-launchpad/qml/windowed/WindowedFrame.qml

qml/windowed/.rcc/qmlcache/launcher-qml-windowed_WindowedFrame_qml.cpp.aotstats: qml/windowed/.rcc/qmlcache/launcher-qml-windowed_WindowedFrame_qml.cpp
	@$(CMAKE_COMMAND) -E touch_nocreate qml/windowed/.rcc/qmlcache/launcher-qml-windowed_WindowedFrame_qml.cpp.aotstats

qml/windowed/.rcc/qmlcache/launcher-qml-windowed_SideBar_qml.cpp: /usr/lib/qt6/libexec/qmlcachegen
qml/windowed/.rcc/qmlcache/launcher-qml-windowed_SideBar_qml.cpp: /home/<USER>/Desktop/myrepo/dde-launchpad/qml/windowed/SideBar.qml
qml/windowed/.rcc/qmlcache/launcher-qml-windowed_SideBar_qml.cpp: qml/windowed/.qt/rcc/qmake_org_deepin_launchpad_windowed.qrc
qml/windowed/.rcc/qmlcache/launcher-qml-windowed_SideBar_qml.cpp: qml/windowed/.qt/rcc/launcher-qml-windowed_raw_qml_0.qrc
qml/windowed/.rcc/qmlcache/launcher-qml-windowed_SideBar_qml.cpp: qml/windowed/launcher-qml-windowed.qmltypes
qml/windowed/.rcc/qmlcache/launcher-qml-windowed_SideBar_qml.cpp: qml/windowed/qmldir
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Generating .rcc/qmlcache/launcher-qml-windowed_SideBar_qml.cpp, .rcc/qmlcache/launcher-qml-windowed_SideBar_qml.cpp.aotstats"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed && /usr/bin/cmake -E make_directory /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/.rcc/qmlcache
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed && /usr/lib/qt6/libexec/qmlcachegen --bare --resource-path /qt/qml/org/deepin/launchpad/windowed/SideBar.qml -I /usr/lib/x86_64-linux-gnu/qt6/qml -i /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/qmldir --resource /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/.qt/rcc/qmake_org_deepin_launchpad_windowed.qrc --resource /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/.qt/rcc/launcher-qml-windowed_raw_qml_0.qrc --dump-aot-stats "--module-id=org.deepin.launchpad.windowed(launcher-qml-windowed)" -o /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/.rcc/qmlcache/launcher-qml-windowed_SideBar_qml.cpp /home/<USER>/Desktop/myrepo/dde-launchpad/qml/windowed/SideBar.qml

qml/windowed/.rcc/qmlcache/launcher-qml-windowed_SideBar_qml.cpp.aotstats: qml/windowed/.rcc/qmlcache/launcher-qml-windowed_SideBar_qml.cpp
	@$(CMAKE_COMMAND) -E touch_nocreate qml/windowed/.rcc/qmlcache/launcher-qml-windowed_SideBar_qml.cpp.aotstats

qml/windowed/.rcc/qmlcache/launcher-qml-windowed_BottomBar_qml.cpp: /usr/lib/qt6/libexec/qmlcachegen
qml/windowed/.rcc/qmlcache/launcher-qml-windowed_BottomBar_qml.cpp: /home/<USER>/Desktop/myrepo/dde-launchpad/qml/windowed/BottomBar.qml
qml/windowed/.rcc/qmlcache/launcher-qml-windowed_BottomBar_qml.cpp: qml/windowed/.qt/rcc/qmake_org_deepin_launchpad_windowed.qrc
qml/windowed/.rcc/qmlcache/launcher-qml-windowed_BottomBar_qml.cpp: qml/windowed/.qt/rcc/launcher-qml-windowed_raw_qml_0.qrc
qml/windowed/.rcc/qmlcache/launcher-qml-windowed_BottomBar_qml.cpp: qml/windowed/launcher-qml-windowed.qmltypes
qml/windowed/.rcc/qmlcache/launcher-qml-windowed_BottomBar_qml.cpp: qml/windowed/qmldir
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Generating .rcc/qmlcache/launcher-qml-windowed_BottomBar_qml.cpp, .rcc/qmlcache/launcher-qml-windowed_BottomBar_qml.cpp.aotstats"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed && /usr/bin/cmake -E make_directory /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/.rcc/qmlcache
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed && /usr/lib/qt6/libexec/qmlcachegen --bare --resource-path /qt/qml/org/deepin/launchpad/windowed/BottomBar.qml -I /usr/lib/x86_64-linux-gnu/qt6/qml -i /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/qmldir --resource /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/.qt/rcc/qmake_org_deepin_launchpad_windowed.qrc --resource /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/.qt/rcc/launcher-qml-windowed_raw_qml_0.qrc --dump-aot-stats "--module-id=org.deepin.launchpad.windowed(launcher-qml-windowed)" -o /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/.rcc/qmlcache/launcher-qml-windowed_BottomBar_qml.cpp /home/<USER>/Desktop/myrepo/dde-launchpad/qml/windowed/BottomBar.qml

qml/windowed/.rcc/qmlcache/launcher-qml-windowed_BottomBar_qml.cpp.aotstats: qml/windowed/.rcc/qmlcache/launcher-qml-windowed_BottomBar_qml.cpp
	@$(CMAKE_COMMAND) -E touch_nocreate qml/windowed/.rcc/qmlcache/launcher-qml-windowed_BottomBar_qml.cpp.aotstats

qml/windowed/.rcc/qmlcache/launcher-qml-windowed_AlphabetCategoryPopup_qml.cpp: /usr/lib/qt6/libexec/qmlcachegen
qml/windowed/.rcc/qmlcache/launcher-qml-windowed_AlphabetCategoryPopup_qml.cpp: /home/<USER>/Desktop/myrepo/dde-launchpad/qml/windowed/AlphabetCategoryPopup.qml
qml/windowed/.rcc/qmlcache/launcher-qml-windowed_AlphabetCategoryPopup_qml.cpp: qml/windowed/.qt/rcc/qmake_org_deepin_launchpad_windowed.qrc
qml/windowed/.rcc/qmlcache/launcher-qml-windowed_AlphabetCategoryPopup_qml.cpp: qml/windowed/.qt/rcc/launcher-qml-windowed_raw_qml_0.qrc
qml/windowed/.rcc/qmlcache/launcher-qml-windowed_AlphabetCategoryPopup_qml.cpp: qml/windowed/launcher-qml-windowed.qmltypes
qml/windowed/.rcc/qmlcache/launcher-qml-windowed_AlphabetCategoryPopup_qml.cpp: qml/windowed/qmldir
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/CMakeFiles --progress-num=$(CMAKE_PROGRESS_8) "Generating .rcc/qmlcache/launcher-qml-windowed_AlphabetCategoryPopup_qml.cpp, .rcc/qmlcache/launcher-qml-windowed_AlphabetCategoryPopup_qml.cpp.aotstats"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed && /usr/bin/cmake -E make_directory /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/.rcc/qmlcache
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed && /usr/lib/qt6/libexec/qmlcachegen --bare --resource-path /qt/qml/org/deepin/launchpad/windowed/AlphabetCategoryPopup.qml -I /usr/lib/x86_64-linux-gnu/qt6/qml -i /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/qmldir --resource /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/.qt/rcc/qmake_org_deepin_launchpad_windowed.qrc --resource /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/.qt/rcc/launcher-qml-windowed_raw_qml_0.qrc --dump-aot-stats "--module-id=org.deepin.launchpad.windowed(launcher-qml-windowed)" -o /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/.rcc/qmlcache/launcher-qml-windowed_AlphabetCategoryPopup_qml.cpp /home/<USER>/Desktop/myrepo/dde-launchpad/qml/windowed/AlphabetCategoryPopup.qml

qml/windowed/.rcc/qmlcache/launcher-qml-windowed_AlphabetCategoryPopup_qml.cpp.aotstats: qml/windowed/.rcc/qmlcache/launcher-qml-windowed_AlphabetCategoryPopup_qml.cpp
	@$(CMAKE_COMMAND) -E touch_nocreate qml/windowed/.rcc/qmlcache/launcher-qml-windowed_AlphabetCategoryPopup_qml.cpp.aotstats

qml/windowed/.rcc/qmlcache/launcher-qml-windowed_AppList_qml.cpp: /usr/lib/qt6/libexec/qmlcachegen
qml/windowed/.rcc/qmlcache/launcher-qml-windowed_AppList_qml.cpp: /home/<USER>/Desktop/myrepo/dde-launchpad/qml/windowed/AppList.qml
qml/windowed/.rcc/qmlcache/launcher-qml-windowed_AppList_qml.cpp: qml/windowed/.qt/rcc/qmake_org_deepin_launchpad_windowed.qrc
qml/windowed/.rcc/qmlcache/launcher-qml-windowed_AppList_qml.cpp: qml/windowed/.qt/rcc/launcher-qml-windowed_raw_qml_0.qrc
qml/windowed/.rcc/qmlcache/launcher-qml-windowed_AppList_qml.cpp: qml/windowed/launcher-qml-windowed.qmltypes
qml/windowed/.rcc/qmlcache/launcher-qml-windowed_AppList_qml.cpp: qml/windowed/qmldir
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/CMakeFiles --progress-num=$(CMAKE_PROGRESS_9) "Generating .rcc/qmlcache/launcher-qml-windowed_AppList_qml.cpp, .rcc/qmlcache/launcher-qml-windowed_AppList_qml.cpp.aotstats"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed && /usr/bin/cmake -E make_directory /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/.rcc/qmlcache
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed && /usr/lib/qt6/libexec/qmlcachegen --bare --resource-path /qt/qml/org/deepin/launchpad/windowed/AppList.qml -I /usr/lib/x86_64-linux-gnu/qt6/qml -i /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/qmldir --resource /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/.qt/rcc/qmake_org_deepin_launchpad_windowed.qrc --resource /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/.qt/rcc/launcher-qml-windowed_raw_qml_0.qrc --dump-aot-stats "--module-id=org.deepin.launchpad.windowed(launcher-qml-windowed)" -o /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/.rcc/qmlcache/launcher-qml-windowed_AppList_qml.cpp /home/<USER>/Desktop/myrepo/dde-launchpad/qml/windowed/AppList.qml

qml/windowed/.rcc/qmlcache/launcher-qml-windowed_AppList_qml.cpp.aotstats: qml/windowed/.rcc/qmlcache/launcher-qml-windowed_AppList_qml.cpp
	@$(CMAKE_COMMAND) -E touch_nocreate qml/windowed/.rcc/qmlcache/launcher-qml-windowed_AppList_qml.cpp.aotstats

qml/windowed/.rcc/qmlcache/launcher-qml-windowed_AppListView_qml.cpp: /usr/lib/qt6/libexec/qmlcachegen
qml/windowed/.rcc/qmlcache/launcher-qml-windowed_AppListView_qml.cpp: /home/<USER>/Desktop/myrepo/dde-launchpad/qml/windowed/AppListView.qml
qml/windowed/.rcc/qmlcache/launcher-qml-windowed_AppListView_qml.cpp: qml/windowed/.qt/rcc/qmake_org_deepin_launchpad_windowed.qrc
qml/windowed/.rcc/qmlcache/launcher-qml-windowed_AppListView_qml.cpp: qml/windowed/.qt/rcc/launcher-qml-windowed_raw_qml_0.qrc
qml/windowed/.rcc/qmlcache/launcher-qml-windowed_AppListView_qml.cpp: qml/windowed/launcher-qml-windowed.qmltypes
qml/windowed/.rcc/qmlcache/launcher-qml-windowed_AppListView_qml.cpp: qml/windowed/qmldir
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/CMakeFiles --progress-num=$(CMAKE_PROGRESS_10) "Generating .rcc/qmlcache/launcher-qml-windowed_AppListView_qml.cpp, .rcc/qmlcache/launcher-qml-windowed_AppListView_qml.cpp.aotstats"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed && /usr/bin/cmake -E make_directory /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/.rcc/qmlcache
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed && /usr/lib/qt6/libexec/qmlcachegen --bare --resource-path /qt/qml/org/deepin/launchpad/windowed/AppListView.qml -I /usr/lib/x86_64-linux-gnu/qt6/qml -i /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/qmldir --resource /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/.qt/rcc/qmake_org_deepin_launchpad_windowed.qrc --resource /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/.qt/rcc/launcher-qml-windowed_raw_qml_0.qrc --dump-aot-stats "--module-id=org.deepin.launchpad.windowed(launcher-qml-windowed)" -o /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/.rcc/qmlcache/launcher-qml-windowed_AppListView_qml.cpp /home/<USER>/Desktop/myrepo/dde-launchpad/qml/windowed/AppListView.qml

qml/windowed/.rcc/qmlcache/launcher-qml-windowed_AppListView_qml.cpp.aotstats: qml/windowed/.rcc/qmlcache/launcher-qml-windowed_AppListView_qml.cpp
	@$(CMAKE_COMMAND) -E touch_nocreate qml/windowed/.rcc/qmlcache/launcher-qml-windowed_AppListView_qml.cpp.aotstats

qml/windowed/.rcc/qmlcache/launcher-qml-windowed_IconItemDelegate_qml.cpp: /usr/lib/qt6/libexec/qmlcachegen
qml/windowed/.rcc/qmlcache/launcher-qml-windowed_IconItemDelegate_qml.cpp: /home/<USER>/Desktop/myrepo/dde-launchpad/qml/windowed/IconItemDelegate.qml
qml/windowed/.rcc/qmlcache/launcher-qml-windowed_IconItemDelegate_qml.cpp: qml/windowed/.qt/rcc/qmake_org_deepin_launchpad_windowed.qrc
qml/windowed/.rcc/qmlcache/launcher-qml-windowed_IconItemDelegate_qml.cpp: qml/windowed/.qt/rcc/launcher-qml-windowed_raw_qml_0.qrc
qml/windowed/.rcc/qmlcache/launcher-qml-windowed_IconItemDelegate_qml.cpp: qml/windowed/launcher-qml-windowed.qmltypes
qml/windowed/.rcc/qmlcache/launcher-qml-windowed_IconItemDelegate_qml.cpp: qml/windowed/qmldir
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/CMakeFiles --progress-num=$(CMAKE_PROGRESS_11) "Generating .rcc/qmlcache/launcher-qml-windowed_IconItemDelegate_qml.cpp, .rcc/qmlcache/launcher-qml-windowed_IconItemDelegate_qml.cpp.aotstats"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed && /usr/bin/cmake -E make_directory /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/.rcc/qmlcache
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed && /usr/lib/qt6/libexec/qmlcachegen --bare --resource-path /qt/qml/org/deepin/launchpad/windowed/IconItemDelegate.qml -I /usr/lib/x86_64-linux-gnu/qt6/qml -i /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/qmldir --resource /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/.qt/rcc/qmake_org_deepin_launchpad_windowed.qrc --resource /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/.qt/rcc/launcher-qml-windowed_raw_qml_0.qrc --dump-aot-stats "--module-id=org.deepin.launchpad.windowed(launcher-qml-windowed)" -o /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/.rcc/qmlcache/launcher-qml-windowed_IconItemDelegate_qml.cpp /home/<USER>/Desktop/myrepo/dde-launchpad/qml/windowed/IconItemDelegate.qml

qml/windowed/.rcc/qmlcache/launcher-qml-windowed_IconItemDelegate_qml.cpp.aotstats: qml/windowed/.rcc/qmlcache/launcher-qml-windowed_IconItemDelegate_qml.cpp
	@$(CMAKE_COMMAND) -E touch_nocreate qml/windowed/.rcc/qmlcache/launcher-qml-windowed_IconItemDelegate_qml.cpp.aotstats

qml/windowed/.rcc/qmlcache/launcher-qml-windowed_FreeSortListView_qml.cpp: /usr/lib/qt6/libexec/qmlcachegen
qml/windowed/.rcc/qmlcache/launcher-qml-windowed_FreeSortListView_qml.cpp: /home/<USER>/Desktop/myrepo/dde-launchpad/qml/windowed/FreeSortListView.qml
qml/windowed/.rcc/qmlcache/launcher-qml-windowed_FreeSortListView_qml.cpp: qml/windowed/.qt/rcc/qmake_org_deepin_launchpad_windowed.qrc
qml/windowed/.rcc/qmlcache/launcher-qml-windowed_FreeSortListView_qml.cpp: qml/windowed/.qt/rcc/launcher-qml-windowed_raw_qml_0.qrc
qml/windowed/.rcc/qmlcache/launcher-qml-windowed_FreeSortListView_qml.cpp: qml/windowed/launcher-qml-windowed.qmltypes
qml/windowed/.rcc/qmlcache/launcher-qml-windowed_FreeSortListView_qml.cpp: qml/windowed/qmldir
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/CMakeFiles --progress-num=$(CMAKE_PROGRESS_12) "Generating .rcc/qmlcache/launcher-qml-windowed_FreeSortListView_qml.cpp, .rcc/qmlcache/launcher-qml-windowed_FreeSortListView_qml.cpp.aotstats"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed && /usr/bin/cmake -E make_directory /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/.rcc/qmlcache
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed && /usr/lib/qt6/libexec/qmlcachegen --bare --resource-path /qt/qml/org/deepin/launchpad/windowed/FreeSortListView.qml -I /usr/lib/x86_64-linux-gnu/qt6/qml -i /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/qmldir --resource /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/.qt/rcc/qmake_org_deepin_launchpad_windowed.qrc --resource /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/.qt/rcc/launcher-qml-windowed_raw_qml_0.qrc --dump-aot-stats "--module-id=org.deepin.launchpad.windowed(launcher-qml-windowed)" -o /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/.rcc/qmlcache/launcher-qml-windowed_FreeSortListView_qml.cpp /home/<USER>/Desktop/myrepo/dde-launchpad/qml/windowed/FreeSortListView.qml

qml/windowed/.rcc/qmlcache/launcher-qml-windowed_FreeSortListView_qml.cpp.aotstats: qml/windowed/.rcc/qmlcache/launcher-qml-windowed_FreeSortListView_qml.cpp
	@$(CMAKE_COMMAND) -E touch_nocreate qml/windowed/.rcc/qmlcache/launcher-qml-windowed_FreeSortListView_qml.cpp.aotstats

qml/windowed/.rcc/qmlcache/launcher-qml-windowed_GridViewContainer_qml.cpp: /usr/lib/qt6/libexec/qmlcachegen
qml/windowed/.rcc/qmlcache/launcher-qml-windowed_GridViewContainer_qml.cpp: /home/<USER>/Desktop/myrepo/dde-launchpad/qml/windowed/GridViewContainer.qml
qml/windowed/.rcc/qmlcache/launcher-qml-windowed_GridViewContainer_qml.cpp: qml/windowed/.qt/rcc/qmake_org_deepin_launchpad_windowed.qrc
qml/windowed/.rcc/qmlcache/launcher-qml-windowed_GridViewContainer_qml.cpp: qml/windowed/.qt/rcc/launcher-qml-windowed_raw_qml_0.qrc
qml/windowed/.rcc/qmlcache/launcher-qml-windowed_GridViewContainer_qml.cpp: qml/windowed/launcher-qml-windowed.qmltypes
qml/windowed/.rcc/qmlcache/launcher-qml-windowed_GridViewContainer_qml.cpp: qml/windowed/qmldir
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/CMakeFiles --progress-num=$(CMAKE_PROGRESS_13) "Generating .rcc/qmlcache/launcher-qml-windowed_GridViewContainer_qml.cpp, .rcc/qmlcache/launcher-qml-windowed_GridViewContainer_qml.cpp.aotstats"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed && /usr/bin/cmake -E make_directory /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/.rcc/qmlcache
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed && /usr/lib/qt6/libexec/qmlcachegen --bare --resource-path /qt/qml/org/deepin/launchpad/windowed/GridViewContainer.qml -I /usr/lib/x86_64-linux-gnu/qt6/qml -i /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/qmldir --resource /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/.qt/rcc/qmake_org_deepin_launchpad_windowed.qrc --resource /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/.qt/rcc/launcher-qml-windowed_raw_qml_0.qrc --dump-aot-stats "--module-id=org.deepin.launchpad.windowed(launcher-qml-windowed)" -o /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/.rcc/qmlcache/launcher-qml-windowed_GridViewContainer_qml.cpp /home/<USER>/Desktop/myrepo/dde-launchpad/qml/windowed/GridViewContainer.qml

qml/windowed/.rcc/qmlcache/launcher-qml-windowed_GridViewContainer_qml.cpp.aotstats: qml/windowed/.rcc/qmlcache/launcher-qml-windowed_GridViewContainer_qml.cpp
	@$(CMAKE_COMMAND) -E touch_nocreate qml/windowed/.rcc/qmlcache/launcher-qml-windowed_GridViewContainer_qml.cpp.aotstats

qml/windowed/.rcc/qmlcache/launcher-qml-windowed_AnalysisView_qml.cpp: /usr/lib/qt6/libexec/qmlcachegen
qml/windowed/.rcc/qmlcache/launcher-qml-windowed_AnalysisView_qml.cpp: /home/<USER>/Desktop/myrepo/dde-launchpad/qml/windowed/AnalysisView.qml
qml/windowed/.rcc/qmlcache/launcher-qml-windowed_AnalysisView_qml.cpp: qml/windowed/.qt/rcc/qmake_org_deepin_launchpad_windowed.qrc
qml/windowed/.rcc/qmlcache/launcher-qml-windowed_AnalysisView_qml.cpp: qml/windowed/.qt/rcc/launcher-qml-windowed_raw_qml_0.qrc
qml/windowed/.rcc/qmlcache/launcher-qml-windowed_AnalysisView_qml.cpp: qml/windowed/launcher-qml-windowed.qmltypes
qml/windowed/.rcc/qmlcache/launcher-qml-windowed_AnalysisView_qml.cpp: qml/windowed/qmldir
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/CMakeFiles --progress-num=$(CMAKE_PROGRESS_14) "Generating .rcc/qmlcache/launcher-qml-windowed_AnalysisView_qml.cpp, .rcc/qmlcache/launcher-qml-windowed_AnalysisView_qml.cpp.aotstats"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed && /usr/bin/cmake -E make_directory /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/.rcc/qmlcache
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed && /usr/lib/qt6/libexec/qmlcachegen --bare --resource-path /qt/qml/org/deepin/launchpad/windowed/AnalysisView.qml -I /usr/lib/x86_64-linux-gnu/qt6/qml -i /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/qmldir --resource /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/.qt/rcc/qmake_org_deepin_launchpad_windowed.qrc --resource /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/.qt/rcc/launcher-qml-windowed_raw_qml_0.qrc --dump-aot-stats "--module-id=org.deepin.launchpad.windowed(launcher-qml-windowed)" -o /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/.rcc/qmlcache/launcher-qml-windowed_AnalysisView_qml.cpp /home/<USER>/Desktop/myrepo/dde-launchpad/qml/windowed/AnalysisView.qml

qml/windowed/.rcc/qmlcache/launcher-qml-windowed_AnalysisView_qml.cpp.aotstats: qml/windowed/.rcc/qmlcache/launcher-qml-windowed_AnalysisView_qml.cpp
	@$(CMAKE_COMMAND) -E touch_nocreate qml/windowed/.rcc/qmlcache/launcher-qml-windowed_AnalysisView_qml.cpp.aotstats

qml/windowed/.rcc/qmlcache/launcher-qml-windowed_RecentlyInstalledView_qml.cpp: /usr/lib/qt6/libexec/qmlcachegen
qml/windowed/.rcc/qmlcache/launcher-qml-windowed_RecentlyInstalledView_qml.cpp: /home/<USER>/Desktop/myrepo/dde-launchpad/qml/windowed/RecentlyInstalledView.qml
qml/windowed/.rcc/qmlcache/launcher-qml-windowed_RecentlyInstalledView_qml.cpp: qml/windowed/.qt/rcc/qmake_org_deepin_launchpad_windowed.qrc
qml/windowed/.rcc/qmlcache/launcher-qml-windowed_RecentlyInstalledView_qml.cpp: qml/windowed/.qt/rcc/launcher-qml-windowed_raw_qml_0.qrc
qml/windowed/.rcc/qmlcache/launcher-qml-windowed_RecentlyInstalledView_qml.cpp: qml/windowed/launcher-qml-windowed.qmltypes
qml/windowed/.rcc/qmlcache/launcher-qml-windowed_RecentlyInstalledView_qml.cpp: qml/windowed/qmldir
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/CMakeFiles --progress-num=$(CMAKE_PROGRESS_15) "Generating .rcc/qmlcache/launcher-qml-windowed_RecentlyInstalledView_qml.cpp, .rcc/qmlcache/launcher-qml-windowed_RecentlyInstalledView_qml.cpp.aotstats"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed && /usr/bin/cmake -E make_directory /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/.rcc/qmlcache
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed && /usr/lib/qt6/libexec/qmlcachegen --bare --resource-path /qt/qml/org/deepin/launchpad/windowed/RecentlyInstalledView.qml -I /usr/lib/x86_64-linux-gnu/qt6/qml -i /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/qmldir --resource /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/.qt/rcc/qmake_org_deepin_launchpad_windowed.qrc --resource /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/.qt/rcc/launcher-qml-windowed_raw_qml_0.qrc --dump-aot-stats "--module-id=org.deepin.launchpad.windowed(launcher-qml-windowed)" -o /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/.rcc/qmlcache/launcher-qml-windowed_RecentlyInstalledView_qml.cpp /home/<USER>/Desktop/myrepo/dde-launchpad/qml/windowed/RecentlyInstalledView.qml

qml/windowed/.rcc/qmlcache/launcher-qml-windowed_RecentlyInstalledView_qml.cpp.aotstats: qml/windowed/.rcc/qmlcache/launcher-qml-windowed_RecentlyInstalledView_qml.cpp
	@$(CMAKE_COMMAND) -E touch_nocreate qml/windowed/.rcc/qmlcache/launcher-qml-windowed_RecentlyInstalledView_qml.cpp.aotstats

qml/windowed/.rcc/qmlcache/launcher-qml-windowed_FrequentlyUsedView_qml.cpp: /usr/lib/qt6/libexec/qmlcachegen
qml/windowed/.rcc/qmlcache/launcher-qml-windowed_FrequentlyUsedView_qml.cpp: /home/<USER>/Desktop/myrepo/dde-launchpad/qml/windowed/FrequentlyUsedView.qml
qml/windowed/.rcc/qmlcache/launcher-qml-windowed_FrequentlyUsedView_qml.cpp: qml/windowed/.qt/rcc/qmake_org_deepin_launchpad_windowed.qrc
qml/windowed/.rcc/qmlcache/launcher-qml-windowed_FrequentlyUsedView_qml.cpp: qml/windowed/.qt/rcc/launcher-qml-windowed_raw_qml_0.qrc
qml/windowed/.rcc/qmlcache/launcher-qml-windowed_FrequentlyUsedView_qml.cpp: qml/windowed/launcher-qml-windowed.qmltypes
qml/windowed/.rcc/qmlcache/launcher-qml-windowed_FrequentlyUsedView_qml.cpp: qml/windowed/qmldir
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/CMakeFiles --progress-num=$(CMAKE_PROGRESS_16) "Generating .rcc/qmlcache/launcher-qml-windowed_FrequentlyUsedView_qml.cpp, .rcc/qmlcache/launcher-qml-windowed_FrequentlyUsedView_qml.cpp.aotstats"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed && /usr/bin/cmake -E make_directory /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/.rcc/qmlcache
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed && /usr/lib/qt6/libexec/qmlcachegen --bare --resource-path /qt/qml/org/deepin/launchpad/windowed/FrequentlyUsedView.qml -I /usr/lib/x86_64-linux-gnu/qt6/qml -i /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/qmldir --resource /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/.qt/rcc/qmake_org_deepin_launchpad_windowed.qrc --resource /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/.qt/rcc/launcher-qml-windowed_raw_qml_0.qrc --dump-aot-stats "--module-id=org.deepin.launchpad.windowed(launcher-qml-windowed)" -o /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/.rcc/qmlcache/launcher-qml-windowed_FrequentlyUsedView_qml.cpp /home/<USER>/Desktop/myrepo/dde-launchpad/qml/windowed/FrequentlyUsedView.qml

qml/windowed/.rcc/qmlcache/launcher-qml-windowed_FrequentlyUsedView_qml.cpp.aotstats: qml/windowed/.rcc/qmlcache/launcher-qml-windowed_FrequentlyUsedView_qml.cpp
	@$(CMAKE_COMMAND) -E touch_nocreate qml/windowed/.rcc/qmlcache/launcher-qml-windowed_FrequentlyUsedView_qml.cpp.aotstats

qml/windowed/.rcc/qmlcache/launcher-qml-windowed_SearchResultView_qml.cpp: /usr/lib/qt6/libexec/qmlcachegen
qml/windowed/.rcc/qmlcache/launcher-qml-windowed_SearchResultView_qml.cpp: /home/<USER>/Desktop/myrepo/dde-launchpad/qml/windowed/SearchResultView.qml
qml/windowed/.rcc/qmlcache/launcher-qml-windowed_SearchResultView_qml.cpp: qml/windowed/.qt/rcc/qmake_org_deepin_launchpad_windowed.qrc
qml/windowed/.rcc/qmlcache/launcher-qml-windowed_SearchResultView_qml.cpp: qml/windowed/.qt/rcc/launcher-qml-windowed_raw_qml_0.qrc
qml/windowed/.rcc/qmlcache/launcher-qml-windowed_SearchResultView_qml.cpp: qml/windowed/launcher-qml-windowed.qmltypes
qml/windowed/.rcc/qmlcache/launcher-qml-windowed_SearchResultView_qml.cpp: qml/windowed/qmldir
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/CMakeFiles --progress-num=$(CMAKE_PROGRESS_17) "Generating .rcc/qmlcache/launcher-qml-windowed_SearchResultView_qml.cpp, .rcc/qmlcache/launcher-qml-windowed_SearchResultView_qml.cpp.aotstats"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed && /usr/bin/cmake -E make_directory /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/.rcc/qmlcache
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed && /usr/lib/qt6/libexec/qmlcachegen --bare --resource-path /qt/qml/org/deepin/launchpad/windowed/SearchResultView.qml -I /usr/lib/x86_64-linux-gnu/qt6/qml -i /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/qmldir --resource /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/.qt/rcc/qmake_org_deepin_launchpad_windowed.qrc --resource /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/.qt/rcc/launcher-qml-windowed_raw_qml_0.qrc --dump-aot-stats "--module-id=org.deepin.launchpad.windowed(launcher-qml-windowed)" -o /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/.rcc/qmlcache/launcher-qml-windowed_SearchResultView_qml.cpp /home/<USER>/Desktop/myrepo/dde-launchpad/qml/windowed/SearchResultView.qml

qml/windowed/.rcc/qmlcache/launcher-qml-windowed_SearchResultView_qml.cpp.aotstats: qml/windowed/.rcc/qmlcache/launcher-qml-windowed_SearchResultView_qml.cpp
	@$(CMAKE_COMMAND) -E touch_nocreate qml/windowed/.rcc/qmlcache/launcher-qml-windowed_SearchResultView_qml.cpp.aotstats

qml/windowed/.rcc/qmlcache/launcher-qml-windowed_ItemBackground_qml.cpp: /usr/lib/qt6/libexec/qmlcachegen
qml/windowed/.rcc/qmlcache/launcher-qml-windowed_ItemBackground_qml.cpp: /home/<USER>/Desktop/myrepo/dde-launchpad/qml/windowed/ItemBackground.qml
qml/windowed/.rcc/qmlcache/launcher-qml-windowed_ItemBackground_qml.cpp: qml/windowed/.qt/rcc/qmake_org_deepin_launchpad_windowed.qrc
qml/windowed/.rcc/qmlcache/launcher-qml-windowed_ItemBackground_qml.cpp: qml/windowed/.qt/rcc/launcher-qml-windowed_raw_qml_0.qrc
qml/windowed/.rcc/qmlcache/launcher-qml-windowed_ItemBackground_qml.cpp: qml/windowed/launcher-qml-windowed.qmltypes
qml/windowed/.rcc/qmlcache/launcher-qml-windowed_ItemBackground_qml.cpp: qml/windowed/qmldir
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/CMakeFiles --progress-num=$(CMAKE_PROGRESS_18) "Generating .rcc/qmlcache/launcher-qml-windowed_ItemBackground_qml.cpp, .rcc/qmlcache/launcher-qml-windowed_ItemBackground_qml.cpp.aotstats"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed && /usr/bin/cmake -E make_directory /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/.rcc/qmlcache
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed && /usr/lib/qt6/libexec/qmlcachegen --bare --resource-path /qt/qml/org/deepin/launchpad/windowed/ItemBackground.qml -I /usr/lib/x86_64-linux-gnu/qt6/qml -i /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/qmldir --resource /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/.qt/rcc/qmake_org_deepin_launchpad_windowed.qrc --resource /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/.qt/rcc/launcher-qml-windowed_raw_qml_0.qrc --dump-aot-stats "--module-id=org.deepin.launchpad.windowed(launcher-qml-windowed)" -o /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/.rcc/qmlcache/launcher-qml-windowed_ItemBackground_qml.cpp /home/<USER>/Desktop/myrepo/dde-launchpad/qml/windowed/ItemBackground.qml

qml/windowed/.rcc/qmlcache/launcher-qml-windowed_ItemBackground_qml.cpp.aotstats: qml/windowed/.rcc/qmlcache/launcher-qml-windowed_ItemBackground_qml.cpp
	@$(CMAKE_COMMAND) -E touch_nocreate qml/windowed/.rcc/qmlcache/launcher-qml-windowed_ItemBackground_qml.cpp.aotstats

qml/windowed/.qt/rcc/qrc_launcher-qml-windowed_raw_qml_0.cpp: /home/<USER>/Desktop/myrepo/dde-launchpad/qml/windowed/WindowedFrame.qml
qml/windowed/.qt/rcc/qrc_launcher-qml-windowed_raw_qml_0.cpp: /home/<USER>/Desktop/myrepo/dde-launchpad/qml/windowed/SideBar.qml
qml/windowed/.qt/rcc/qrc_launcher-qml-windowed_raw_qml_0.cpp: /home/<USER>/Desktop/myrepo/dde-launchpad/qml/windowed/BottomBar.qml
qml/windowed/.qt/rcc/qrc_launcher-qml-windowed_raw_qml_0.cpp: /home/<USER>/Desktop/myrepo/dde-launchpad/qml/windowed/AlphabetCategoryPopup.qml
qml/windowed/.qt/rcc/qrc_launcher-qml-windowed_raw_qml_0.cpp: /home/<USER>/Desktop/myrepo/dde-launchpad/qml/windowed/AppList.qml
qml/windowed/.qt/rcc/qrc_launcher-qml-windowed_raw_qml_0.cpp: /home/<USER>/Desktop/myrepo/dde-launchpad/qml/windowed/AppListView.qml
qml/windowed/.qt/rcc/qrc_launcher-qml-windowed_raw_qml_0.cpp: /home/<USER>/Desktop/myrepo/dde-launchpad/qml/windowed/IconItemDelegate.qml
qml/windowed/.qt/rcc/qrc_launcher-qml-windowed_raw_qml_0.cpp: /home/<USER>/Desktop/myrepo/dde-launchpad/qml/windowed/FreeSortListView.qml
qml/windowed/.qt/rcc/qrc_launcher-qml-windowed_raw_qml_0.cpp: /home/<USER>/Desktop/myrepo/dde-launchpad/qml/windowed/GridViewContainer.qml
qml/windowed/.qt/rcc/qrc_launcher-qml-windowed_raw_qml_0.cpp: /home/<USER>/Desktop/myrepo/dde-launchpad/qml/windowed/AnalysisView.qml
qml/windowed/.qt/rcc/qrc_launcher-qml-windowed_raw_qml_0.cpp: /home/<USER>/Desktop/myrepo/dde-launchpad/qml/windowed/RecentlyInstalledView.qml
qml/windowed/.qt/rcc/qrc_launcher-qml-windowed_raw_qml_0.cpp: /home/<USER>/Desktop/myrepo/dde-launchpad/qml/windowed/FrequentlyUsedView.qml
qml/windowed/.qt/rcc/qrc_launcher-qml-windowed_raw_qml_0.cpp: /home/<USER>/Desktop/myrepo/dde-launchpad/qml/windowed/SearchResultView.qml
qml/windowed/.qt/rcc/qrc_launcher-qml-windowed_raw_qml_0.cpp: /home/<USER>/Desktop/myrepo/dde-launchpad/qml/windowed/ItemBackground.qml
qml/windowed/.qt/rcc/qrc_launcher-qml-windowed_raw_qml_0.cpp: qml/windowed/.qt/rcc/launcher-qml-windowed_raw_qml_0.qrc
qml/windowed/.qt/rcc/qrc_launcher-qml-windowed_raw_qml_0.cpp: /usr/lib/qt6/libexec/rcc
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/CMakeFiles --progress-num=$(CMAKE_PROGRESS_19) "Running rcc for resource launcher-qml-windowed_raw_qml_0"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed && /usr/lib/qt6/libexec/rcc --output /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/.qt/rcc/qrc_launcher-qml-windowed_raw_qml_0.cpp --name launcher-qml-windowed_raw_qml_0 /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/.qt/rcc/launcher-qml-windowed_raw_qml_0.qrc

qml/windowed/launcher-qml-windowed_autogen/timestamp: /usr/lib/qt6/libexec/moc
qml/windowed/launcher-qml-windowed_autogen/timestamp: qml/windowed/CMakeFiles/launcher-qml-windowed.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/CMakeFiles --progress-num=$(CMAKE_PROGRESS_20) "Automatic MOC for target launcher-qml-windowed"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed && /usr/bin/cmake -E cmake_autogen /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/CMakeFiles/launcher-qml-windowed_autogen.dir/AutogenInfo.json None
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed && /usr/bin/cmake -E touch /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/launcher-qml-windowed_autogen/timestamp

qml/windowed/meta_types/launcher-qml-windowed_json_file_list.txt: /usr/lib/qt6/libexec/cmake_automoc_parser
qml/windowed/meta_types/launcher-qml-windowed_json_file_list.txt: qml/windowed/launcher-qml-windowed_autogen/timestamp
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/CMakeFiles --progress-num=$(CMAKE_PROGRESS_21) "Running AUTOMOC file extraction for target launcher-qml-windowed"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed && /usr/lib/qt6/libexec/cmake_automoc_parser --cmake-autogen-cache-file /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/CMakeFiles/launcher-qml-windowed_autogen.dir/ParseCache.txt --cmake-autogen-info-file /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/CMakeFiles/launcher-qml-windowed_autogen.dir/AutogenInfo.json --output-file-path /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/meta_types/launcher-qml-windowed_json_file_list.txt --timestamp-file-path /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/meta_types/launcher-qml-windowed_json_file_list.txt.timestamp --cmake-autogen-include-dir-path /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/launcher-qml-windowed_autogen/include

qml/windowed/meta_types/qt6launcher-qml-windowed_none_metatypes.json: qml/windowed/meta_types/qt6launcher-qml-windowed_none_metatypes.json.gen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/CMakeFiles --progress-num=$(CMAKE_PROGRESS_22) "Generating meta_types/qt6launcher-qml-windowed_none_metatypes.json"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed && /usr/bin/cmake -E true

qml/windowed/CMakeFiles/launcher-qml-windowed.dir/codegen:
.PHONY : qml/windowed/CMakeFiles/launcher-qml-windowed.dir/codegen

qml/windowed/CMakeFiles/launcher-qml-windowed.dir/launcher-qml-windowed_autogen/mocs_compilation.cpp.o: qml/windowed/CMakeFiles/launcher-qml-windowed.dir/flags.make
qml/windowed/CMakeFiles/launcher-qml-windowed.dir/launcher-qml-windowed_autogen/mocs_compilation.cpp.o: qml/windowed/launcher-qml-windowed_autogen/mocs_compilation.cpp
qml/windowed/CMakeFiles/launcher-qml-windowed.dir/launcher-qml-windowed_autogen/mocs_compilation.cpp.o: qml/windowed/CMakeFiles/launcher-qml-windowed.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/CMakeFiles --progress-num=$(CMAKE_PROGRESS_23) "Building CXX object qml/windowed/CMakeFiles/launcher-qml-windowed.dir/launcher-qml-windowed_autogen/mocs_compilation.cpp.o"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT qml/windowed/CMakeFiles/launcher-qml-windowed.dir/launcher-qml-windowed_autogen/mocs_compilation.cpp.o -MF CMakeFiles/launcher-qml-windowed.dir/launcher-qml-windowed_autogen/mocs_compilation.cpp.o.d -o CMakeFiles/launcher-qml-windowed.dir/launcher-qml-windowed_autogen/mocs_compilation.cpp.o -c /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/launcher-qml-windowed_autogen/mocs_compilation.cpp

qml/windowed/CMakeFiles/launcher-qml-windowed.dir/launcher-qml-windowed_autogen/mocs_compilation.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/launcher-qml-windowed.dir/launcher-qml-windowed_autogen/mocs_compilation.cpp.i"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/launcher-qml-windowed_autogen/mocs_compilation.cpp > CMakeFiles/launcher-qml-windowed.dir/launcher-qml-windowed_autogen/mocs_compilation.cpp.i

qml/windowed/CMakeFiles/launcher-qml-windowed.dir/launcher-qml-windowed_autogen/mocs_compilation.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/launcher-qml-windowed.dir/launcher-qml-windowed_autogen/mocs_compilation.cpp.s"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/launcher-qml-windowed_autogen/mocs_compilation.cpp -o CMakeFiles/launcher-qml-windowed.dir/launcher-qml-windowed_autogen/mocs_compilation.cpp.s

qml/windowed/CMakeFiles/launcher-qml-windowed.dir/launcher-qml-windowed_org_deepin_launchpad_windowedPlugin.cpp.o: qml/windowed/CMakeFiles/launcher-qml-windowed.dir/flags.make
qml/windowed/CMakeFiles/launcher-qml-windowed.dir/launcher-qml-windowed_org_deepin_launchpad_windowedPlugin.cpp.o: qml/windowed/launcher-qml-windowed_org_deepin_launchpad_windowedPlugin.cpp
qml/windowed/CMakeFiles/launcher-qml-windowed.dir/launcher-qml-windowed_org_deepin_launchpad_windowedPlugin.cpp.o: qml/windowed/CMakeFiles/launcher-qml-windowed.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/CMakeFiles --progress-num=$(CMAKE_PROGRESS_24) "Building CXX object qml/windowed/CMakeFiles/launcher-qml-windowed.dir/launcher-qml-windowed_org_deepin_launchpad_windowedPlugin.cpp.o"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT qml/windowed/CMakeFiles/launcher-qml-windowed.dir/launcher-qml-windowed_org_deepin_launchpad_windowedPlugin.cpp.o -MF CMakeFiles/launcher-qml-windowed.dir/launcher-qml-windowed_org_deepin_launchpad_windowedPlugin.cpp.o.d -o CMakeFiles/launcher-qml-windowed.dir/launcher-qml-windowed_org_deepin_launchpad_windowedPlugin.cpp.o -c /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/launcher-qml-windowed_org_deepin_launchpad_windowedPlugin.cpp

qml/windowed/CMakeFiles/launcher-qml-windowed.dir/launcher-qml-windowed_org_deepin_launchpad_windowedPlugin.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/launcher-qml-windowed.dir/launcher-qml-windowed_org_deepin_launchpad_windowedPlugin.cpp.i"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/launcher-qml-windowed_org_deepin_launchpad_windowedPlugin.cpp > CMakeFiles/launcher-qml-windowed.dir/launcher-qml-windowed_org_deepin_launchpad_windowedPlugin.cpp.i

qml/windowed/CMakeFiles/launcher-qml-windowed.dir/launcher-qml-windowed_org_deepin_launchpad_windowedPlugin.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/launcher-qml-windowed.dir/launcher-qml-windowed_org_deepin_launchpad_windowedPlugin.cpp.s"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/launcher-qml-windowed_org_deepin_launchpad_windowedPlugin.cpp -o CMakeFiles/launcher-qml-windowed.dir/launcher-qml-windowed_org_deepin_launchpad_windowedPlugin.cpp.s

qml/windowed/CMakeFiles/launcher-qml-windowed.dir/launcher-qml-windowed_qmltyperegistrations.cpp.o: qml/windowed/CMakeFiles/launcher-qml-windowed.dir/flags.make
qml/windowed/CMakeFiles/launcher-qml-windowed.dir/launcher-qml-windowed_qmltyperegistrations.cpp.o: qml/windowed/launcher-qml-windowed_qmltyperegistrations.cpp
qml/windowed/CMakeFiles/launcher-qml-windowed.dir/launcher-qml-windowed_qmltyperegistrations.cpp.o: qml/windowed/CMakeFiles/launcher-qml-windowed.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/CMakeFiles --progress-num=$(CMAKE_PROGRESS_25) "Building CXX object qml/windowed/CMakeFiles/launcher-qml-windowed.dir/launcher-qml-windowed_qmltyperegistrations.cpp.o"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT qml/windowed/CMakeFiles/launcher-qml-windowed.dir/launcher-qml-windowed_qmltyperegistrations.cpp.o -MF CMakeFiles/launcher-qml-windowed.dir/launcher-qml-windowed_qmltyperegistrations.cpp.o.d -o CMakeFiles/launcher-qml-windowed.dir/launcher-qml-windowed_qmltyperegistrations.cpp.o -c /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/launcher-qml-windowed_qmltyperegistrations.cpp

qml/windowed/CMakeFiles/launcher-qml-windowed.dir/launcher-qml-windowed_qmltyperegistrations.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/launcher-qml-windowed.dir/launcher-qml-windowed_qmltyperegistrations.cpp.i"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/launcher-qml-windowed_qmltyperegistrations.cpp > CMakeFiles/launcher-qml-windowed.dir/launcher-qml-windowed_qmltyperegistrations.cpp.i

qml/windowed/CMakeFiles/launcher-qml-windowed.dir/launcher-qml-windowed_qmltyperegistrations.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/launcher-qml-windowed.dir/launcher-qml-windowed_qmltyperegistrations.cpp.s"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/launcher-qml-windowed_qmltyperegistrations.cpp -o CMakeFiles/launcher-qml-windowed.dir/launcher-qml-windowed_qmltyperegistrations.cpp.s

qml/windowed/CMakeFiles/launcher-qml-windowed.dir/.qt/rcc/qrc_qmake_org_deepin_launchpad_windowed.cpp.o: qml/windowed/CMakeFiles/launcher-qml-windowed.dir/flags.make
qml/windowed/CMakeFiles/launcher-qml-windowed.dir/.qt/rcc/qrc_qmake_org_deepin_launchpad_windowed.cpp.o: qml/windowed/.qt/rcc/qrc_qmake_org_deepin_launchpad_windowed.cpp
qml/windowed/CMakeFiles/launcher-qml-windowed.dir/.qt/rcc/qrc_qmake_org_deepin_launchpad_windowed.cpp.o: qml/windowed/CMakeFiles/launcher-qml-windowed.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/CMakeFiles --progress-num=$(CMAKE_PROGRESS_26) "Building CXX object qml/windowed/CMakeFiles/launcher-qml-windowed.dir/.qt/rcc/qrc_qmake_org_deepin_launchpad_windowed.cpp.o"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT qml/windowed/CMakeFiles/launcher-qml-windowed.dir/.qt/rcc/qrc_qmake_org_deepin_launchpad_windowed.cpp.o -MF CMakeFiles/launcher-qml-windowed.dir/.qt/rcc/qrc_qmake_org_deepin_launchpad_windowed.cpp.o.d -o CMakeFiles/launcher-qml-windowed.dir/.qt/rcc/qrc_qmake_org_deepin_launchpad_windowed.cpp.o -c /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/.qt/rcc/qrc_qmake_org_deepin_launchpad_windowed.cpp

qml/windowed/CMakeFiles/launcher-qml-windowed.dir/.qt/rcc/qrc_qmake_org_deepin_launchpad_windowed.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/launcher-qml-windowed.dir/.qt/rcc/qrc_qmake_org_deepin_launchpad_windowed.cpp.i"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/.qt/rcc/qrc_qmake_org_deepin_launchpad_windowed.cpp > CMakeFiles/launcher-qml-windowed.dir/.qt/rcc/qrc_qmake_org_deepin_launchpad_windowed.cpp.i

qml/windowed/CMakeFiles/launcher-qml-windowed.dir/.qt/rcc/qrc_qmake_org_deepin_launchpad_windowed.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/launcher-qml-windowed.dir/.qt/rcc/qrc_qmake_org_deepin_launchpad_windowed.cpp.s"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/.qt/rcc/qrc_qmake_org_deepin_launchpad_windowed.cpp -o CMakeFiles/launcher-qml-windowed.dir/.qt/rcc/qrc_qmake_org_deepin_launchpad_windowed.cpp.s

qml/windowed/CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_qmlcache_loader.cpp.o: qml/windowed/CMakeFiles/launcher-qml-windowed.dir/flags.make
qml/windowed/CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_qmlcache_loader.cpp.o: qml/windowed/.rcc/qmlcache/launcher-qml-windowed_qmlcache_loader.cpp
qml/windowed/CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_qmlcache_loader.cpp.o: qml/windowed/CMakeFiles/launcher-qml-windowed.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/CMakeFiles --progress-num=$(CMAKE_PROGRESS_27) "Building CXX object qml/windowed/CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_qmlcache_loader.cpp.o"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT qml/windowed/CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_qmlcache_loader.cpp.o -MF CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_qmlcache_loader.cpp.o.d -o CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_qmlcache_loader.cpp.o -c /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/.rcc/qmlcache/launcher-qml-windowed_qmlcache_loader.cpp

qml/windowed/CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_qmlcache_loader.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_qmlcache_loader.cpp.i"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/.rcc/qmlcache/launcher-qml-windowed_qmlcache_loader.cpp > CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_qmlcache_loader.cpp.i

qml/windowed/CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_qmlcache_loader.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_qmlcache_loader.cpp.s"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/.rcc/qmlcache/launcher-qml-windowed_qmlcache_loader.cpp -o CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_qmlcache_loader.cpp.s

qml/windowed/CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_WindowedFrame_qml.cpp.o: qml/windowed/CMakeFiles/launcher-qml-windowed.dir/flags.make
qml/windowed/CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_WindowedFrame_qml.cpp.o: qml/windowed/.rcc/qmlcache/launcher-qml-windowed_WindowedFrame_qml.cpp
qml/windowed/CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_WindowedFrame_qml.cpp.o: qml/windowed/CMakeFiles/launcher-qml-windowed.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/CMakeFiles --progress-num=$(CMAKE_PROGRESS_28) "Building CXX object qml/windowed/CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_WindowedFrame_qml.cpp.o"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT qml/windowed/CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_WindowedFrame_qml.cpp.o -MF CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_WindowedFrame_qml.cpp.o.d -o CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_WindowedFrame_qml.cpp.o -c /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/.rcc/qmlcache/launcher-qml-windowed_WindowedFrame_qml.cpp

qml/windowed/CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_WindowedFrame_qml.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_WindowedFrame_qml.cpp.i"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/.rcc/qmlcache/launcher-qml-windowed_WindowedFrame_qml.cpp > CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_WindowedFrame_qml.cpp.i

qml/windowed/CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_WindowedFrame_qml.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_WindowedFrame_qml.cpp.s"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/.rcc/qmlcache/launcher-qml-windowed_WindowedFrame_qml.cpp -o CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_WindowedFrame_qml.cpp.s

qml/windowed/CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_SideBar_qml.cpp.o: qml/windowed/CMakeFiles/launcher-qml-windowed.dir/flags.make
qml/windowed/CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_SideBar_qml.cpp.o: qml/windowed/.rcc/qmlcache/launcher-qml-windowed_SideBar_qml.cpp
qml/windowed/CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_SideBar_qml.cpp.o: qml/windowed/CMakeFiles/launcher-qml-windowed.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/CMakeFiles --progress-num=$(CMAKE_PROGRESS_29) "Building CXX object qml/windowed/CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_SideBar_qml.cpp.o"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT qml/windowed/CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_SideBar_qml.cpp.o -MF CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_SideBar_qml.cpp.o.d -o CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_SideBar_qml.cpp.o -c /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/.rcc/qmlcache/launcher-qml-windowed_SideBar_qml.cpp

qml/windowed/CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_SideBar_qml.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_SideBar_qml.cpp.i"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/.rcc/qmlcache/launcher-qml-windowed_SideBar_qml.cpp > CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_SideBar_qml.cpp.i

qml/windowed/CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_SideBar_qml.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_SideBar_qml.cpp.s"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/.rcc/qmlcache/launcher-qml-windowed_SideBar_qml.cpp -o CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_SideBar_qml.cpp.s

qml/windowed/CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_BottomBar_qml.cpp.o: qml/windowed/CMakeFiles/launcher-qml-windowed.dir/flags.make
qml/windowed/CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_BottomBar_qml.cpp.o: qml/windowed/.rcc/qmlcache/launcher-qml-windowed_BottomBar_qml.cpp
qml/windowed/CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_BottomBar_qml.cpp.o: qml/windowed/CMakeFiles/launcher-qml-windowed.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/CMakeFiles --progress-num=$(CMAKE_PROGRESS_30) "Building CXX object qml/windowed/CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_BottomBar_qml.cpp.o"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT qml/windowed/CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_BottomBar_qml.cpp.o -MF CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_BottomBar_qml.cpp.o.d -o CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_BottomBar_qml.cpp.o -c /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/.rcc/qmlcache/launcher-qml-windowed_BottomBar_qml.cpp

qml/windowed/CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_BottomBar_qml.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_BottomBar_qml.cpp.i"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/.rcc/qmlcache/launcher-qml-windowed_BottomBar_qml.cpp > CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_BottomBar_qml.cpp.i

qml/windowed/CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_BottomBar_qml.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_BottomBar_qml.cpp.s"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/.rcc/qmlcache/launcher-qml-windowed_BottomBar_qml.cpp -o CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_BottomBar_qml.cpp.s

qml/windowed/CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_AlphabetCategoryPopup_qml.cpp.o: qml/windowed/CMakeFiles/launcher-qml-windowed.dir/flags.make
qml/windowed/CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_AlphabetCategoryPopup_qml.cpp.o: qml/windowed/.rcc/qmlcache/launcher-qml-windowed_AlphabetCategoryPopup_qml.cpp
qml/windowed/CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_AlphabetCategoryPopup_qml.cpp.o: qml/windowed/CMakeFiles/launcher-qml-windowed.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/CMakeFiles --progress-num=$(CMAKE_PROGRESS_31) "Building CXX object qml/windowed/CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_AlphabetCategoryPopup_qml.cpp.o"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT qml/windowed/CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_AlphabetCategoryPopup_qml.cpp.o -MF CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_AlphabetCategoryPopup_qml.cpp.o.d -o CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_AlphabetCategoryPopup_qml.cpp.o -c /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/.rcc/qmlcache/launcher-qml-windowed_AlphabetCategoryPopup_qml.cpp

qml/windowed/CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_AlphabetCategoryPopup_qml.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_AlphabetCategoryPopup_qml.cpp.i"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/.rcc/qmlcache/launcher-qml-windowed_AlphabetCategoryPopup_qml.cpp > CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_AlphabetCategoryPopup_qml.cpp.i

qml/windowed/CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_AlphabetCategoryPopup_qml.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_AlphabetCategoryPopup_qml.cpp.s"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/.rcc/qmlcache/launcher-qml-windowed_AlphabetCategoryPopup_qml.cpp -o CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_AlphabetCategoryPopup_qml.cpp.s

qml/windowed/CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_AppList_qml.cpp.o: qml/windowed/CMakeFiles/launcher-qml-windowed.dir/flags.make
qml/windowed/CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_AppList_qml.cpp.o: qml/windowed/.rcc/qmlcache/launcher-qml-windowed_AppList_qml.cpp
qml/windowed/CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_AppList_qml.cpp.o: qml/windowed/CMakeFiles/launcher-qml-windowed.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/CMakeFiles --progress-num=$(CMAKE_PROGRESS_32) "Building CXX object qml/windowed/CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_AppList_qml.cpp.o"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT qml/windowed/CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_AppList_qml.cpp.o -MF CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_AppList_qml.cpp.o.d -o CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_AppList_qml.cpp.o -c /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/.rcc/qmlcache/launcher-qml-windowed_AppList_qml.cpp

qml/windowed/CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_AppList_qml.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_AppList_qml.cpp.i"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/.rcc/qmlcache/launcher-qml-windowed_AppList_qml.cpp > CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_AppList_qml.cpp.i

qml/windowed/CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_AppList_qml.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_AppList_qml.cpp.s"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/.rcc/qmlcache/launcher-qml-windowed_AppList_qml.cpp -o CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_AppList_qml.cpp.s

qml/windowed/CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_AppListView_qml.cpp.o: qml/windowed/CMakeFiles/launcher-qml-windowed.dir/flags.make
qml/windowed/CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_AppListView_qml.cpp.o: qml/windowed/.rcc/qmlcache/launcher-qml-windowed_AppListView_qml.cpp
qml/windowed/CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_AppListView_qml.cpp.o: qml/windowed/CMakeFiles/launcher-qml-windowed.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/CMakeFiles --progress-num=$(CMAKE_PROGRESS_33) "Building CXX object qml/windowed/CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_AppListView_qml.cpp.o"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT qml/windowed/CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_AppListView_qml.cpp.o -MF CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_AppListView_qml.cpp.o.d -o CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_AppListView_qml.cpp.o -c /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/.rcc/qmlcache/launcher-qml-windowed_AppListView_qml.cpp

qml/windowed/CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_AppListView_qml.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_AppListView_qml.cpp.i"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/.rcc/qmlcache/launcher-qml-windowed_AppListView_qml.cpp > CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_AppListView_qml.cpp.i

qml/windowed/CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_AppListView_qml.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_AppListView_qml.cpp.s"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/.rcc/qmlcache/launcher-qml-windowed_AppListView_qml.cpp -o CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_AppListView_qml.cpp.s

qml/windowed/CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_IconItemDelegate_qml.cpp.o: qml/windowed/CMakeFiles/launcher-qml-windowed.dir/flags.make
qml/windowed/CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_IconItemDelegate_qml.cpp.o: qml/windowed/.rcc/qmlcache/launcher-qml-windowed_IconItemDelegate_qml.cpp
qml/windowed/CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_IconItemDelegate_qml.cpp.o: qml/windowed/CMakeFiles/launcher-qml-windowed.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/CMakeFiles --progress-num=$(CMAKE_PROGRESS_34) "Building CXX object qml/windowed/CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_IconItemDelegate_qml.cpp.o"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT qml/windowed/CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_IconItemDelegate_qml.cpp.o -MF CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_IconItemDelegate_qml.cpp.o.d -o CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_IconItemDelegate_qml.cpp.o -c /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/.rcc/qmlcache/launcher-qml-windowed_IconItemDelegate_qml.cpp

qml/windowed/CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_IconItemDelegate_qml.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_IconItemDelegate_qml.cpp.i"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/.rcc/qmlcache/launcher-qml-windowed_IconItemDelegate_qml.cpp > CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_IconItemDelegate_qml.cpp.i

qml/windowed/CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_IconItemDelegate_qml.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_IconItemDelegate_qml.cpp.s"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/.rcc/qmlcache/launcher-qml-windowed_IconItemDelegate_qml.cpp -o CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_IconItemDelegate_qml.cpp.s

qml/windowed/CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_FreeSortListView_qml.cpp.o: qml/windowed/CMakeFiles/launcher-qml-windowed.dir/flags.make
qml/windowed/CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_FreeSortListView_qml.cpp.o: qml/windowed/.rcc/qmlcache/launcher-qml-windowed_FreeSortListView_qml.cpp
qml/windowed/CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_FreeSortListView_qml.cpp.o: qml/windowed/CMakeFiles/launcher-qml-windowed.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/CMakeFiles --progress-num=$(CMAKE_PROGRESS_35) "Building CXX object qml/windowed/CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_FreeSortListView_qml.cpp.o"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT qml/windowed/CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_FreeSortListView_qml.cpp.o -MF CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_FreeSortListView_qml.cpp.o.d -o CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_FreeSortListView_qml.cpp.o -c /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/.rcc/qmlcache/launcher-qml-windowed_FreeSortListView_qml.cpp

qml/windowed/CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_FreeSortListView_qml.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_FreeSortListView_qml.cpp.i"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/.rcc/qmlcache/launcher-qml-windowed_FreeSortListView_qml.cpp > CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_FreeSortListView_qml.cpp.i

qml/windowed/CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_FreeSortListView_qml.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_FreeSortListView_qml.cpp.s"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/.rcc/qmlcache/launcher-qml-windowed_FreeSortListView_qml.cpp -o CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_FreeSortListView_qml.cpp.s

qml/windowed/CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_GridViewContainer_qml.cpp.o: qml/windowed/CMakeFiles/launcher-qml-windowed.dir/flags.make
qml/windowed/CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_GridViewContainer_qml.cpp.o: qml/windowed/.rcc/qmlcache/launcher-qml-windowed_GridViewContainer_qml.cpp
qml/windowed/CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_GridViewContainer_qml.cpp.o: qml/windowed/CMakeFiles/launcher-qml-windowed.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/CMakeFiles --progress-num=$(CMAKE_PROGRESS_36) "Building CXX object qml/windowed/CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_GridViewContainer_qml.cpp.o"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT qml/windowed/CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_GridViewContainer_qml.cpp.o -MF CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_GridViewContainer_qml.cpp.o.d -o CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_GridViewContainer_qml.cpp.o -c /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/.rcc/qmlcache/launcher-qml-windowed_GridViewContainer_qml.cpp

qml/windowed/CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_GridViewContainer_qml.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_GridViewContainer_qml.cpp.i"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/.rcc/qmlcache/launcher-qml-windowed_GridViewContainer_qml.cpp > CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_GridViewContainer_qml.cpp.i

qml/windowed/CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_GridViewContainer_qml.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_GridViewContainer_qml.cpp.s"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/.rcc/qmlcache/launcher-qml-windowed_GridViewContainer_qml.cpp -o CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_GridViewContainer_qml.cpp.s

qml/windowed/CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_AnalysisView_qml.cpp.o: qml/windowed/CMakeFiles/launcher-qml-windowed.dir/flags.make
qml/windowed/CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_AnalysisView_qml.cpp.o: qml/windowed/.rcc/qmlcache/launcher-qml-windowed_AnalysisView_qml.cpp
qml/windowed/CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_AnalysisView_qml.cpp.o: qml/windowed/CMakeFiles/launcher-qml-windowed.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/CMakeFiles --progress-num=$(CMAKE_PROGRESS_37) "Building CXX object qml/windowed/CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_AnalysisView_qml.cpp.o"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT qml/windowed/CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_AnalysisView_qml.cpp.o -MF CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_AnalysisView_qml.cpp.o.d -o CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_AnalysisView_qml.cpp.o -c /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/.rcc/qmlcache/launcher-qml-windowed_AnalysisView_qml.cpp

qml/windowed/CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_AnalysisView_qml.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_AnalysisView_qml.cpp.i"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/.rcc/qmlcache/launcher-qml-windowed_AnalysisView_qml.cpp > CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_AnalysisView_qml.cpp.i

qml/windowed/CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_AnalysisView_qml.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_AnalysisView_qml.cpp.s"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/.rcc/qmlcache/launcher-qml-windowed_AnalysisView_qml.cpp -o CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_AnalysisView_qml.cpp.s

qml/windowed/CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_RecentlyInstalledView_qml.cpp.o: qml/windowed/CMakeFiles/launcher-qml-windowed.dir/flags.make
qml/windowed/CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_RecentlyInstalledView_qml.cpp.o: qml/windowed/.rcc/qmlcache/launcher-qml-windowed_RecentlyInstalledView_qml.cpp
qml/windowed/CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_RecentlyInstalledView_qml.cpp.o: qml/windowed/CMakeFiles/launcher-qml-windowed.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/CMakeFiles --progress-num=$(CMAKE_PROGRESS_38) "Building CXX object qml/windowed/CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_RecentlyInstalledView_qml.cpp.o"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT qml/windowed/CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_RecentlyInstalledView_qml.cpp.o -MF CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_RecentlyInstalledView_qml.cpp.o.d -o CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_RecentlyInstalledView_qml.cpp.o -c /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/.rcc/qmlcache/launcher-qml-windowed_RecentlyInstalledView_qml.cpp

qml/windowed/CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_RecentlyInstalledView_qml.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_RecentlyInstalledView_qml.cpp.i"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/.rcc/qmlcache/launcher-qml-windowed_RecentlyInstalledView_qml.cpp > CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_RecentlyInstalledView_qml.cpp.i

qml/windowed/CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_RecentlyInstalledView_qml.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_RecentlyInstalledView_qml.cpp.s"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/.rcc/qmlcache/launcher-qml-windowed_RecentlyInstalledView_qml.cpp -o CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_RecentlyInstalledView_qml.cpp.s

qml/windowed/CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_FrequentlyUsedView_qml.cpp.o: qml/windowed/CMakeFiles/launcher-qml-windowed.dir/flags.make
qml/windowed/CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_FrequentlyUsedView_qml.cpp.o: qml/windowed/.rcc/qmlcache/launcher-qml-windowed_FrequentlyUsedView_qml.cpp
qml/windowed/CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_FrequentlyUsedView_qml.cpp.o: qml/windowed/CMakeFiles/launcher-qml-windowed.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/CMakeFiles --progress-num=$(CMAKE_PROGRESS_39) "Building CXX object qml/windowed/CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_FrequentlyUsedView_qml.cpp.o"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT qml/windowed/CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_FrequentlyUsedView_qml.cpp.o -MF CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_FrequentlyUsedView_qml.cpp.o.d -o CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_FrequentlyUsedView_qml.cpp.o -c /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/.rcc/qmlcache/launcher-qml-windowed_FrequentlyUsedView_qml.cpp

qml/windowed/CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_FrequentlyUsedView_qml.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_FrequentlyUsedView_qml.cpp.i"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/.rcc/qmlcache/launcher-qml-windowed_FrequentlyUsedView_qml.cpp > CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_FrequentlyUsedView_qml.cpp.i

qml/windowed/CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_FrequentlyUsedView_qml.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_FrequentlyUsedView_qml.cpp.s"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/.rcc/qmlcache/launcher-qml-windowed_FrequentlyUsedView_qml.cpp -o CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_FrequentlyUsedView_qml.cpp.s

qml/windowed/CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_SearchResultView_qml.cpp.o: qml/windowed/CMakeFiles/launcher-qml-windowed.dir/flags.make
qml/windowed/CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_SearchResultView_qml.cpp.o: qml/windowed/.rcc/qmlcache/launcher-qml-windowed_SearchResultView_qml.cpp
qml/windowed/CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_SearchResultView_qml.cpp.o: qml/windowed/CMakeFiles/launcher-qml-windowed.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/CMakeFiles --progress-num=$(CMAKE_PROGRESS_40) "Building CXX object qml/windowed/CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_SearchResultView_qml.cpp.o"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT qml/windowed/CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_SearchResultView_qml.cpp.o -MF CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_SearchResultView_qml.cpp.o.d -o CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_SearchResultView_qml.cpp.o -c /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/.rcc/qmlcache/launcher-qml-windowed_SearchResultView_qml.cpp

qml/windowed/CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_SearchResultView_qml.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_SearchResultView_qml.cpp.i"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/.rcc/qmlcache/launcher-qml-windowed_SearchResultView_qml.cpp > CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_SearchResultView_qml.cpp.i

qml/windowed/CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_SearchResultView_qml.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_SearchResultView_qml.cpp.s"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/.rcc/qmlcache/launcher-qml-windowed_SearchResultView_qml.cpp -o CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_SearchResultView_qml.cpp.s

qml/windowed/CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_ItemBackground_qml.cpp.o: qml/windowed/CMakeFiles/launcher-qml-windowed.dir/flags.make
qml/windowed/CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_ItemBackground_qml.cpp.o: qml/windowed/.rcc/qmlcache/launcher-qml-windowed_ItemBackground_qml.cpp
qml/windowed/CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_ItemBackground_qml.cpp.o: qml/windowed/CMakeFiles/launcher-qml-windowed.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/CMakeFiles --progress-num=$(CMAKE_PROGRESS_41) "Building CXX object qml/windowed/CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_ItemBackground_qml.cpp.o"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT qml/windowed/CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_ItemBackground_qml.cpp.o -MF CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_ItemBackground_qml.cpp.o.d -o CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_ItemBackground_qml.cpp.o -c /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/.rcc/qmlcache/launcher-qml-windowed_ItemBackground_qml.cpp

qml/windowed/CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_ItemBackground_qml.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_ItemBackground_qml.cpp.i"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/.rcc/qmlcache/launcher-qml-windowed_ItemBackground_qml.cpp > CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_ItemBackground_qml.cpp.i

qml/windowed/CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_ItemBackground_qml.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_ItemBackground_qml.cpp.s"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/.rcc/qmlcache/launcher-qml-windowed_ItemBackground_qml.cpp -o CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_ItemBackground_qml.cpp.s

qml/windowed/CMakeFiles/launcher-qml-windowed.dir/.qt/rcc/qrc_launcher-qml-windowed_raw_qml_0.cpp.o: qml/windowed/CMakeFiles/launcher-qml-windowed.dir/flags.make
qml/windowed/CMakeFiles/launcher-qml-windowed.dir/.qt/rcc/qrc_launcher-qml-windowed_raw_qml_0.cpp.o: qml/windowed/.qt/rcc/qrc_launcher-qml-windowed_raw_qml_0.cpp
qml/windowed/CMakeFiles/launcher-qml-windowed.dir/.qt/rcc/qrc_launcher-qml-windowed_raw_qml_0.cpp.o: qml/windowed/CMakeFiles/launcher-qml-windowed.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/CMakeFiles --progress-num=$(CMAKE_PROGRESS_42) "Building CXX object qml/windowed/CMakeFiles/launcher-qml-windowed.dir/.qt/rcc/qrc_launcher-qml-windowed_raw_qml_0.cpp.o"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT qml/windowed/CMakeFiles/launcher-qml-windowed.dir/.qt/rcc/qrc_launcher-qml-windowed_raw_qml_0.cpp.o -MF CMakeFiles/launcher-qml-windowed.dir/.qt/rcc/qrc_launcher-qml-windowed_raw_qml_0.cpp.o.d -o CMakeFiles/launcher-qml-windowed.dir/.qt/rcc/qrc_launcher-qml-windowed_raw_qml_0.cpp.o -c /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/.qt/rcc/qrc_launcher-qml-windowed_raw_qml_0.cpp

qml/windowed/CMakeFiles/launcher-qml-windowed.dir/.qt/rcc/qrc_launcher-qml-windowed_raw_qml_0.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/launcher-qml-windowed.dir/.qt/rcc/qrc_launcher-qml-windowed_raw_qml_0.cpp.i"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/.qt/rcc/qrc_launcher-qml-windowed_raw_qml_0.cpp > CMakeFiles/launcher-qml-windowed.dir/.qt/rcc/qrc_launcher-qml-windowed_raw_qml_0.cpp.i

qml/windowed/CMakeFiles/launcher-qml-windowed.dir/.qt/rcc/qrc_launcher-qml-windowed_raw_qml_0.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/launcher-qml-windowed.dir/.qt/rcc/qrc_launcher-qml-windowed_raw_qml_0.cpp.s"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/.qt/rcc/qrc_launcher-qml-windowed_raw_qml_0.cpp -o CMakeFiles/launcher-qml-windowed.dir/.qt/rcc/qrc_launcher-qml-windowed_raw_qml_0.cpp.s

# Object files for target launcher-qml-windowed
launcher__qml__windowed_OBJECTS = \
"CMakeFiles/launcher-qml-windowed.dir/launcher-qml-windowed_autogen/mocs_compilation.cpp.o" \
"CMakeFiles/launcher-qml-windowed.dir/launcher-qml-windowed_org_deepin_launchpad_windowedPlugin.cpp.o" \
"CMakeFiles/launcher-qml-windowed.dir/launcher-qml-windowed_qmltyperegistrations.cpp.o" \
"CMakeFiles/launcher-qml-windowed.dir/.qt/rcc/qrc_qmake_org_deepin_launchpad_windowed.cpp.o" \
"CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_qmlcache_loader.cpp.o" \
"CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_WindowedFrame_qml.cpp.o" \
"CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_SideBar_qml.cpp.o" \
"CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_BottomBar_qml.cpp.o" \
"CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_AlphabetCategoryPopup_qml.cpp.o" \
"CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_AppList_qml.cpp.o" \
"CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_AppListView_qml.cpp.o" \
"CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_IconItemDelegate_qml.cpp.o" \
"CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_FreeSortListView_qml.cpp.o" \
"CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_GridViewContainer_qml.cpp.o" \
"CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_AnalysisView_qml.cpp.o" \
"CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_RecentlyInstalledView_qml.cpp.o" \
"CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_FrequentlyUsedView_qml.cpp.o" \
"CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_SearchResultView_qml.cpp.o" \
"CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_ItemBackground_qml.cpp.o" \
"CMakeFiles/launcher-qml-windowed.dir/.qt/rcc/qrc_launcher-qml-windowed_raw_qml_0.cpp.o"

# External object files for target launcher-qml-windowed
launcher__qml__windowed_EXTERNAL_OBJECTS =

qml/windowed/liblauncher-qml-windowed.a: qml/windowed/CMakeFiles/launcher-qml-windowed.dir/launcher-qml-windowed_autogen/mocs_compilation.cpp.o
qml/windowed/liblauncher-qml-windowed.a: qml/windowed/CMakeFiles/launcher-qml-windowed.dir/launcher-qml-windowed_org_deepin_launchpad_windowedPlugin.cpp.o
qml/windowed/liblauncher-qml-windowed.a: qml/windowed/CMakeFiles/launcher-qml-windowed.dir/launcher-qml-windowed_qmltyperegistrations.cpp.o
qml/windowed/liblauncher-qml-windowed.a: qml/windowed/CMakeFiles/launcher-qml-windowed.dir/.qt/rcc/qrc_qmake_org_deepin_launchpad_windowed.cpp.o
qml/windowed/liblauncher-qml-windowed.a: qml/windowed/CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_qmlcache_loader.cpp.o
qml/windowed/liblauncher-qml-windowed.a: qml/windowed/CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_WindowedFrame_qml.cpp.o
qml/windowed/liblauncher-qml-windowed.a: qml/windowed/CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_SideBar_qml.cpp.o
qml/windowed/liblauncher-qml-windowed.a: qml/windowed/CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_BottomBar_qml.cpp.o
qml/windowed/liblauncher-qml-windowed.a: qml/windowed/CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_AlphabetCategoryPopup_qml.cpp.o
qml/windowed/liblauncher-qml-windowed.a: qml/windowed/CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_AppList_qml.cpp.o
qml/windowed/liblauncher-qml-windowed.a: qml/windowed/CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_AppListView_qml.cpp.o
qml/windowed/liblauncher-qml-windowed.a: qml/windowed/CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_IconItemDelegate_qml.cpp.o
qml/windowed/liblauncher-qml-windowed.a: qml/windowed/CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_FreeSortListView_qml.cpp.o
qml/windowed/liblauncher-qml-windowed.a: qml/windowed/CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_GridViewContainer_qml.cpp.o
qml/windowed/liblauncher-qml-windowed.a: qml/windowed/CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_AnalysisView_qml.cpp.o
qml/windowed/liblauncher-qml-windowed.a: qml/windowed/CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_RecentlyInstalledView_qml.cpp.o
qml/windowed/liblauncher-qml-windowed.a: qml/windowed/CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_FrequentlyUsedView_qml.cpp.o
qml/windowed/liblauncher-qml-windowed.a: qml/windowed/CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_SearchResultView_qml.cpp.o
qml/windowed/liblauncher-qml-windowed.a: qml/windowed/CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_ItemBackground_qml.cpp.o
qml/windowed/liblauncher-qml-windowed.a: qml/windowed/CMakeFiles/launcher-qml-windowed.dir/.qt/rcc/qrc_launcher-qml-windowed_raw_qml_0.cpp.o
qml/windowed/liblauncher-qml-windowed.a: qml/windowed/CMakeFiles/launcher-qml-windowed.dir/build.make
qml/windowed/liblauncher-qml-windowed.a: qml/windowed/CMakeFiles/launcher-qml-windowed.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/CMakeFiles --progress-num=$(CMAKE_PROGRESS_43) "Linking CXX static library liblauncher-qml-windowed.a"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed && $(CMAKE_COMMAND) -P CMakeFiles/launcher-qml-windowed.dir/cmake_clean_target.cmake
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/launcher-qml-windowed.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
qml/windowed/CMakeFiles/launcher-qml-windowed.dir/build: qml/windowed/liblauncher-qml-windowed.a
.PHONY : qml/windowed/CMakeFiles/launcher-qml-windowed.dir/build

qml/windowed/CMakeFiles/launcher-qml-windowed.dir/clean:
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed && $(CMAKE_COMMAND) -P CMakeFiles/launcher-qml-windowed.dir/cmake_clean.cmake
.PHONY : qml/windowed/CMakeFiles/launcher-qml-windowed.dir/clean

qml/windowed/CMakeFiles/launcher-qml-windowed.dir/depend: qml/windowed/.qt/rcc/qrc_launcher-qml-windowed_raw_qml_0.cpp
qml/windowed/CMakeFiles/launcher-qml-windowed.dir/depend: qml/windowed/.qt/rcc/qrc_qmake_org_deepin_launchpad_windowed.cpp
qml/windowed/CMakeFiles/launcher-qml-windowed.dir/depend: qml/windowed/.rcc/qmlcache/launcher-qml-windowed_AlphabetCategoryPopup_qml.cpp
qml/windowed/CMakeFiles/launcher-qml-windowed.dir/depend: qml/windowed/.rcc/qmlcache/launcher-qml-windowed_AlphabetCategoryPopup_qml.cpp.aotstats
qml/windowed/CMakeFiles/launcher-qml-windowed.dir/depend: qml/windowed/.rcc/qmlcache/launcher-qml-windowed_AnalysisView_qml.cpp
qml/windowed/CMakeFiles/launcher-qml-windowed.dir/depend: qml/windowed/.rcc/qmlcache/launcher-qml-windowed_AnalysisView_qml.cpp.aotstats
qml/windowed/CMakeFiles/launcher-qml-windowed.dir/depend: qml/windowed/.rcc/qmlcache/launcher-qml-windowed_AppListView_qml.cpp
qml/windowed/CMakeFiles/launcher-qml-windowed.dir/depend: qml/windowed/.rcc/qmlcache/launcher-qml-windowed_AppListView_qml.cpp.aotstats
qml/windowed/CMakeFiles/launcher-qml-windowed.dir/depend: qml/windowed/.rcc/qmlcache/launcher-qml-windowed_AppList_qml.cpp
qml/windowed/CMakeFiles/launcher-qml-windowed.dir/depend: qml/windowed/.rcc/qmlcache/launcher-qml-windowed_AppList_qml.cpp.aotstats
qml/windowed/CMakeFiles/launcher-qml-windowed.dir/depend: qml/windowed/.rcc/qmlcache/launcher-qml-windowed_BottomBar_qml.cpp
qml/windowed/CMakeFiles/launcher-qml-windowed.dir/depend: qml/windowed/.rcc/qmlcache/launcher-qml-windowed_BottomBar_qml.cpp.aotstats
qml/windowed/CMakeFiles/launcher-qml-windowed.dir/depend: qml/windowed/.rcc/qmlcache/launcher-qml-windowed_FreeSortListView_qml.cpp
qml/windowed/CMakeFiles/launcher-qml-windowed.dir/depend: qml/windowed/.rcc/qmlcache/launcher-qml-windowed_FreeSortListView_qml.cpp.aotstats
qml/windowed/CMakeFiles/launcher-qml-windowed.dir/depend: qml/windowed/.rcc/qmlcache/launcher-qml-windowed_FrequentlyUsedView_qml.cpp
qml/windowed/CMakeFiles/launcher-qml-windowed.dir/depend: qml/windowed/.rcc/qmlcache/launcher-qml-windowed_FrequentlyUsedView_qml.cpp.aotstats
qml/windowed/CMakeFiles/launcher-qml-windowed.dir/depend: qml/windowed/.rcc/qmlcache/launcher-qml-windowed_GridViewContainer_qml.cpp
qml/windowed/CMakeFiles/launcher-qml-windowed.dir/depend: qml/windowed/.rcc/qmlcache/launcher-qml-windowed_GridViewContainer_qml.cpp.aotstats
qml/windowed/CMakeFiles/launcher-qml-windowed.dir/depend: qml/windowed/.rcc/qmlcache/launcher-qml-windowed_IconItemDelegate_qml.cpp
qml/windowed/CMakeFiles/launcher-qml-windowed.dir/depend: qml/windowed/.rcc/qmlcache/launcher-qml-windowed_IconItemDelegate_qml.cpp.aotstats
qml/windowed/CMakeFiles/launcher-qml-windowed.dir/depend: qml/windowed/.rcc/qmlcache/launcher-qml-windowed_ItemBackground_qml.cpp
qml/windowed/CMakeFiles/launcher-qml-windowed.dir/depend: qml/windowed/.rcc/qmlcache/launcher-qml-windowed_ItemBackground_qml.cpp.aotstats
qml/windowed/CMakeFiles/launcher-qml-windowed.dir/depend: qml/windowed/.rcc/qmlcache/launcher-qml-windowed_RecentlyInstalledView_qml.cpp
qml/windowed/CMakeFiles/launcher-qml-windowed.dir/depend: qml/windowed/.rcc/qmlcache/launcher-qml-windowed_RecentlyInstalledView_qml.cpp.aotstats
qml/windowed/CMakeFiles/launcher-qml-windowed.dir/depend: qml/windowed/.rcc/qmlcache/launcher-qml-windowed_SearchResultView_qml.cpp
qml/windowed/CMakeFiles/launcher-qml-windowed.dir/depend: qml/windowed/.rcc/qmlcache/launcher-qml-windowed_SearchResultView_qml.cpp.aotstats
qml/windowed/CMakeFiles/launcher-qml-windowed.dir/depend: qml/windowed/.rcc/qmlcache/launcher-qml-windowed_SideBar_qml.cpp
qml/windowed/CMakeFiles/launcher-qml-windowed.dir/depend: qml/windowed/.rcc/qmlcache/launcher-qml-windowed_SideBar_qml.cpp.aotstats
qml/windowed/CMakeFiles/launcher-qml-windowed.dir/depend: qml/windowed/.rcc/qmlcache/launcher-qml-windowed_WindowedFrame_qml.cpp
qml/windowed/CMakeFiles/launcher-qml-windowed.dir/depend: qml/windowed/.rcc/qmlcache/launcher-qml-windowed_WindowedFrame_qml.cpp.aotstats
qml/windowed/CMakeFiles/launcher-qml-windowed.dir/depend: qml/windowed/.rcc/qmlcache/launcher-qml-windowed_qmlcache_loader.cpp
qml/windowed/CMakeFiles/launcher-qml-windowed.dir/depend: qml/windowed/launcher-qml-windowed.qmltypes
qml/windowed/CMakeFiles/launcher-qml-windowed.dir/depend: qml/windowed/launcher-qml-windowed_autogen/timestamp
qml/windowed/CMakeFiles/launcher-qml-windowed.dir/depend: qml/windowed/launcher-qml-windowed_qmltyperegistrations.cpp
qml/windowed/CMakeFiles/launcher-qml-windowed.dir/depend: qml/windowed/meta_types/launcher-qml-windowed_json_file_list.txt
qml/windowed/CMakeFiles/launcher-qml-windowed.dir/depend: qml/windowed/meta_types/qt6launcher-qml-windowed_none_metatypes.json
qml/windowed/CMakeFiles/launcher-qml-windowed.dir/depend: qml/windowed/meta_types/qt6launcher-qml-windowed_none_metatypes.json.gen
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/Desktop/myrepo/dde-launchpad /home/<USER>/Desktop/myrepo/dde-launchpad/qml/windowed /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/CMakeFiles/launcher-qml-windowed.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : qml/windowed/CMakeFiles/launcher-qml-windowed.dir/depend

