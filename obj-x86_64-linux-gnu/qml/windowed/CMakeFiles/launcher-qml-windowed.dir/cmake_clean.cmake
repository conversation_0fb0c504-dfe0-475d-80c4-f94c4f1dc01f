file(REMOVE_RECURSE
  ".qt/rcc/qrc_launcher-qml-windowed_raw_qml_0.cpp"
  ".qt/rcc/qrc_qmake_org_deepin_launchpad_windowed.cpp"
  ".rcc/qmlcache/launcher-qml-windowed_AlphabetCategoryPopup_qml.cpp"
  ".rcc/qmlcache/launcher-qml-windowed_AlphabetCategoryPopup_qml.cpp.aotstats"
  ".rcc/qmlcache/launcher-qml-windowed_AnalysisView_qml.cpp"
  ".rcc/qmlcache/launcher-qml-windowed_AnalysisView_qml.cpp.aotstats"
  ".rcc/qmlcache/launcher-qml-windowed_AppListView_qml.cpp"
  ".rcc/qmlcache/launcher-qml-windowed_AppListView_qml.cpp.aotstats"
  ".rcc/qmlcache/launcher-qml-windowed_AppList_qml.cpp"
  ".rcc/qmlcache/launcher-qml-windowed_AppList_qml.cpp.aotstats"
  ".rcc/qmlcache/launcher-qml-windowed_BottomBar_qml.cpp"
  ".rcc/qmlcache/launcher-qml-windowed_BottomBar_qml.cpp.aotstats"
  ".rcc/qmlcache/launcher-qml-windowed_FreeSortListView_qml.cpp"
  ".rcc/qmlcache/launcher-qml-windowed_FreeSortListView_qml.cpp.aotstats"
  ".rcc/qmlcache/launcher-qml-windowed_FrequentlyUsedView_qml.cpp"
  ".rcc/qmlcache/launcher-qml-windowed_FrequentlyUsedView_qml.cpp.aotstats"
  ".rcc/qmlcache/launcher-qml-windowed_GridViewContainer_qml.cpp"
  ".rcc/qmlcache/launcher-qml-windowed_GridViewContainer_qml.cpp.aotstats"
  ".rcc/qmlcache/launcher-qml-windowed_IconItemDelegate_qml.cpp"
  ".rcc/qmlcache/launcher-qml-windowed_IconItemDelegate_qml.cpp.aotstats"
  ".rcc/qmlcache/launcher-qml-windowed_ItemBackground_qml.cpp"
  ".rcc/qmlcache/launcher-qml-windowed_ItemBackground_qml.cpp.aotstats"
  ".rcc/qmlcache/launcher-qml-windowed_RecentlyInstalledView_qml.cpp"
  ".rcc/qmlcache/launcher-qml-windowed_RecentlyInstalledView_qml.cpp.aotstats"
  ".rcc/qmlcache/launcher-qml-windowed_SearchResultView_qml.cpp"
  ".rcc/qmlcache/launcher-qml-windowed_SearchResultView_qml.cpp.aotstats"
  ".rcc/qmlcache/launcher-qml-windowed_SideBar_qml.cpp"
  ".rcc/qmlcache/launcher-qml-windowed_SideBar_qml.cpp.aotstats"
  ".rcc/qmlcache/launcher-qml-windowed_WindowedFrame_qml.cpp"
  ".rcc/qmlcache/launcher-qml-windowed_WindowedFrame_qml.cpp.aotstats"
  ".rcc/qmlcache/launcher-qml-windowed_qmlcache_loader.cpp"
  "CMakeFiles/launcher-qml-windowed_autogen.dir/AutogenUsed.txt"
  "CMakeFiles/launcher-qml-windowed_autogen.dir/ParseCache.txt"
  "launcher-qml-windowed_autogen"
  "CMakeFiles/launcher-qml-windowed.dir/.qt/rcc/qrc_launcher-qml-windowed_raw_qml_0.cpp.o"
  "CMakeFiles/launcher-qml-windowed.dir/.qt/rcc/qrc_launcher-qml-windowed_raw_qml_0.cpp.o.d"
  "CMakeFiles/launcher-qml-windowed.dir/.qt/rcc/qrc_qmake_org_deepin_launchpad_windowed.cpp.o"
  "CMakeFiles/launcher-qml-windowed.dir/.qt/rcc/qrc_qmake_org_deepin_launchpad_windowed.cpp.o.d"
  "CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_AlphabetCategoryPopup_qml.cpp.o"
  "CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_AlphabetCategoryPopup_qml.cpp.o.d"
  "CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_AnalysisView_qml.cpp.o"
  "CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_AnalysisView_qml.cpp.o.d"
  "CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_AppListView_qml.cpp.o"
  "CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_AppListView_qml.cpp.o.d"
  "CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_AppList_qml.cpp.o"
  "CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_AppList_qml.cpp.o.d"
  "CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_BottomBar_qml.cpp.o"
  "CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_BottomBar_qml.cpp.o.d"
  "CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_FreeSortListView_qml.cpp.o"
  "CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_FreeSortListView_qml.cpp.o.d"
  "CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_FrequentlyUsedView_qml.cpp.o"
  "CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_FrequentlyUsedView_qml.cpp.o.d"
  "CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_GridViewContainer_qml.cpp.o"
  "CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_GridViewContainer_qml.cpp.o.d"
  "CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_IconItemDelegate_qml.cpp.o"
  "CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_IconItemDelegate_qml.cpp.o.d"
  "CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_ItemBackground_qml.cpp.o"
  "CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_ItemBackground_qml.cpp.o.d"
  "CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_RecentlyInstalledView_qml.cpp.o"
  "CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_RecentlyInstalledView_qml.cpp.o.d"
  "CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_SearchResultView_qml.cpp.o"
  "CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_SearchResultView_qml.cpp.o.d"
  "CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_SideBar_qml.cpp.o"
  "CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_SideBar_qml.cpp.o.d"
  "CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_WindowedFrame_qml.cpp.o"
  "CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_WindowedFrame_qml.cpp.o.d"
  "CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_qmlcache_loader.cpp.o"
  "CMakeFiles/launcher-qml-windowed.dir/.rcc/qmlcache/launcher-qml-windowed_qmlcache_loader.cpp.o.d"
  "CMakeFiles/launcher-qml-windowed.dir/launcher-qml-windowed_autogen/mocs_compilation.cpp.o"
  "CMakeFiles/launcher-qml-windowed.dir/launcher-qml-windowed_autogen/mocs_compilation.cpp.o.d"
  "CMakeFiles/launcher-qml-windowed.dir/launcher-qml-windowed_org_deepin_launchpad_windowedPlugin.cpp.o"
  "CMakeFiles/launcher-qml-windowed.dir/launcher-qml-windowed_org_deepin_launchpad_windowedPlugin.cpp.o.d"
  "CMakeFiles/launcher-qml-windowed.dir/launcher-qml-windowed_qmltyperegistrations.cpp.o"
  "CMakeFiles/launcher-qml-windowed.dir/launcher-qml-windowed_qmltyperegistrations.cpp.o.d"
  "launcher-qml-windowed.qmltypes"
  "launcher-qml-windowed_autogen/mocs_compilation.cpp"
  "launcher-qml-windowed_autogen/timestamp"
  "launcher-qml-windowed_qmltyperegistrations.cpp"
  "liblauncher-qml-windowed.a"
  "liblauncher-qml-windowed.pdb"
  "meta_types/launcher-qml-windowed_json_file_list.txt"
  "meta_types/launcher-qml-windowed_json_file_list.txt.timestamp"
  "meta_types/qt6launcher-qml-windowed_none_metatypes.json"
  "meta_types/qt6launcher-qml-windowed_none_metatypes.json.gen"
)

# Per-language clean rules from dependency scanning.
foreach(lang CXX)
  include(CMakeFiles/launcher-qml-windowed.dir/cmake_clean_${lang}.cmake OPTIONAL)
endforeach()
