# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.31

# compile CXX with /usr/bin/c++
CXX_DEFINES = -DDSG_DATA_DIR=\"/usr/share/dsg\" -DQT_CORE_LIB -DQT_DEPRECATED_WARNINGS -DQT_MESSAGELOGCONTEXT -DQT_NETWORK_LIB -DQT_NO_DEBUG -DQT_PLUGIN -DQT_QMLINTEGRATION_LIB -DQT_QML_LIB -DQT_STATICPLUGIN

CXX_INCLUDES = -I/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed -I/home/<USER>/Desktop/myrepo/dde-launchpad/qml/windowed -I/usr/include/x86_64-linux-gnu/qt6/QtCore -I/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/launcher-qml-windowed_autogen/include -I/usr/include/x86_64-linux-gnu/qt6/QtQml/6.8.0 -I/usr/include/x86_64-linux-gnu/qt6/QtQml/6.8.0/QtQml -I/usr/include/x86_64-linux-gnu/qt6/QtCore/6.8.0 -I/usr/include/x86_64-linux-gnu/qt6/QtCore/6.8.0/QtCore -I/usr/include/x86_64-linux-gnu/qt6/QtQml -I/usr/include/x86_64-linux-gnu/qt6/QtQmlIntegration -I/usr/include/x86_64-linux-gnu/qt6/QtNetwork -isystem /usr/include/x86_64-linux-gnu/qt6 -isystem /usr/lib/x86_64-linux-gnu/qt6/mkspecs/linux-g++

CXX_FLAGS = -g -O2 -fstack-protector-strong -fstack-clash-protection -Wformat -Werror=format-security -fcf-protection -Wall -Wdate-time -D_FORTIFY_SOURCE=2 -std=gnu++17 -fPIC -fPIC

