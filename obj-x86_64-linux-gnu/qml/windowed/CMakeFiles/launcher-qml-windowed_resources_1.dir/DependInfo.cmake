
# Consider dependencies only in project.
set(CMAKE_DEPENDS_IN_PROJECT_ONLY OFF)

# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  )

# The set of dependency files which are needed:
set(CMAKE_DEPENDS_DEPENDENCY_FILES
  "/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/.qt/rcc/qrc_qmake_org_deepin_launchpad_windowed_init.cpp" "qml/windowed/CMakeFiles/launcher-qml-windowed_resources_1.dir/.qt/rcc/qrc_qmake_org_deepin_launchpad_windowed_init.cpp.o" "gcc" "qml/windowed/CMakeFiles/launcher-qml-windowed_resources_1.dir/.qt/rcc/qrc_qmake_org_deepin_launchpad_windowed_init.cpp.o.d"
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_LINKED_INFO_FILES
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_FORWARD_LINKED_INFO_FILES
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
