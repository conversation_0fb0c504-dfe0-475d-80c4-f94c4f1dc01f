# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.31

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Produce verbose output by default.
VERBOSE = 1

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/Desktop/myrepo/dde-launchpad

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu

# Include any dependencies generated for this target.
include qml/windowed/CMakeFiles/launcher-qml-windowed_resources_1.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include qml/windowed/CMakeFiles/launcher-qml-windowed_resources_1.dir/compiler_depend.make

# Include the progress variables for this target.
include qml/windowed/CMakeFiles/launcher-qml-windowed_resources_1.dir/progress.make

# Include the compile flags for this target's objects.
include qml/windowed/CMakeFiles/launcher-qml-windowed_resources_1.dir/flags.make

qml/windowed/CMakeFiles/launcher-qml-windowed_resources_1.dir/codegen:
.PHONY : qml/windowed/CMakeFiles/launcher-qml-windowed_resources_1.dir/codegen

qml/windowed/CMakeFiles/launcher-qml-windowed_resources_1.dir/.qt/rcc/qrc_qmake_org_deepin_launchpad_windowed_init.cpp.o: qml/windowed/CMakeFiles/launcher-qml-windowed_resources_1.dir/flags.make
qml/windowed/CMakeFiles/launcher-qml-windowed_resources_1.dir/.qt/rcc/qrc_qmake_org_deepin_launchpad_windowed_init.cpp.o: qml/windowed/.qt/rcc/qrc_qmake_org_deepin_launchpad_windowed_init.cpp
qml/windowed/CMakeFiles/launcher-qml-windowed_resources_1.dir/.qt/rcc/qrc_qmake_org_deepin_launchpad_windowed_init.cpp.o: qml/windowed/CMakeFiles/launcher-qml-windowed_resources_1.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object qml/windowed/CMakeFiles/launcher-qml-windowed_resources_1.dir/.qt/rcc/qrc_qmake_org_deepin_launchpad_windowed_init.cpp.o"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT qml/windowed/CMakeFiles/launcher-qml-windowed_resources_1.dir/.qt/rcc/qrc_qmake_org_deepin_launchpad_windowed_init.cpp.o -MF CMakeFiles/launcher-qml-windowed_resources_1.dir/.qt/rcc/qrc_qmake_org_deepin_launchpad_windowed_init.cpp.o.d -o CMakeFiles/launcher-qml-windowed_resources_1.dir/.qt/rcc/qrc_qmake_org_deepin_launchpad_windowed_init.cpp.o -c /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/.qt/rcc/qrc_qmake_org_deepin_launchpad_windowed_init.cpp

qml/windowed/CMakeFiles/launcher-qml-windowed_resources_1.dir/.qt/rcc/qrc_qmake_org_deepin_launchpad_windowed_init.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/launcher-qml-windowed_resources_1.dir/.qt/rcc/qrc_qmake_org_deepin_launchpad_windowed_init.cpp.i"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/.qt/rcc/qrc_qmake_org_deepin_launchpad_windowed_init.cpp > CMakeFiles/launcher-qml-windowed_resources_1.dir/.qt/rcc/qrc_qmake_org_deepin_launchpad_windowed_init.cpp.i

qml/windowed/CMakeFiles/launcher-qml-windowed_resources_1.dir/.qt/rcc/qrc_qmake_org_deepin_launchpad_windowed_init.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/launcher-qml-windowed_resources_1.dir/.qt/rcc/qrc_qmake_org_deepin_launchpad_windowed_init.cpp.s"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/.qt/rcc/qrc_qmake_org_deepin_launchpad_windowed_init.cpp -o CMakeFiles/launcher-qml-windowed_resources_1.dir/.qt/rcc/qrc_qmake_org_deepin_launchpad_windowed_init.cpp.s

launcher-qml-windowed_resources_1: qml/windowed/CMakeFiles/launcher-qml-windowed_resources_1.dir/.qt/rcc/qrc_qmake_org_deepin_launchpad_windowed_init.cpp.o
launcher-qml-windowed_resources_1: qml/windowed/CMakeFiles/launcher-qml-windowed_resources_1.dir/build.make
.PHONY : launcher-qml-windowed_resources_1

# Rule to build all files generated by this target.
qml/windowed/CMakeFiles/launcher-qml-windowed_resources_1.dir/build: launcher-qml-windowed_resources_1
.PHONY : qml/windowed/CMakeFiles/launcher-qml-windowed_resources_1.dir/build

qml/windowed/CMakeFiles/launcher-qml-windowed_resources_1.dir/clean:
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed && $(CMAKE_COMMAND) -P CMakeFiles/launcher-qml-windowed_resources_1.dir/cmake_clean.cmake
.PHONY : qml/windowed/CMakeFiles/launcher-qml-windowed_resources_1.dir/clean

qml/windowed/CMakeFiles/launcher-qml-windowed_resources_1.dir/depend:
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/Desktop/myrepo/dde-launchpad /home/<USER>/Desktop/myrepo/dde-launchpad/qml/windowed /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/CMakeFiles/launcher-qml-windowed_resources_1.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : qml/windowed/CMakeFiles/launcher-qml-windowed_resources_1.dir/depend

