# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.31

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Produce verbose output by default.
VERBOSE = 1

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/Desktop/myrepo/dde-launchpad

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu

# Utility rule file for launcher-qml-windowed_qmllint.

# Include any custom commands dependencies for this target.
include qml/windowed/CMakeFiles/launcher-qml-windowed_qmllint.dir/compiler_depend.make

# Include the progress variables for this target.
include qml/windowed/CMakeFiles/launcher-qml-windowed_qmllint.dir/progress.make

qml/windowed/CMakeFiles/launcher-qml-windowed_qmllint: /usr/lib/qt6/bin/qmllint
qml/windowed/CMakeFiles/launcher-qml-windowed_qmllint: /home/<USER>/Desktop/myrepo/dde-launchpad/qml/windowed/WindowedFrame.qml
qml/windowed/CMakeFiles/launcher-qml-windowed_qmllint: /home/<USER>/Desktop/myrepo/dde-launchpad/qml/windowed/SideBar.qml
qml/windowed/CMakeFiles/launcher-qml-windowed_qmllint: /home/<USER>/Desktop/myrepo/dde-launchpad/qml/windowed/BottomBar.qml
qml/windowed/CMakeFiles/launcher-qml-windowed_qmllint: /home/<USER>/Desktop/myrepo/dde-launchpad/qml/windowed/AlphabetCategoryPopup.qml
qml/windowed/CMakeFiles/launcher-qml-windowed_qmllint: /home/<USER>/Desktop/myrepo/dde-launchpad/qml/windowed/AppList.qml
qml/windowed/CMakeFiles/launcher-qml-windowed_qmllint: /home/<USER>/Desktop/myrepo/dde-launchpad/qml/windowed/AppListView.qml
qml/windowed/CMakeFiles/launcher-qml-windowed_qmllint: /home/<USER>/Desktop/myrepo/dde-launchpad/qml/windowed/IconItemDelegate.qml
qml/windowed/CMakeFiles/launcher-qml-windowed_qmllint: /home/<USER>/Desktop/myrepo/dde-launchpad/qml/windowed/FreeSortListView.qml
qml/windowed/CMakeFiles/launcher-qml-windowed_qmllint: /home/<USER>/Desktop/myrepo/dde-launchpad/qml/windowed/GridViewContainer.qml
qml/windowed/CMakeFiles/launcher-qml-windowed_qmllint: /home/<USER>/Desktop/myrepo/dde-launchpad/qml/windowed/AnalysisView.qml
qml/windowed/CMakeFiles/launcher-qml-windowed_qmllint: /home/<USER>/Desktop/myrepo/dde-launchpad/qml/windowed/RecentlyInstalledView.qml
qml/windowed/CMakeFiles/launcher-qml-windowed_qmllint: /home/<USER>/Desktop/myrepo/dde-launchpad/qml/windowed/FrequentlyUsedView.qml
qml/windowed/CMakeFiles/launcher-qml-windowed_qmllint: /home/<USER>/Desktop/myrepo/dde-launchpad/qml/windowed/SearchResultView.qml
qml/windowed/CMakeFiles/launcher-qml-windowed_qmllint: /home/<USER>/Desktop/myrepo/dde-launchpad/qml/windowed/ItemBackground.qml
qml/windowed/CMakeFiles/launcher-qml-windowed_qmllint: qml/windowed/.rcc/qmllint/launcher-qml-windowed.rsp
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/qml/windowed && /usr/lib/qt6/bin/qmllint @/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/.rcc/qmllint/launcher-qml-windowed.rsp

qml/windowed/CMakeFiles/launcher-qml-windowed_qmllint.dir/codegen:
.PHONY : qml/windowed/CMakeFiles/launcher-qml-windowed_qmllint.dir/codegen

launcher-qml-windowed_qmllint: qml/windowed/CMakeFiles/launcher-qml-windowed_qmllint
launcher-qml-windowed_qmllint: qml/windowed/CMakeFiles/launcher-qml-windowed_qmllint.dir/build.make
.PHONY : launcher-qml-windowed_qmllint

# Rule to build all files generated by this target.
qml/windowed/CMakeFiles/launcher-qml-windowed_qmllint.dir/build: launcher-qml-windowed_qmllint
.PHONY : qml/windowed/CMakeFiles/launcher-qml-windowed_qmllint.dir/build

qml/windowed/CMakeFiles/launcher-qml-windowed_qmllint.dir/clean:
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed && $(CMAKE_COMMAND) -P CMakeFiles/launcher-qml-windowed_qmllint.dir/cmake_clean.cmake
.PHONY : qml/windowed/CMakeFiles/launcher-qml-windowed_qmllint.dir/clean

qml/windowed/CMakeFiles/launcher-qml-windowed_qmllint.dir/depend:
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/Desktop/myrepo/dde-launchpad /home/<USER>/Desktop/myrepo/dde-launchpad/qml/windowed /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/CMakeFiles/launcher-qml-windowed_qmllint.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : qml/windowed/CMakeFiles/launcher-qml-windowed_qmllint.dir/depend

