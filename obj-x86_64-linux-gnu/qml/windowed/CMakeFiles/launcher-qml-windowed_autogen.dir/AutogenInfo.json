{"BUILD_DIR": "/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/launcher-qml-windowed_autogen", "CMAKE_BINARY_DIR": "/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu", "CMAKE_CURRENT_BINARY_DIR": "/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed", "CMAKE_CURRENT_SOURCE_DIR": "/home/<USER>/Desktop/myrepo/dde-launchpad/qml/windowed", "CMAKE_EXECUTABLE": "/usr/bin/cmake", "CMAKE_LIST_FILES": ["/home/<USER>/Desktop/myrepo/dde-launchpad/qml/windowed/CMakeLists.txt", "/usr/lib/x86_64-linux-gnu/cmake/Qt6Qml/Qt6QmlPluginTemplate.cpp.in", "/usr/lib/x86_64-linux-gnu/cmake/Qt6Core/Qt6CoreConfigureFileTemplate.in", "/usr/lib/x86_64-linux-gnu/cmake/Qt6Core/Qt6CoreResourceInit.in.cpp", "/usr/lib/x86_64-linux-gnu/cmake/Qt6Qml/Qt6QmlPluginTemplate.cpp.in", "/usr/lib/x86_64-linux-gnu/cmake/Qt6Qml/Qt6QmlModuleDirMappingTemplate.qrc.in", "/usr/lib/x86_64-linux-gnu/cmake/Qt6Core/Qt6CoreConfigureFileTemplate.in", "/usr/lib/x86_64-linux-gnu/cmake/Qt6Core/Qt6CoreResourceInit.in.cpp", "/usr/lib/x86_64-linux-gnu/cmake/Qt6Qml/Qt6qmldirTemplate.cmake.in", "/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/launcher-qml-windowed_org_deepin_launchpad_windowedPlugin_in.cpp"], "CMAKE_SOURCE_DIR": "/home/<USER>/Desktop/myrepo/dde-launchpad", "CROSS_CONFIG": false, "DEP_FILE": "/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/launcher-qml-windowed_autogen/deps", "DEP_FILE_RULE_NAME": "launcher-qml-windowed_autogen/timestamp", "HEADERS": [], "HEADER_EXTENSIONS": ["h", "hh", "h++", "hm", "hpp", "hxx", "in", "txx"], "INCLUDE_DIR": "/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/launcher-qml-windowed_autogen/include", "MOC_COMPILATION_FILE": "/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/launcher-qml-windowed_autogen/mocs_compilation.cpp", "MOC_DEFINITIONS": ["DSG_DATA_DIR=\"/usr/share/dsg\"", "QT_CORE_LIB", "QT_DEPRECATED_WARNINGS", "QT_MESSAGELOGCONTEXT", "QT_NETWORK_LIB", "QT_NO_DEBUG", "QT_PLUGIN", "QT_QMLINTEGRATION_LIB", "QT_QML_LIB", "QT_STATICPLUGIN"], "MOC_DEPEND_FILTERS": [["Q_PLUGIN_METADATA", "[\n][ \t]*Q_PLUGIN_METADATA[ \t]*\\([^\\)]*FILE[ \t]*\"([^\"]+)\""]], "MOC_INCLUDES": ["/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed", "/home/<USER>/Desktop/myrepo/dde-launchpad/qml/windowed", "/usr/include/x86_64-linux-gnu/qt6/QtQml/6.8.0", "/usr/include/x86_64-linux-gnu/qt6/QtQml/6.8.0/QtQml", "/usr/include/x86_64-linux-gnu/qt6/QtCore/6.8.0", "/usr/include/x86_64-linux-gnu/qt6/QtCore/6.8.0/QtCore", "/usr/include/x86_64-linux-gnu/qt6/QtCore", "/usr/include/x86_64-linux-gnu/qt6", "/usr/lib/x86_64-linux-gnu/qt6/mkspecs/linux-g++", "/usr/include/x86_64-linux-gnu/qt6/QtQml", "/usr/include/x86_64-linux-gnu/qt6/QtQmlIntegration", "/usr/include/x86_64-linux-gnu/qt6/QtNetwork", "/usr/include/c++/12", "/usr/include/x86_64-linux-gnu/c++/12", "/usr/include/c++/12/backward", "/usr/lib/gcc/x86_64-linux-gnu/12/include", "/usr/include/x86_64-linux-gnu", "/usr/include"], "MOC_MACRO_NAMES": ["Q_OBJECT", "Q_GADGET", "Q_NAMESPACE", "Q_NAMESPACE_EXPORT", "Q_GADGET_EXPORT", "Q_ENUM_NS"], "MOC_OPTIONS": ["-Muri=org.deepin.launchpad.windowed", "--output-json"], "MOC_PATH_PREFIX": false, "MOC_PREDEFS_CMD": ["/usr/bin/c++", "-std=gnu++17", "-dM", "-E", "-c", "/usr/share/cmake-3.31/Modules/CMakeCXXCompilerABI.cpp"], "MOC_PREDEFS_FILE": "/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/launcher-qml-windowed_autogen/moc_predefs.h", "MOC_RELAXED_MODE": false, "MOC_SKIP": ["/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/.qt/rcc/qrc_launcher-qml-windowed_raw_qml_0.cpp", "/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/.qt/rcc/qrc_launcher-qml-windowed_raw_qml_0_init.cpp", "/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/.qt/rcc/qrc_qmake_org_deepin_launchpad_windowed.cpp", "/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/.qt/rcc/qrc_qmake_org_deepin_launchpad_windowed_init.cpp", "/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/.rcc/qmlcache/launcher-qml-windowed_AlphabetCategoryPopup_qml.cpp", "/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/.rcc/qmlcache/launcher-qml-windowed_AnalysisView_qml.cpp", "/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/.rcc/qmlcache/launcher-qml-windowed_AppListView_qml.cpp", "/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/.rcc/qmlcache/launcher-qml-windowed_AppList_qml.cpp", "/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/.rcc/qmlcache/launcher-qml-windowed_BottomBar_qml.cpp", "/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/.rcc/qmlcache/launcher-qml-windowed_FreeSortListView_qml.cpp", "/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/.rcc/qmlcache/launcher-qml-windowed_FrequentlyUsedView_qml.cpp", "/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/.rcc/qmlcache/launcher-qml-windowed_GridViewContainer_qml.cpp", "/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/.rcc/qmlcache/launcher-qml-windowed_IconItemDelegate_qml.cpp", "/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/.rcc/qmlcache/launcher-qml-windowed_ItemBackground_qml.cpp", "/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/.rcc/qmlcache/launcher-qml-windowed_RecentlyInstalledView_qml.cpp", "/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/.rcc/qmlcache/launcher-qml-windowed_SearchResultView_qml.cpp", "/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/.rcc/qmlcache/launcher-qml-windowed_SideBar_qml.cpp", "/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/.rcc/qmlcache/launcher-qml-windowed_WindowedFrame_qml.cpp", "/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/.rcc/qmlcache/launcher-qml-windowed_qmlcache_loader.cpp", "/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/launcher-qml-windowed_qmltyperegistrations.cpp"], "MULTI_CONFIG": false, "PARALLEL": 8, "PARSE_CACHE_FILE": "/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/CMakeFiles/launcher-qml-windowed_autogen.dir/ParseCache.txt", "QT_MOC_EXECUTABLE": "/usr/lib/qt6/libexec/moc", "QT_UIC_EXECUTABLE": "", "QT_VERSION_MAJOR": 6, "QT_VERSION_MINOR": 8, "SETTINGS_FILE": "/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/CMakeFiles/launcher-qml-windowed_autogen.dir/AutogenUsed.txt", "SOURCES": [["/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/launcher-qml-windowed_org_deepin_launchpad_windowedPlugin.cpp", "Mu", null]], "USE_BETTER_GRAPH": true, "VERBOSITY": 0}