# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.31

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Produce verbose output by default.
VERBOSE = 1

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/Desktop/myrepo/dde-launchpad

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu

# Utility rule file for launcher-qml-windowed_autogen.

# Include any custom commands dependencies for this target.
include qml/windowed/CMakeFiles/launcher-qml-windowed_autogen.dir/compiler_depend.make

# Include the progress variables for this target.
include qml/windowed/CMakeFiles/launcher-qml-windowed_autogen.dir/progress.make

qml/windowed/CMakeFiles/launcher-qml-windowed_autogen: qml/windowed/launcher-qml-windowed_autogen/timestamp

qml/windowed/launcher-qml-windowed_autogen/timestamp: /usr/lib/qt6/libexec/moc
qml/windowed/launcher-qml-windowed_autogen/timestamp: qml/windowed/CMakeFiles/launcher-qml-windowed_autogen.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Automatic MOC for target launcher-qml-windowed"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed && /usr/bin/cmake -E cmake_autogen /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/CMakeFiles/launcher-qml-windowed_autogen.dir/AutogenInfo.json None
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed && /usr/bin/cmake -E touch /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/launcher-qml-windowed_autogen/timestamp

qml/windowed/CMakeFiles/launcher-qml-windowed_autogen.dir/codegen:
.PHONY : qml/windowed/CMakeFiles/launcher-qml-windowed_autogen.dir/codegen

launcher-qml-windowed_autogen: qml/windowed/CMakeFiles/launcher-qml-windowed_autogen
launcher-qml-windowed_autogen: qml/windowed/launcher-qml-windowed_autogen/timestamp
launcher-qml-windowed_autogen: qml/windowed/CMakeFiles/launcher-qml-windowed_autogen.dir/build.make
.PHONY : launcher-qml-windowed_autogen

# Rule to build all files generated by this target.
qml/windowed/CMakeFiles/launcher-qml-windowed_autogen.dir/build: launcher-qml-windowed_autogen
.PHONY : qml/windowed/CMakeFiles/launcher-qml-windowed_autogen.dir/build

qml/windowed/CMakeFiles/launcher-qml-windowed_autogen.dir/clean:
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed && $(CMAKE_COMMAND) -P CMakeFiles/launcher-qml-windowed_autogen.dir/cmake_clean.cmake
.PHONY : qml/windowed/CMakeFiles/launcher-qml-windowed_autogen.dir/clean

qml/windowed/CMakeFiles/launcher-qml-windowed_autogen.dir/depend:
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/Desktop/myrepo/dde-launchpad /home/<USER>/Desktop/myrepo/dde-launchpad/qml/windowed /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/CMakeFiles/launcher-qml-windowed_autogen.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : qml/windowed/CMakeFiles/launcher-qml-windowed_autogen.dir/depend

