# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.31

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Produce verbose output by default.
VERBOSE = 1

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/Desktop/myrepo/dde-launchpad

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu

# Include any dependencies generated for this target.
include qml/windowed/CMakeFiles/launcher-qml-windowed_init.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include qml/windowed/CMakeFiles/launcher-qml-windowed_init.dir/compiler_depend.make

# Include the progress variables for this target.
include qml/windowed/CMakeFiles/launcher-qml-windowed_init.dir/progress.make

# Include the compile flags for this target's objects.
include qml/windowed/CMakeFiles/launcher-qml-windowed_init.dir/flags.make

qml/windowed/meta_types/qt6launcher-qml-windowed_init_none_metatypes.json.gen: /usr/lib/qt6/libexec/moc
qml/windowed/meta_types/qt6launcher-qml-windowed_init_none_metatypes.json.gen: qml/windowed/meta_types/launcher-qml-windowed_init_json_file_list.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Running moc --collect-json for target launcher-qml-windowed_init"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed && /usr/lib/qt6/libexec/moc -o /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/meta_types/qt6launcher-qml-windowed_init_none_metatypes.json.gen --collect-json @/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/meta_types/launcher-qml-windowed_init_json_file_list.txt
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed && /usr/bin/cmake -E copy_if_different /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/meta_types/qt6launcher-qml-windowed_init_none_metatypes.json.gen /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/meta_types/qt6launcher-qml-windowed_init_none_metatypes.json

qml/windowed/launcher-qml-windowed_init_autogen/timestamp: /usr/lib/qt6/libexec/moc
qml/windowed/launcher-qml-windowed_init_autogen/timestamp: qml/windowed/CMakeFiles/launcher-qml-windowed_init.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Automatic MOC for target launcher-qml-windowed_init"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed && /usr/bin/cmake -E cmake_autogen /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/CMakeFiles/launcher-qml-windowed_init_autogen.dir/AutogenInfo.json None
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed && /usr/bin/cmake -E touch /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/launcher-qml-windowed_init_autogen/timestamp

qml/windowed/meta_types/launcher-qml-windowed_init_json_file_list.txt: /usr/lib/qt6/libexec/cmake_automoc_parser
qml/windowed/meta_types/launcher-qml-windowed_init_json_file_list.txt: qml/windowed/launcher-qml-windowed_init_autogen/timestamp
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Running AUTOMOC file extraction for target launcher-qml-windowed_init"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed && /usr/lib/qt6/libexec/cmake_automoc_parser --cmake-autogen-cache-file /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/CMakeFiles/launcher-qml-windowed_init_autogen.dir/ParseCache.txt --cmake-autogen-info-file /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/CMakeFiles/launcher-qml-windowed_init_autogen.dir/AutogenInfo.json --output-file-path /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/meta_types/launcher-qml-windowed_init_json_file_list.txt --timestamp-file-path /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/meta_types/launcher-qml-windowed_init_json_file_list.txt.timestamp --cmake-autogen-include-dir-path /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/launcher-qml-windowed_init_autogen/include

qml/windowed/CMakeFiles/launcher-qml-windowed_init.dir/codegen:
.PHONY : qml/windowed/CMakeFiles/launcher-qml-windowed_init.dir/codegen

qml/windowed/CMakeFiles/launcher-qml-windowed_init.dir/launcher-qml-windowed_init_autogen/mocs_compilation.cpp.o: qml/windowed/CMakeFiles/launcher-qml-windowed_init.dir/flags.make
qml/windowed/CMakeFiles/launcher-qml-windowed_init.dir/launcher-qml-windowed_init_autogen/mocs_compilation.cpp.o: qml/windowed/launcher-qml-windowed_init_autogen/mocs_compilation.cpp
qml/windowed/CMakeFiles/launcher-qml-windowed_init.dir/launcher-qml-windowed_init_autogen/mocs_compilation.cpp.o: qml/windowed/CMakeFiles/launcher-qml-windowed_init.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building CXX object qml/windowed/CMakeFiles/launcher-qml-windowed_init.dir/launcher-qml-windowed_init_autogen/mocs_compilation.cpp.o"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT qml/windowed/CMakeFiles/launcher-qml-windowed_init.dir/launcher-qml-windowed_init_autogen/mocs_compilation.cpp.o -MF CMakeFiles/launcher-qml-windowed_init.dir/launcher-qml-windowed_init_autogen/mocs_compilation.cpp.o.d -o CMakeFiles/launcher-qml-windowed_init.dir/launcher-qml-windowed_init_autogen/mocs_compilation.cpp.o -c /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/launcher-qml-windowed_init_autogen/mocs_compilation.cpp

qml/windowed/CMakeFiles/launcher-qml-windowed_init.dir/launcher-qml-windowed_init_autogen/mocs_compilation.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/launcher-qml-windowed_init.dir/launcher-qml-windowed_init_autogen/mocs_compilation.cpp.i"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/launcher-qml-windowed_init_autogen/mocs_compilation.cpp > CMakeFiles/launcher-qml-windowed_init.dir/launcher-qml-windowed_init_autogen/mocs_compilation.cpp.i

qml/windowed/CMakeFiles/launcher-qml-windowed_init.dir/launcher-qml-windowed_init_autogen/mocs_compilation.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/launcher-qml-windowed_init.dir/launcher-qml-windowed_init_autogen/mocs_compilation.cpp.s"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/launcher-qml-windowed_init_autogen/mocs_compilation.cpp -o CMakeFiles/launcher-qml-windowed_init.dir/launcher-qml-windowed_init_autogen/mocs_compilation.cpp.s

qml/windowed/CMakeFiles/launcher-qml-windowed_init.dir/launcher_qml_windowed_init.cpp.o: qml/windowed/CMakeFiles/launcher-qml-windowed_init.dir/flags.make
qml/windowed/CMakeFiles/launcher-qml-windowed_init.dir/launcher_qml_windowed_init.cpp.o: qml/windowed/launcher_qml_windowed_init.cpp
qml/windowed/CMakeFiles/launcher-qml-windowed_init.dir/launcher_qml_windowed_init.cpp.o: qml/windowed/CMakeFiles/launcher-qml-windowed_init.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building CXX object qml/windowed/CMakeFiles/launcher-qml-windowed_init.dir/launcher_qml_windowed_init.cpp.o"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT qml/windowed/CMakeFiles/launcher-qml-windowed_init.dir/launcher_qml_windowed_init.cpp.o -MF CMakeFiles/launcher-qml-windowed_init.dir/launcher_qml_windowed_init.cpp.o.d -o CMakeFiles/launcher-qml-windowed_init.dir/launcher_qml_windowed_init.cpp.o -c /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/launcher_qml_windowed_init.cpp

qml/windowed/CMakeFiles/launcher-qml-windowed_init.dir/launcher_qml_windowed_init.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/launcher-qml-windowed_init.dir/launcher_qml_windowed_init.cpp.i"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/launcher_qml_windowed_init.cpp > CMakeFiles/launcher-qml-windowed_init.dir/launcher_qml_windowed_init.cpp.i

qml/windowed/CMakeFiles/launcher-qml-windowed_init.dir/launcher_qml_windowed_init.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/launcher-qml-windowed_init.dir/launcher_qml_windowed_init.cpp.s"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/launcher_qml_windowed_init.cpp -o CMakeFiles/launcher-qml-windowed_init.dir/launcher_qml_windowed_init.cpp.s

launcher-qml-windowed_init: qml/windowed/CMakeFiles/launcher-qml-windowed_init.dir/launcher-qml-windowed_init_autogen/mocs_compilation.cpp.o
launcher-qml-windowed_init: qml/windowed/CMakeFiles/launcher-qml-windowed_init.dir/launcher_qml_windowed_init.cpp.o
launcher-qml-windowed_init: qml/windowed/CMakeFiles/launcher-qml-windowed_init.dir/build.make
.PHONY : launcher-qml-windowed_init

# Rule to build all files generated by this target.
qml/windowed/CMakeFiles/launcher-qml-windowed_init.dir/build: launcher-qml-windowed_init
.PHONY : qml/windowed/CMakeFiles/launcher-qml-windowed_init.dir/build

qml/windowed/CMakeFiles/launcher-qml-windowed_init.dir/clean:
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed && $(CMAKE_COMMAND) -P CMakeFiles/launcher-qml-windowed_init.dir/cmake_clean.cmake
.PHONY : qml/windowed/CMakeFiles/launcher-qml-windowed_init.dir/clean

qml/windowed/CMakeFiles/launcher-qml-windowed_init.dir/depend: qml/windowed/launcher-qml-windowed_init_autogen/timestamp
qml/windowed/CMakeFiles/launcher-qml-windowed_init.dir/depend: qml/windowed/meta_types/launcher-qml-windowed_init_json_file_list.txt
qml/windowed/CMakeFiles/launcher-qml-windowed_init.dir/depend: qml/windowed/meta_types/qt6launcher-qml-windowed_init_none_metatypes.json.gen
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/Desktop/myrepo/dde-launchpad /home/<USER>/Desktop/myrepo/dde-launchpad/qml/windowed /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/qml/windowed/CMakeFiles/launcher-qml-windowed_init.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : qml/windowed/CMakeFiles/launcher-qml-windowed_init.dir/depend

