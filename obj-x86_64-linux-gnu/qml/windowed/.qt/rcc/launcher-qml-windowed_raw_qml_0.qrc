<RCC>
  <qresource prefix="/qt/qml/org/deepin/launchpad/windowed/">
    <file alias="WindowedFrame.qml">/home/<USER>/Desktop/myrepo/dde-launchpad/qml/windowed/WindowedFrame.qml</file>
    <file alias="SideBar.qml">/home/<USER>/Desktop/myrepo/dde-launchpad/qml/windowed/SideBar.qml</file>
    <file alias="BottomBar.qml">/home/<USER>/Desktop/myrepo/dde-launchpad/qml/windowed/BottomBar.qml</file>
    <file alias="AlphabetCategoryPopup.qml">/home/<USER>/Desktop/myrepo/dde-launchpad/qml/windowed/AlphabetCategoryPopup.qml</file>
    <file alias="AppList.qml">/home/<USER>/Desktop/myrepo/dde-launchpad/qml/windowed/AppList.qml</file>
    <file alias="AppListView.qml">/home/<USER>/Desktop/myrepo/dde-launchpad/qml/windowed/AppListView.qml</file>
    <file alias="IconItemDelegate.qml">/home/<USER>/Desktop/myrepo/dde-launchpad/qml/windowed/IconItemDelegate.qml</file>
    <file alias="FreeSortListView.qml">/home/<USER>/Desktop/myrepo/dde-launchpad/qml/windowed/FreeSortListView.qml</file>
    <file alias="GridViewContainer.qml">/home/<USER>/Desktop/myrepo/dde-launchpad/qml/windowed/GridViewContainer.qml</file>
    <file alias="AnalysisView.qml">/home/<USER>/Desktop/myrepo/dde-launchpad/qml/windowed/AnalysisView.qml</file>
    <file alias="RecentlyInstalledView.qml">/home/<USER>/Desktop/myrepo/dde-launchpad/qml/windowed/RecentlyInstalledView.qml</file>
    <file alias="FrequentlyUsedView.qml">/home/<USER>/Desktop/myrepo/dde-launchpad/qml/windowed/FrequentlyUsedView.qml</file>
    <file alias="SearchResultView.qml">/home/<USER>/Desktop/myrepo/dde-launchpad/qml/windowed/SearchResultView.qml</file>
    <file alias="ItemBackground.qml">/home/<USER>/Desktop/myrepo/dde-launchpad/qml/windowed/ItemBackground.qml</file>
  </qresource>
</RCC>

