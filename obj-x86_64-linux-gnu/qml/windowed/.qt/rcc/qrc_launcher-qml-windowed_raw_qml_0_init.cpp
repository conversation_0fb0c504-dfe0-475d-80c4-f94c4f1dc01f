// Copyright (C) 2023 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR BSD-3-Clause

// This file was generated by the qt_add_resources command.

#include <QtCore/qtsymbolmacros.h>

QT_DECLARE_EXTERN_RESOURCE(launcher_qml_windowed_raw_qml_0)

namespace {
    struct resourceReferenceKeeper {
        resourceReferenceKeeper() { QT_KEEP_RESOURCE(launcher_qml_windowed_raw_qml_0) }
    } resourceReferenceKeeperInstance;
}
