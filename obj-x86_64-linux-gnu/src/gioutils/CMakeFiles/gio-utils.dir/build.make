# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.31

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Produce verbose output by default.
VERBOSE = 1

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/Desktop/myrepo/dde-launchpad

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu

# Include any dependencies generated for this target.
include src/gioutils/CMakeFiles/gio-utils.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include src/gioutils/CMakeFiles/gio-utils.dir/compiler_depend.make

# Include the progress variables for this target.
include src/gioutils/CMakeFiles/gio-utils.dir/progress.make

# Include the compile flags for this target's objects.
include src/gioutils/CMakeFiles/gio-utils.dir/flags.make

src/gioutils/gio-utils_autogen/timestamp: /usr/lib/qt6/libexec/moc
src/gioutils/gio-utils_autogen/timestamp: src/gioutils/CMakeFiles/gio-utils.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Automatic MOC for target gio-utils"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/gioutils && /usr/bin/cmake -E cmake_autogen /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/gioutils/CMakeFiles/gio-utils_autogen.dir/AutogenInfo.json None
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/gioutils && /usr/bin/cmake -E touch /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/gioutils/gio-utils_autogen/timestamp

src/gioutils/CMakeFiles/gio-utils.dir/codegen:
.PHONY : src/gioutils/CMakeFiles/gio-utils.dir/codegen

src/gioutils/CMakeFiles/gio-utils.dir/gio-utils_autogen/mocs_compilation.cpp.o: src/gioutils/CMakeFiles/gio-utils.dir/flags.make
src/gioutils/CMakeFiles/gio-utils.dir/gio-utils_autogen/mocs_compilation.cpp.o: src/gioutils/gio-utils_autogen/mocs_compilation.cpp
src/gioutils/CMakeFiles/gio-utils.dir/gio-utils_autogen/mocs_compilation.cpp.o: src/gioutils/CMakeFiles/gio-utils.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object src/gioutils/CMakeFiles/gio-utils.dir/gio-utils_autogen/mocs_compilation.cpp.o"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/gioutils && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/gioutils/CMakeFiles/gio-utils.dir/gio-utils_autogen/mocs_compilation.cpp.o -MF CMakeFiles/gio-utils.dir/gio-utils_autogen/mocs_compilation.cpp.o.d -o CMakeFiles/gio-utils.dir/gio-utils_autogen/mocs_compilation.cpp.o -c /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/gioutils/gio-utils_autogen/mocs_compilation.cpp

src/gioutils/CMakeFiles/gio-utils.dir/gio-utils_autogen/mocs_compilation.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/gio-utils.dir/gio-utils_autogen/mocs_compilation.cpp.i"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/gioutils && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/gioutils/gio-utils_autogen/mocs_compilation.cpp > CMakeFiles/gio-utils.dir/gio-utils_autogen/mocs_compilation.cpp.i

src/gioutils/CMakeFiles/gio-utils.dir/gio-utils_autogen/mocs_compilation.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/gio-utils.dir/gio-utils_autogen/mocs_compilation.cpp.s"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/gioutils && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/gioutils/gio-utils_autogen/mocs_compilation.cpp -o CMakeFiles/gio-utils.dir/gio-utils_autogen/mocs_compilation.cpp.s

src/gioutils/CMakeFiles/gio-utils.dir/trashmonitor.cpp.o: src/gioutils/CMakeFiles/gio-utils.dir/flags.make
src/gioutils/CMakeFiles/gio-utils.dir/trashmonitor.cpp.o: /home/<USER>/Desktop/myrepo/dde-launchpad/src/gioutils/trashmonitor.cpp
src/gioutils/CMakeFiles/gio-utils.dir/trashmonitor.cpp.o: src/gioutils/CMakeFiles/gio-utils.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object src/gioutils/CMakeFiles/gio-utils.dir/trashmonitor.cpp.o"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/gioutils && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/gioutils/CMakeFiles/gio-utils.dir/trashmonitor.cpp.o -MF CMakeFiles/gio-utils.dir/trashmonitor.cpp.o.d -o CMakeFiles/gio-utils.dir/trashmonitor.cpp.o -c /home/<USER>/Desktop/myrepo/dde-launchpad/src/gioutils/trashmonitor.cpp

src/gioutils/CMakeFiles/gio-utils.dir/trashmonitor.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/gio-utils.dir/trashmonitor.cpp.i"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/gioutils && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Desktop/myrepo/dde-launchpad/src/gioutils/trashmonitor.cpp > CMakeFiles/gio-utils.dir/trashmonitor.cpp.i

src/gioutils/CMakeFiles/gio-utils.dir/trashmonitor.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/gio-utils.dir/trashmonitor.cpp.s"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/gioutils && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Desktop/myrepo/dde-launchpad/src/gioutils/trashmonitor.cpp -o CMakeFiles/gio-utils.dir/trashmonitor.cpp.s

src/gioutils/CMakeFiles/gio-utils.dir/appinfomonitor.cpp.o: src/gioutils/CMakeFiles/gio-utils.dir/flags.make
src/gioutils/CMakeFiles/gio-utils.dir/appinfomonitor.cpp.o: /home/<USER>/Desktop/myrepo/dde-launchpad/src/gioutils/appinfomonitor.cpp
src/gioutils/CMakeFiles/gio-utils.dir/appinfomonitor.cpp.o: src/gioutils/CMakeFiles/gio-utils.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building CXX object src/gioutils/CMakeFiles/gio-utils.dir/appinfomonitor.cpp.o"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/gioutils && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/gioutils/CMakeFiles/gio-utils.dir/appinfomonitor.cpp.o -MF CMakeFiles/gio-utils.dir/appinfomonitor.cpp.o.d -o CMakeFiles/gio-utils.dir/appinfomonitor.cpp.o -c /home/<USER>/Desktop/myrepo/dde-launchpad/src/gioutils/appinfomonitor.cpp

src/gioutils/CMakeFiles/gio-utils.dir/appinfomonitor.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/gio-utils.dir/appinfomonitor.cpp.i"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/gioutils && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Desktop/myrepo/dde-launchpad/src/gioutils/appinfomonitor.cpp > CMakeFiles/gio-utils.dir/appinfomonitor.cpp.i

src/gioutils/CMakeFiles/gio-utils.dir/appinfomonitor.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/gio-utils.dir/appinfomonitor.cpp.s"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/gioutils && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Desktop/myrepo/dde-launchpad/src/gioutils/appinfomonitor.cpp -o CMakeFiles/gio-utils.dir/appinfomonitor.cpp.s

src/gioutils/CMakeFiles/gio-utils.dir/appinfo.cpp.o: src/gioutils/CMakeFiles/gio-utils.dir/flags.make
src/gioutils/CMakeFiles/gio-utils.dir/appinfo.cpp.o: /home/<USER>/Desktop/myrepo/dde-launchpad/src/gioutils/appinfo.cpp
src/gioutils/CMakeFiles/gio-utils.dir/appinfo.cpp.o: src/gioutils/CMakeFiles/gio-utils.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building CXX object src/gioutils/CMakeFiles/gio-utils.dir/appinfo.cpp.o"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/gioutils && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/gioutils/CMakeFiles/gio-utils.dir/appinfo.cpp.o -MF CMakeFiles/gio-utils.dir/appinfo.cpp.o.d -o CMakeFiles/gio-utils.dir/appinfo.cpp.o -c /home/<USER>/Desktop/myrepo/dde-launchpad/src/gioutils/appinfo.cpp

src/gioutils/CMakeFiles/gio-utils.dir/appinfo.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/gio-utils.dir/appinfo.cpp.i"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/gioutils && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Desktop/myrepo/dde-launchpad/src/gioutils/appinfo.cpp > CMakeFiles/gio-utils.dir/appinfo.cpp.i

src/gioutils/CMakeFiles/gio-utils.dir/appinfo.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/gio-utils.dir/appinfo.cpp.s"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/gioutils && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Desktop/myrepo/dde-launchpad/src/gioutils/appinfo.cpp -o CMakeFiles/gio-utils.dir/appinfo.cpp.s

gio-utils: src/gioutils/CMakeFiles/gio-utils.dir/gio-utils_autogen/mocs_compilation.cpp.o
gio-utils: src/gioutils/CMakeFiles/gio-utils.dir/trashmonitor.cpp.o
gio-utils: src/gioutils/CMakeFiles/gio-utils.dir/appinfomonitor.cpp.o
gio-utils: src/gioutils/CMakeFiles/gio-utils.dir/appinfo.cpp.o
gio-utils: src/gioutils/CMakeFiles/gio-utils.dir/build.make
.PHONY : gio-utils

# Rule to build all files generated by this target.
src/gioutils/CMakeFiles/gio-utils.dir/build: gio-utils
.PHONY : src/gioutils/CMakeFiles/gio-utils.dir/build

src/gioutils/CMakeFiles/gio-utils.dir/clean:
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/gioutils && $(CMAKE_COMMAND) -P CMakeFiles/gio-utils.dir/cmake_clean.cmake
.PHONY : src/gioutils/CMakeFiles/gio-utils.dir/clean

src/gioutils/CMakeFiles/gio-utils.dir/depend: src/gioutils/gio-utils_autogen/timestamp
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/Desktop/myrepo/dde-launchpad /home/<USER>/Desktop/myrepo/dde-launchpad/src/gioutils /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/gioutils /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/gioutils/CMakeFiles/gio-utils.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : src/gioutils/CMakeFiles/gio-utils.dir/depend

