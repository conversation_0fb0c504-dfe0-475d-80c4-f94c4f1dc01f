
# Consider dependencies only in project.
set(CMAKE_DEPENDS_IN_PROJECT_ONLY OFF)

# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  )

# The set of dependency files which are needed:
set(CMAKE_DEPENDS_DEPENDENCY_FILES
  "" "src/gioutils/gio-utils_autogen/timestamp" "custom" "src/gioutils/gio-utils_autogen/deps"
  "/home/<USER>/Desktop/myrepo/dde-launchpad/src/gioutils/appinfo.cpp" "src/gioutils/CMakeFiles/gio-utils.dir/appinfo.cpp.o" "gcc" "src/gioutils/CMakeFiles/gio-utils.dir/appinfo.cpp.o.d"
  "/home/<USER>/Desktop/myrepo/dde-launchpad/src/gioutils/appinfomonitor.cpp" "src/gioutils/CMakeFiles/gio-utils.dir/appinfomonitor.cpp.o" "gcc" "src/gioutils/CMakeFiles/gio-utils.dir/appinfomonitor.cpp.o.d"
  "/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/gioutils/gio-utils_autogen/mocs_compilation.cpp" "src/gioutils/CMakeFiles/gio-utils.dir/gio-utils_autogen/mocs_compilation.cpp.o" "gcc" "src/gioutils/CMakeFiles/gio-utils.dir/gio-utils_autogen/mocs_compilation.cpp.o.d"
  "/home/<USER>/Desktop/myrepo/dde-launchpad/src/gioutils/trashmonitor.cpp" "src/gioutils/CMakeFiles/gio-utils.dir/trashmonitor.cpp.o" "gcc" "src/gioutils/CMakeFiles/gio-utils.dir/trashmonitor.cpp.o.d"
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_LINKED_INFO_FILES
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_FORWARD_LINKED_INFO_FILES
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
