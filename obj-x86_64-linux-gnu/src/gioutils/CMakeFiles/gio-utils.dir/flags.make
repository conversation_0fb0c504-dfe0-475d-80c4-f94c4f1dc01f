# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.31

# compile CXX with /usr/bin/c++
CXX_DEFINES = -DDSG_DATA_DIR=\"/usr/share/dsg\" -DQT_CORE_LIB -DQT_MESSAGELOGCONTEXT -DQT_NO_DEBUG -DQT_NO_KEYWORDS

CXX_INCLUDES = -I/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/gioutils -I/home/<USER>/Desktop/myrepo/dde-launchpad/src/gioutils -I/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/gioutils/gio-utils_autogen/include -isystem /usr/include/x86_64-linux-gnu/qt6/QtCore -isystem /usr/include/x86_64-linux-gnu/qt6 -isystem /usr/lib/x86_64-linux-gnu/qt6/mkspecs/linux-g++ -isystem /usr/include/glib-2.0 -isystem /usr/lib/x86_64-linux-gnu/glib-2.0/include -isystem /usr/include/sysprof-6 -isystem /usr/include/libmount -isystem /usr/include/blkid -isystem /usr/include/gio-unix-2.0

CXX_FLAGS = -g -O2 -fstack-protector-strong -fstack-clash-protection -Wformat -Werror=format-security -fcf-protection -Wall -Wdate-time -D_FORTIFY_SOURCE=2 -std=gnu++17 -fPIC -fPIC -pthread

