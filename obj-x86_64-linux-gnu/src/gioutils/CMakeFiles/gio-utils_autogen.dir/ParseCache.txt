# Generated by CMake. Changes will be overwritten.
/home/<USER>/Desktop/myrepo/dde-launchpad/src/gioutils/trashmonitor.cpp
/home/<USER>/Desktop/myrepo/dde-launchpad/src/gioutils/appinfomonitor.cpp
/home/<USER>/Desktop/myrepo/dde-launchpad/src/gioutils/trashmonitor.h
 mmc:Q_OBJECT
 mdp:/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/gioutils/gio-utils_autogen/moc_predefs.h
 mdp:/home/<USER>/Desktop/myrepo/dde-launchpad/src/gioutils/trashmonitor.h
 mdp:/usr/include/alloca.h
 mdp:/usr/include/asm-generic/errno-base.h
 mdp:/usr/include/asm-generic/errno.h
 mdp:/usr/include/assert.h
 mdp:/usr/include/c++/12/algorithm
 mdp:/usr/include/c++/12/array
 mdp:/usr/include/c++/12/atomic
 mdp:/usr/include/c++/12/backward/auto_ptr.h
 mdp:/usr/include/c++/12/backward/binders.h
 mdp:/usr/include/c++/12/bit
 mdp:/usr/include/c++/12/bits/algorithmfwd.h
 mdp:/usr/include/c++/12/bits/align.h
 mdp:/usr/include/c++/12/bits/alloc_traits.h
 mdp:/usr/include/c++/12/bits/allocated_ptr.h
 mdp:/usr/include/c++/12/bits/allocator.h
 mdp:/usr/include/c++/12/bits/atomic_base.h
 mdp:/usr/include/c++/12/bits/atomic_lockfree_defines.h
 mdp:/usr/include/c++/12/bits/basic_string.h
 mdp:/usr/include/c++/12/bits/basic_string.tcc
 mdp:/usr/include/c++/12/bits/char_traits.h
 mdp:/usr/include/c++/12/bits/charconv.h
 mdp:/usr/include/c++/12/bits/chrono.h
 mdp:/usr/include/c++/12/bits/concept_check.h
 mdp:/usr/include/c++/12/bits/cpp_type_traits.h
 mdp:/usr/include/c++/12/bits/cxxabi_forced.h
 mdp:/usr/include/c++/12/bits/cxxabi_init_exception.h
 mdp:/usr/include/c++/12/bits/enable_special_members.h
 mdp:/usr/include/c++/12/bits/erase_if.h
 mdp:/usr/include/c++/12/bits/exception.h
 mdp:/usr/include/c++/12/bits/exception_defines.h
 mdp:/usr/include/c++/12/bits/exception_ptr.h
 mdp:/usr/include/c++/12/bits/functexcept.h
 mdp:/usr/include/c++/12/bits/functional_hash.h
 mdp:/usr/include/c++/12/bits/hash_bytes.h
 mdp:/usr/include/c++/12/bits/hashtable.h
 mdp:/usr/include/c++/12/bits/hashtable_policy.h
 mdp:/usr/include/c++/12/bits/invoke.h
 mdp:/usr/include/c++/12/bits/ios_base.h
 mdp:/usr/include/c++/12/bits/list.tcc
 mdp:/usr/include/c++/12/bits/locale_classes.h
 mdp:/usr/include/c++/12/bits/locale_classes.tcc
 mdp:/usr/include/c++/12/bits/localefwd.h
 mdp:/usr/include/c++/12/bits/memoryfwd.h
 mdp:/usr/include/c++/12/bits/move.h
 mdp:/usr/include/c++/12/bits/nested_exception.h
 mdp:/usr/include/c++/12/bits/new_allocator.h
 mdp:/usr/include/c++/12/bits/node_handle.h
 mdp:/usr/include/c++/12/bits/ostream_insert.h
 mdp:/usr/include/c++/12/bits/parse_numbers.h
 mdp:/usr/include/c++/12/bits/postypes.h
 mdp:/usr/include/c++/12/bits/predefined_ops.h
 mdp:/usr/include/c++/12/bits/ptr_traits.h
 mdp:/usr/include/c++/12/bits/range_access.h
 mdp:/usr/include/c++/12/bits/refwrap.h
 mdp:/usr/include/c++/12/bits/shared_ptr.h
 mdp:/usr/include/c++/12/bits/shared_ptr_atomic.h
 mdp:/usr/include/c++/12/bits/shared_ptr_base.h
 mdp:/usr/include/c++/12/bits/specfun.h
 mdp:/usr/include/c++/12/bits/std_abs.h
 mdp:/usr/include/c++/12/bits/std_function.h
 mdp:/usr/include/c++/12/bits/stl_algo.h
 mdp:/usr/include/c++/12/bits/stl_algobase.h
 mdp:/usr/include/c++/12/bits/stl_bvector.h
 mdp:/usr/include/c++/12/bits/stl_construct.h
 mdp:/usr/include/c++/12/bits/stl_function.h
 mdp:/usr/include/c++/12/bits/stl_heap.h
 mdp:/usr/include/c++/12/bits/stl_iterator.h
 mdp:/usr/include/c++/12/bits/stl_iterator_base_funcs.h
 mdp:/usr/include/c++/12/bits/stl_iterator_base_types.h
 mdp:/usr/include/c++/12/bits/stl_list.h
 mdp:/usr/include/c++/12/bits/stl_map.h
 mdp:/usr/include/c++/12/bits/stl_multimap.h
 mdp:/usr/include/c++/12/bits/stl_numeric.h
 mdp:/usr/include/c++/12/bits/stl_pair.h
 mdp:/usr/include/c++/12/bits/stl_raw_storage_iter.h
 mdp:/usr/include/c++/12/bits/stl_relops.h
 mdp:/usr/include/c++/12/bits/stl_tempbuf.h
 mdp:/usr/include/c++/12/bits/stl_tree.h
 mdp:/usr/include/c++/12/bits/stl_uninitialized.h
 mdp:/usr/include/c++/12/bits/stl_vector.h
 mdp:/usr/include/c++/12/bits/stream_iterator.h
 mdp:/usr/include/c++/12/bits/streambuf.tcc
 mdp:/usr/include/c++/12/bits/streambuf_iterator.h
 mdp:/usr/include/c++/12/bits/string_view.tcc
 mdp:/usr/include/c++/12/bits/stringfwd.h
 mdp:/usr/include/c++/12/bits/uniform_int_dist.h
 mdp:/usr/include/c++/12/bits/unique_ptr.h
 mdp:/usr/include/c++/12/bits/unordered_map.h
 mdp:/usr/include/c++/12/bits/uses_allocator.h
 mdp:/usr/include/c++/12/bits/utility.h
 mdp:/usr/include/c++/12/bits/vector.tcc
 mdp:/usr/include/c++/12/cctype
 mdp:/usr/include/c++/12/cerrno
 mdp:/usr/include/c++/12/chrono
 mdp:/usr/include/c++/12/clocale
 mdp:/usr/include/c++/12/cmath
 mdp:/usr/include/c++/12/compare
 mdp:/usr/include/c++/12/cstddef
 mdp:/usr/include/c++/12/cstdint
 mdp:/usr/include/c++/12/cstdio
 mdp:/usr/include/c++/12/cstdlib
 mdp:/usr/include/c++/12/cstring
 mdp:/usr/include/c++/12/ctime
 mdp:/usr/include/c++/12/cwchar
 mdp:/usr/include/c++/12/debug/assertions.h
 mdp:/usr/include/c++/12/debug/debug.h
 mdp:/usr/include/c++/12/exception
 mdp:/usr/include/c++/12/ext/aligned_buffer.h
 mdp:/usr/include/c++/12/ext/alloc_traits.h
 mdp:/usr/include/c++/12/ext/atomicity.h
 mdp:/usr/include/c++/12/ext/concurrence.h
 mdp:/usr/include/c++/12/ext/numeric_traits.h
 mdp:/usr/include/c++/12/ext/string_conversions.h
 mdp:/usr/include/c++/12/ext/type_traits.h
 mdp:/usr/include/c++/12/functional
 mdp:/usr/include/c++/12/initializer_list
 mdp:/usr/include/c++/12/iosfwd
 mdp:/usr/include/c++/12/iterator
 mdp:/usr/include/c++/12/limits
 mdp:/usr/include/c++/12/list
 mdp:/usr/include/c++/12/map
 mdp:/usr/include/c++/12/memory
 mdp:/usr/include/c++/12/new
 mdp:/usr/include/c++/12/numeric
 mdp:/usr/include/c++/12/optional
 mdp:/usr/include/c++/12/pstl/execution_defs.h
 mdp:/usr/include/c++/12/pstl/glue_algorithm_defs.h
 mdp:/usr/include/c++/12/pstl/glue_memory_defs.h
 mdp:/usr/include/c++/12/pstl/glue_numeric_defs.h
 mdp:/usr/include/c++/12/ratio
 mdp:/usr/include/c++/12/stdexcept
 mdp:/usr/include/c++/12/streambuf
 mdp:/usr/include/c++/12/string
 mdp:/usr/include/c++/12/string_view
 mdp:/usr/include/c++/12/system_error
 mdp:/usr/include/c++/12/tr1/bessel_function.tcc
 mdp:/usr/include/c++/12/tr1/beta_function.tcc
 mdp:/usr/include/c++/12/tr1/ell_integral.tcc
 mdp:/usr/include/c++/12/tr1/exp_integral.tcc
 mdp:/usr/include/c++/12/tr1/gamma.tcc
 mdp:/usr/include/c++/12/tr1/hypergeometric.tcc
 mdp:/usr/include/c++/12/tr1/legendre_function.tcc
 mdp:/usr/include/c++/12/tr1/modified_bessel_func.tcc
 mdp:/usr/include/c++/12/tr1/poly_hermite.tcc
 mdp:/usr/include/c++/12/tr1/poly_laguerre.tcc
 mdp:/usr/include/c++/12/tr1/riemann_zeta.tcc
 mdp:/usr/include/c++/12/tr1/special_function_util.h
 mdp:/usr/include/c++/12/tuple
 mdp:/usr/include/c++/12/type_traits
 mdp:/usr/include/c++/12/typeinfo
 mdp:/usr/include/c++/12/unordered_map
 mdp:/usr/include/c++/12/utility
 mdp:/usr/include/c++/12/variant
 mdp:/usr/include/c++/12/vector
 mdp:/usr/include/ctype.h
 mdp:/usr/include/dirent.h
 mdp:/usr/include/endian.h
 mdp:/usr/include/errno.h
 mdp:/usr/include/features-time64.h
 mdp:/usr/include/features.h
 mdp:/usr/include/glib-2.0/gio/gaction.h
 mdp:/usr/include/glib-2.0/gio/gactiongroup.h
 mdp:/usr/include/glib-2.0/gio/gactiongroupexporter.h
 mdp:/usr/include/glib-2.0/gio/gactionmap.h
 mdp:/usr/include/glib-2.0/gio/gappinfo.h
 mdp:/usr/include/glib-2.0/gio/gapplication.h
 mdp:/usr/include/glib-2.0/gio/gapplicationcommandline.h
 mdp:/usr/include/glib-2.0/gio/gasyncinitable.h
 mdp:/usr/include/glib-2.0/gio/gasyncresult.h
 mdp:/usr/include/glib-2.0/gio/gbufferedinputstream.h
 mdp:/usr/include/glib-2.0/gio/gbufferedoutputstream.h
 mdp:/usr/include/glib-2.0/gio/gbytesicon.h
 mdp:/usr/include/glib-2.0/gio/gcancellable.h
 mdp:/usr/include/glib-2.0/gio/gcharsetconverter.h
 mdp:/usr/include/glib-2.0/gio/gcontenttype.h
 mdp:/usr/include/glib-2.0/gio/gconverter.h
 mdp:/usr/include/glib-2.0/gio/gconverterinputstream.h
 mdp:/usr/include/glib-2.0/gio/gconverteroutputstream.h
 mdp:/usr/include/glib-2.0/gio/gcredentials.h
 mdp:/usr/include/glib-2.0/gio/gdatagrambased.h
 mdp:/usr/include/glib-2.0/gio/gdatainputstream.h
 mdp:/usr/include/glib-2.0/gio/gdataoutputstream.h
 mdp:/usr/include/glib-2.0/gio/gdbusactiongroup.h
 mdp:/usr/include/glib-2.0/gio/gdbusaddress.h
 mdp:/usr/include/glib-2.0/gio/gdbusauthobserver.h
 mdp:/usr/include/glib-2.0/gio/gdbusconnection.h
 mdp:/usr/include/glib-2.0/gio/gdbuserror.h
 mdp:/usr/include/glib-2.0/gio/gdbusinterface.h
 mdp:/usr/include/glib-2.0/gio/gdbusinterfaceskeleton.h
 mdp:/usr/include/glib-2.0/gio/gdbusintrospection.h
 mdp:/usr/include/glib-2.0/gio/gdbusmenumodel.h
 mdp:/usr/include/glib-2.0/gio/gdbusmessage.h
 mdp:/usr/include/glib-2.0/gio/gdbusmethodinvocation.h
 mdp:/usr/include/glib-2.0/gio/gdbusnameowning.h
 mdp:/usr/include/glib-2.0/gio/gdbusnamewatching.h
 mdp:/usr/include/glib-2.0/gio/gdbusobject.h
 mdp:/usr/include/glib-2.0/gio/gdbusobjectmanager.h
 mdp:/usr/include/glib-2.0/gio/gdbusobjectmanagerclient.h
 mdp:/usr/include/glib-2.0/gio/gdbusobjectmanagerserver.h
 mdp:/usr/include/glib-2.0/gio/gdbusobjectproxy.h
 mdp:/usr/include/glib-2.0/gio/gdbusobjectskeleton.h
 mdp:/usr/include/glib-2.0/gio/gdbusproxy.h
 mdp:/usr/include/glib-2.0/gio/gdbusserver.h
 mdp:/usr/include/glib-2.0/gio/gdbusutils.h
 mdp:/usr/include/glib-2.0/gio/gdebugcontroller.h
 mdp:/usr/include/glib-2.0/gio/gdebugcontrollerdbus.h
 mdp:/usr/include/glib-2.0/gio/gdrive.h
 mdp:/usr/include/glib-2.0/gio/gdtlsclientconnection.h
 mdp:/usr/include/glib-2.0/gio/gdtlsconnection.h
 mdp:/usr/include/glib-2.0/gio/gdtlsserverconnection.h
 mdp:/usr/include/glib-2.0/gio/gemblem.h
 mdp:/usr/include/glib-2.0/gio/gemblemedicon.h
 mdp:/usr/include/glib-2.0/gio/gfile.h
 mdp:/usr/include/glib-2.0/gio/gfileattribute.h
 mdp:/usr/include/glib-2.0/gio/gfileenumerator.h
 mdp:/usr/include/glib-2.0/gio/gfileicon.h
 mdp:/usr/include/glib-2.0/gio/gfileinfo.h
 mdp:/usr/include/glib-2.0/gio/gfileinputstream.h
 mdp:/usr/include/glib-2.0/gio/gfileiostream.h
 mdp:/usr/include/glib-2.0/gio/gfilemonitor.h
 mdp:/usr/include/glib-2.0/gio/gfilenamecompleter.h
 mdp:/usr/include/glib-2.0/gio/gfileoutputstream.h
 mdp:/usr/include/glib-2.0/gio/gfilterinputstream.h
 mdp:/usr/include/glib-2.0/gio/gfilteroutputstream.h
 mdp:/usr/include/glib-2.0/gio/gicon.h
 mdp:/usr/include/glib-2.0/gio/ginetaddress.h
 mdp:/usr/include/glib-2.0/gio/ginetaddressmask.h
 mdp:/usr/include/glib-2.0/gio/ginetsocketaddress.h
 mdp:/usr/include/glib-2.0/gio/ginitable.h
 mdp:/usr/include/glib-2.0/gio/ginputstream.h
 mdp:/usr/include/glib-2.0/gio/gio-autocleanups.h
 mdp:/usr/include/glib-2.0/gio/gio-visibility.h
 mdp:/usr/include/glib-2.0/gio/gio.h
 mdp:/usr/include/glib-2.0/gio/gioenums.h
 mdp:/usr/include/glib-2.0/gio/gioenumtypes.h
 mdp:/usr/include/glib-2.0/gio/gioerror.h
 mdp:/usr/include/glib-2.0/gio/giomodule.h
 mdp:/usr/include/glib-2.0/gio/gioscheduler.h
 mdp:/usr/include/glib-2.0/gio/giostream.h
 mdp:/usr/include/glib-2.0/gio/giotypes.h
 mdp:/usr/include/glib-2.0/gio/glistmodel.h
 mdp:/usr/include/glib-2.0/gio/gliststore.h
 mdp:/usr/include/glib-2.0/gio/gloadableicon.h
 mdp:/usr/include/glib-2.0/gio/gmemoryinputstream.h
 mdp:/usr/include/glib-2.0/gio/gmemorymonitor.h
 mdp:/usr/include/glib-2.0/gio/gmemoryoutputstream.h
 mdp:/usr/include/glib-2.0/gio/gmenu.h
 mdp:/usr/include/glib-2.0/gio/gmenuexporter.h
 mdp:/usr/include/glib-2.0/gio/gmenumodel.h
 mdp:/usr/include/glib-2.0/gio/gmount.h
 mdp:/usr/include/glib-2.0/gio/gmountoperation.h
 mdp:/usr/include/glib-2.0/gio/gnativesocketaddress.h
 mdp:/usr/include/glib-2.0/gio/gnativevolumemonitor.h
 mdp:/usr/include/glib-2.0/gio/gnetworkaddress.h
 mdp:/usr/include/glib-2.0/gio/gnetworkmonitor.h
 mdp:/usr/include/glib-2.0/gio/gnetworkservice.h
 mdp:/usr/include/glib-2.0/gio/gnotification.h
 mdp:/usr/include/glib-2.0/gio/goutputstream.h
 mdp:/usr/include/glib-2.0/gio/gpermission.h
 mdp:/usr/include/glib-2.0/gio/gpollableinputstream.h
 mdp:/usr/include/glib-2.0/gio/gpollableoutputstream.h
 mdp:/usr/include/glib-2.0/gio/gpollableutils.h
 mdp:/usr/include/glib-2.0/gio/gpowerprofilemonitor.h
 mdp:/usr/include/glib-2.0/gio/gpropertyaction.h
 mdp:/usr/include/glib-2.0/gio/gproxy.h
 mdp:/usr/include/glib-2.0/gio/gproxyaddress.h
 mdp:/usr/include/glib-2.0/gio/gproxyaddressenumerator.h
 mdp:/usr/include/glib-2.0/gio/gproxyresolver.h
 mdp:/usr/include/glib-2.0/gio/gremoteactiongroup.h
 mdp:/usr/include/glib-2.0/gio/gresolver.h
 mdp:/usr/include/glib-2.0/gio/gresource.h
 mdp:/usr/include/glib-2.0/gio/gseekable.h
 mdp:/usr/include/glib-2.0/gio/gsettings.h
 mdp:/usr/include/glib-2.0/gio/gsettingsschema.h
 mdp:/usr/include/glib-2.0/gio/gsimpleaction.h
 mdp:/usr/include/glib-2.0/gio/gsimpleactiongroup.h
 mdp:/usr/include/glib-2.0/gio/gsimpleasyncresult.h
 mdp:/usr/include/glib-2.0/gio/gsimpleiostream.h
 mdp:/usr/include/glib-2.0/gio/gsimplepermission.h
 mdp:/usr/include/glib-2.0/gio/gsimpleproxyresolver.h
 mdp:/usr/include/glib-2.0/gio/gsocket.h
 mdp:/usr/include/glib-2.0/gio/gsocketaddress.h
 mdp:/usr/include/glib-2.0/gio/gsocketaddressenumerator.h
 mdp:/usr/include/glib-2.0/gio/gsocketclient.h
 mdp:/usr/include/glib-2.0/gio/gsocketconnectable.h
 mdp:/usr/include/glib-2.0/gio/gsocketconnection.h
 mdp:/usr/include/glib-2.0/gio/gsocketcontrolmessage.h
 mdp:/usr/include/glib-2.0/gio/gsocketlistener.h
 mdp:/usr/include/glib-2.0/gio/gsocketservice.h
 mdp:/usr/include/glib-2.0/gio/gsrvtarget.h
 mdp:/usr/include/glib-2.0/gio/gsubprocess.h
 mdp:/usr/include/glib-2.0/gio/gsubprocesslauncher.h
 mdp:/usr/include/glib-2.0/gio/gtask.h
 mdp:/usr/include/glib-2.0/gio/gtcpconnection.h
 mdp:/usr/include/glib-2.0/gio/gtcpwrapperconnection.h
 mdp:/usr/include/glib-2.0/gio/gtestdbus.h
 mdp:/usr/include/glib-2.0/gio/gthemedicon.h
 mdp:/usr/include/glib-2.0/gio/gthreadedsocketservice.h
 mdp:/usr/include/glib-2.0/gio/gtlsbackend.h
 mdp:/usr/include/glib-2.0/gio/gtlscertificate.h
 mdp:/usr/include/glib-2.0/gio/gtlsclientconnection.h
 mdp:/usr/include/glib-2.0/gio/gtlsconnection.h
 mdp:/usr/include/glib-2.0/gio/gtlsdatabase.h
 mdp:/usr/include/glib-2.0/gio/gtlsfiledatabase.h
 mdp:/usr/include/glib-2.0/gio/gtlsinteraction.h
 mdp:/usr/include/glib-2.0/gio/gtlspassword.h
 mdp:/usr/include/glib-2.0/gio/gtlsserverconnection.h
 mdp:/usr/include/glib-2.0/gio/gunixconnection.h
 mdp:/usr/include/glib-2.0/gio/gunixcredentialsmessage.h
 mdp:/usr/include/glib-2.0/gio/gunixfdlist.h
 mdp:/usr/include/glib-2.0/gio/gunixsocketaddress.h
 mdp:/usr/include/glib-2.0/gio/gvfs.h
 mdp:/usr/include/glib-2.0/gio/gvolume.h
 mdp:/usr/include/glib-2.0/gio/gvolumemonitor.h
 mdp:/usr/include/glib-2.0/gio/gzlibcompressor.h
 mdp:/usr/include/glib-2.0/gio/gzlibdecompressor.h
 mdp:/usr/include/glib-2.0/glib-object.h
 mdp:/usr/include/glib-2.0/glib.h
 mdp:/usr/include/glib-2.0/glib/deprecated/gallocator.h
 mdp:/usr/include/glib-2.0/glib/deprecated/gcache.h
 mdp:/usr/include/glib-2.0/glib/deprecated/gcompletion.h
 mdp:/usr/include/glib-2.0/glib/deprecated/gmain.h
 mdp:/usr/include/glib-2.0/glib/deprecated/grel.h
 mdp:/usr/include/glib-2.0/glib/deprecated/gthread.h
 mdp:/usr/include/glib-2.0/glib/galloca.h
 mdp:/usr/include/glib-2.0/glib/garray.h
 mdp:/usr/include/glib-2.0/glib/gasyncqueue.h
 mdp:/usr/include/glib-2.0/glib/gatomic.h
 mdp:/usr/include/glib-2.0/glib/gbacktrace.h
 mdp:/usr/include/glib-2.0/glib/gbase64.h
 mdp:/usr/include/glib-2.0/glib/gbitlock.h
 mdp:/usr/include/glib-2.0/glib/gbookmarkfile.h
 mdp:/usr/include/glib-2.0/glib/gbytes.h
 mdp:/usr/include/glib-2.0/glib/gcharset.h
 mdp:/usr/include/glib-2.0/glib/gchecksum.h
 mdp:/usr/include/glib-2.0/glib/gconvert.h
 mdp:/usr/include/glib-2.0/glib/gdataset.h
 mdp:/usr/include/glib-2.0/glib/gdate.h
 mdp:/usr/include/glib-2.0/glib/gdatetime.h
 mdp:/usr/include/glib-2.0/glib/gdir.h
 mdp:/usr/include/glib-2.0/glib/genviron.h
 mdp:/usr/include/glib-2.0/glib/gerror.h
 mdp:/usr/include/glib-2.0/glib/gfileutils.h
 mdp:/usr/include/glib-2.0/glib/ggettext.h
 mdp:/usr/include/glib-2.0/glib/ghash.h
 mdp:/usr/include/glib-2.0/glib/ghmac.h
 mdp:/usr/include/glib-2.0/glib/ghook.h
 mdp:/usr/include/glib-2.0/glib/ghostutils.h
 mdp:/usr/include/glib-2.0/glib/giochannel.h
 mdp:/usr/include/glib-2.0/glib/gkeyfile.h
 mdp:/usr/include/glib-2.0/glib/glib-autocleanups.h
 mdp:/usr/include/glib-2.0/glib/glib-typeof.h
 mdp:/usr/include/glib-2.0/glib/glib-visibility.h
 mdp:/usr/include/glib-2.0/glib/glist.h
 mdp:/usr/include/glib-2.0/glib/gmacros.h
 mdp:/usr/include/glib-2.0/glib/gmain.h
 mdp:/usr/include/glib-2.0/glib/gmappedfile.h
 mdp:/usr/include/glib-2.0/glib/gmarkup.h
 mdp:/usr/include/glib-2.0/glib/gmem.h
 mdp:/usr/include/glib-2.0/glib/gmessages.h
 mdp:/usr/include/glib-2.0/glib/gnode.h
 mdp:/usr/include/glib-2.0/glib/goption.h
 mdp:/usr/include/glib-2.0/glib/gpathbuf.h
 mdp:/usr/include/glib-2.0/glib/gpattern.h
 mdp:/usr/include/glib-2.0/glib/gpoll.h
 mdp:/usr/include/glib-2.0/glib/gprimes.h
 mdp:/usr/include/glib-2.0/glib/gqsort.h
 mdp:/usr/include/glib-2.0/glib/gquark.h
 mdp:/usr/include/glib-2.0/glib/gqueue.h
 mdp:/usr/include/glib-2.0/glib/grand.h
 mdp:/usr/include/glib-2.0/glib/grcbox.h
 mdp:/usr/include/glib-2.0/glib/grefcount.h
 mdp:/usr/include/glib-2.0/glib/grefstring.h
 mdp:/usr/include/glib-2.0/glib/gregex.h
 mdp:/usr/include/glib-2.0/glib/gscanner.h
 mdp:/usr/include/glib-2.0/glib/gsequence.h
 mdp:/usr/include/glib-2.0/glib/gshell.h
 mdp:/usr/include/glib-2.0/glib/gslice.h
 mdp:/usr/include/glib-2.0/glib/gslist.h
 mdp:/usr/include/glib-2.0/glib/gspawn.h
 mdp:/usr/include/glib-2.0/glib/gstrfuncs.h
 mdp:/usr/include/glib-2.0/glib/gstring.h
 mdp:/usr/include/glib-2.0/glib/gstringchunk.h
 mdp:/usr/include/glib-2.0/glib/gstrvbuilder.h
 mdp:/usr/include/glib-2.0/glib/gtestutils.h
 mdp:/usr/include/glib-2.0/glib/gthread.h
 mdp:/usr/include/glib-2.0/glib/gthreadpool.h
 mdp:/usr/include/glib-2.0/glib/gtimer.h
 mdp:/usr/include/glib-2.0/glib/gtimezone.h
 mdp:/usr/include/glib-2.0/glib/gtrashstack.h
 mdp:/usr/include/glib-2.0/glib/gtree.h
 mdp:/usr/include/glib-2.0/glib/gtypes.h
 mdp:/usr/include/glib-2.0/glib/gunicode.h
 mdp:/usr/include/glib-2.0/glib/guri.h
 mdp:/usr/include/glib-2.0/glib/gutils.h
 mdp:/usr/include/glib-2.0/glib/guuid.h
 mdp:/usr/include/glib-2.0/glib/gvariant.h
 mdp:/usr/include/glib-2.0/glib/gvarianttype.h
 mdp:/usr/include/glib-2.0/glib/gversion.h
 mdp:/usr/include/glib-2.0/glib/gversionmacros.h
 mdp:/usr/include/glib-2.0/gmodule.h
 mdp:/usr/include/glib-2.0/gmodule/gmodule-visibility.h
 mdp:/usr/include/glib-2.0/gobject/gbinding.h
 mdp:/usr/include/glib-2.0/gobject/gbindinggroup.h
 mdp:/usr/include/glib-2.0/gobject/gboxed.h
 mdp:/usr/include/glib-2.0/gobject/gclosure.h
 mdp:/usr/include/glib-2.0/gobject/genums.h
 mdp:/usr/include/glib-2.0/gobject/glib-enumtypes.h
 mdp:/usr/include/glib-2.0/gobject/glib-types.h
 mdp:/usr/include/glib-2.0/gobject/gmarshal.h
 mdp:/usr/include/glib-2.0/gobject/gobject-autocleanups.h
 mdp:/usr/include/glib-2.0/gobject/gobject-visibility.h
 mdp:/usr/include/glib-2.0/gobject/gobject.h
 mdp:/usr/include/glib-2.0/gobject/gparam.h
 mdp:/usr/include/glib-2.0/gobject/gparamspecs.h
 mdp:/usr/include/glib-2.0/gobject/gsignal.h
 mdp:/usr/include/glib-2.0/gobject/gsignalgroup.h
 mdp:/usr/include/glib-2.0/gobject/gsourceclosure.h
 mdp:/usr/include/glib-2.0/gobject/gtype.h
 mdp:/usr/include/glib-2.0/gobject/gtypemodule.h
 mdp:/usr/include/glib-2.0/gobject/gtypeplugin.h
 mdp:/usr/include/glib-2.0/gobject/gvalue.h
 mdp:/usr/include/glib-2.0/gobject/gvaluearray.h
 mdp:/usr/include/glib-2.0/gobject/gvaluetypes.h
 mdp:/usr/include/limits.h
 mdp:/usr/include/linux/errno.h
 mdp:/usr/include/linux/limits.h
 mdp:/usr/include/locale.h
 mdp:/usr/include/pthread.h
 mdp:/usr/include/sched.h
 mdp:/usr/include/signal.h
 mdp:/usr/include/stdc-predef.h
 mdp:/usr/include/stdint.h
 mdp:/usr/include/stdio.h
 mdp:/usr/include/stdlib.h
 mdp:/usr/include/string.h
 mdp:/usr/include/strings.h
 mdp:/usr/include/time.h
 mdp:/usr/include/unistd.h
 mdp:/usr/include/wchar.h
 mdp:/usr/include/x86_64-linux-gnu/asm/errno.h
 mdp:/usr/include/x86_64-linux-gnu/bits/atomic_wide_counter.h
 mdp:/usr/include/x86_64-linux-gnu/bits/byteswap.h
 mdp:/usr/include/x86_64-linux-gnu/bits/confname.h
 mdp:/usr/include/x86_64-linux-gnu/bits/cpu-set.h
 mdp:/usr/include/x86_64-linux-gnu/bits/dirent.h
 mdp:/usr/include/x86_64-linux-gnu/bits/dirent_ext.h
 mdp:/usr/include/x86_64-linux-gnu/bits/endian.h
 mdp:/usr/include/x86_64-linux-gnu/bits/endianness.h
 mdp:/usr/include/x86_64-linux-gnu/bits/environments.h
 mdp:/usr/include/x86_64-linux-gnu/bits/errno.h
 mdp:/usr/include/x86_64-linux-gnu/bits/floatn-common.h
 mdp:/usr/include/x86_64-linux-gnu/bits/floatn.h
 mdp:/usr/include/x86_64-linux-gnu/bits/getopt_core.h
 mdp:/usr/include/x86_64-linux-gnu/bits/getopt_posix.h
 mdp:/usr/include/x86_64-linux-gnu/bits/libc-header-start.h
 mdp:/usr/include/x86_64-linux-gnu/bits/local_lim.h
 mdp:/usr/include/x86_64-linux-gnu/bits/locale.h
 mdp:/usr/include/x86_64-linux-gnu/bits/long-double.h
 mdp:/usr/include/x86_64-linux-gnu/bits/posix1_lim.h
 mdp:/usr/include/x86_64-linux-gnu/bits/posix2_lim.h
 mdp:/usr/include/x86_64-linux-gnu/bits/posix_opt.h
 mdp:/usr/include/x86_64-linux-gnu/bits/pthread_stack_min-dynamic.h
 mdp:/usr/include/x86_64-linux-gnu/bits/pthreadtypes-arch.h
 mdp:/usr/include/x86_64-linux-gnu/bits/pthreadtypes.h
 mdp:/usr/include/x86_64-linux-gnu/bits/sched.h
 mdp:/usr/include/x86_64-linux-gnu/bits/select.h
 mdp:/usr/include/x86_64-linux-gnu/bits/setjmp.h
 mdp:/usr/include/x86_64-linux-gnu/bits/sigaction.h
 mdp:/usr/include/x86_64-linux-gnu/bits/sigcontext.h
 mdp:/usr/include/x86_64-linux-gnu/bits/sigevent-consts.h
 mdp:/usr/include/x86_64-linux-gnu/bits/siginfo-arch.h
 mdp:/usr/include/x86_64-linux-gnu/bits/siginfo-consts-arch.h
 mdp:/usr/include/x86_64-linux-gnu/bits/siginfo-consts.h
 mdp:/usr/include/x86_64-linux-gnu/bits/signal_ext.h
 mdp:/usr/include/x86_64-linux-gnu/bits/signum-arch.h
 mdp:/usr/include/x86_64-linux-gnu/bits/signum-generic.h
 mdp:/usr/include/x86_64-linux-gnu/bits/sigstack.h
 mdp:/usr/include/x86_64-linux-gnu/bits/sigstksz.h
 mdp:/usr/include/x86_64-linux-gnu/bits/sigthread.h
 mdp:/usr/include/x86_64-linux-gnu/bits/ss_flags.h
 mdp:/usr/include/x86_64-linux-gnu/bits/stdint-intn.h
 mdp:/usr/include/x86_64-linux-gnu/bits/stdint-uintn.h
 mdp:/usr/include/x86_64-linux-gnu/bits/stdio_lim.h
 mdp:/usr/include/x86_64-linux-gnu/bits/stdlib-float.h
 mdp:/usr/include/x86_64-linux-gnu/bits/struct_mutex.h
 mdp:/usr/include/x86_64-linux-gnu/bits/struct_rwlock.h
 mdp:/usr/include/x86_64-linux-gnu/bits/thread-shared-types.h
 mdp:/usr/include/x86_64-linux-gnu/bits/time.h
 mdp:/usr/include/x86_64-linux-gnu/bits/time64.h
 mdp:/usr/include/x86_64-linux-gnu/bits/timesize.h
 mdp:/usr/include/x86_64-linux-gnu/bits/timex.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/FILE.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/__FILE.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/__fpos64_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/__fpos_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/__locale_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/__mbstate_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/__sigset_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/__sigval_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/clock_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/clockid_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/cookie_io_functions_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/error_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/locale_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/mbstate_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/sig_atomic_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/sigevent_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/siginfo_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/sigset_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/sigval_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/stack_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/struct_FILE.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/struct___jmp_buf_tag.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/struct_itimerspec.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/struct_sched_param.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/struct_sigstack.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/struct_timespec.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/struct_timeval.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/struct_tm.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/time_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/timer_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/wint_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/typesizes.h
 mdp:/usr/include/x86_64-linux-gnu/bits/uintn-identity.h
 mdp:/usr/include/x86_64-linux-gnu/bits/uio_lim.h
 mdp:/usr/include/x86_64-linux-gnu/bits/unistd_ext.h
 mdp:/usr/include/x86_64-linux-gnu/bits/waitflags.h
 mdp:/usr/include/x86_64-linux-gnu/bits/waitstatus.h
 mdp:/usr/include/x86_64-linux-gnu/bits/wchar.h
 mdp:/usr/include/x86_64-linux-gnu/bits/wordsize.h
 mdp:/usr/include/x86_64-linux-gnu/bits/xopen_lim.h
 mdp:/usr/include/x86_64-linux-gnu/c++/12/bits/atomic_word.h
 mdp:/usr/include/x86_64-linux-gnu/c++/12/bits/c++allocator.h
 mdp:/usr/include/x86_64-linux-gnu/c++/12/bits/c++config.h
 mdp:/usr/include/x86_64-linux-gnu/c++/12/bits/c++locale.h
 mdp:/usr/include/x86_64-linux-gnu/c++/12/bits/cpu_defines.h
 mdp:/usr/include/x86_64-linux-gnu/c++/12/bits/error_constants.h
 mdp:/usr/include/x86_64-linux-gnu/c++/12/bits/gthr-default.h
 mdp:/usr/include/x86_64-linux-gnu/c++/12/bits/gthr.h
 mdp:/usr/include/x86_64-linux-gnu/c++/12/bits/os_defines.h
 mdp:/usr/include/x86_64-linux-gnu/gnu/stubs-64.h
 mdp:/usr/include/x86_64-linux-gnu/gnu/stubs.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/QObject
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/q20functional.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/q20memory.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/q20type_traits.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qalgorithms.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qanystringview.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qarraydata.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qarraydataops.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qarraydatapointer.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qassert.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qatomic.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qatomic_cxx11.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qbasicatomic.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qbindingstorage.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qbytearray.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qbytearrayalgorithms.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qbytearraylist.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qbytearrayview.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qchar.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qcompare.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qcompare_impl.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qcomparehelpers.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qcompilerdetection.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qconfig.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qconstructormacros.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qcontainerfwd.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qcontainerinfo.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qcontainertools_impl.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qdarwinhelpers.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qdatastream.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qexceptionhandling.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qflags.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qfloat16.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qforeach.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qfunctionaltools_impl.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qfunctionpointer.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qgenericatomic.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qglobal.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qglobalstatic.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qhashfunctions.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qiodevicebase.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qiterable.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qiterator.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qlatin1stringview.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qlist.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qlogging.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qmalloc.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qmath.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qmetacontainer.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qmetatype.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qminmax.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qnamespace.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qnumeric.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qobject.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qobject_impl.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qobjectdefs.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qobjectdefs_impl.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qoverload.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qpair.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qprocessordetection.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qrefcount.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qscopedpointer.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qscopeguard.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qstring.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qstringalgorithms.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qstringbuilder.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qstringconverter.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qstringconverter_base.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qstringfwd.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qstringlist.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qstringliteral.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qstringmatcher.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qstringtokenizer.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qstringview.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qswap.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qsysinfo.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qsystemdetection.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtaggedpointer.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtclasshelpermacros.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtconfiginclude.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtconfigmacros.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtcore-config.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtcoreexports.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtdeprecationdefinitions.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtdeprecationmarkers.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtenvironmentvariables.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtmetamacros.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtnoop.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtpreprocessorsupport.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtresource.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qttranslation.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qttypetraits.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtversion.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtversionchecks.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtypeinfo.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtypes.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qutf8stringview.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qversiontagging.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qxptype_traits.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qyieldcpu.h
 mdp:/usr/include/x86_64-linux-gnu/sys/cdefs.h
 mdp:/usr/include/x86_64-linux-gnu/sys/select.h
 mdp:/usr/include/x86_64-linux-gnu/sys/types.h
 mdp:/usr/include/x86_64-linux-gnu/sys/ucontext.h
 mdp:/usr/lib/gcc/x86_64-linux-gnu/12/include/float.h
 mdp:/usr/lib/gcc/x86_64-linux-gnu/12/include/stdarg.h
 mdp:/usr/lib/gcc/x86_64-linux-gnu/12/include/stdbool.h
 mdp:/usr/lib/gcc/x86_64-linux-gnu/12/include/stddef.h
 mdp:/usr/lib/x86_64-linux-gnu/glib-2.0/include/glibconfig.h
/home/<USER>/Desktop/myrepo/dde-launchpad/src/gioutils/appinfo.cpp
/home/<USER>/Desktop/myrepo/dde-launchpad/src/gioutils/appinfomonitor.h
 mmc:Q_OBJECT
 mdp:/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/gioutils/gio-utils_autogen/moc_predefs.h
 mdp:/home/<USER>/Desktop/myrepo/dde-launchpad/src/gioutils/appinfomonitor.h
 mdp:/usr/include/alloca.h
 mdp:/usr/include/asm-generic/errno-base.h
 mdp:/usr/include/asm-generic/errno.h
 mdp:/usr/include/assert.h
 mdp:/usr/include/c++/12/algorithm
 mdp:/usr/include/c++/12/array
 mdp:/usr/include/c++/12/atomic
 mdp:/usr/include/c++/12/backward/auto_ptr.h
 mdp:/usr/include/c++/12/backward/binders.h
 mdp:/usr/include/c++/12/bit
 mdp:/usr/include/c++/12/bits/algorithmfwd.h
 mdp:/usr/include/c++/12/bits/align.h
 mdp:/usr/include/c++/12/bits/alloc_traits.h
 mdp:/usr/include/c++/12/bits/allocated_ptr.h
 mdp:/usr/include/c++/12/bits/allocator.h
 mdp:/usr/include/c++/12/bits/atomic_base.h
 mdp:/usr/include/c++/12/bits/atomic_lockfree_defines.h
 mdp:/usr/include/c++/12/bits/basic_string.h
 mdp:/usr/include/c++/12/bits/basic_string.tcc
 mdp:/usr/include/c++/12/bits/char_traits.h
 mdp:/usr/include/c++/12/bits/charconv.h
 mdp:/usr/include/c++/12/bits/chrono.h
 mdp:/usr/include/c++/12/bits/concept_check.h
 mdp:/usr/include/c++/12/bits/cpp_type_traits.h
 mdp:/usr/include/c++/12/bits/cxxabi_forced.h
 mdp:/usr/include/c++/12/bits/cxxabi_init_exception.h
 mdp:/usr/include/c++/12/bits/enable_special_members.h
 mdp:/usr/include/c++/12/bits/erase_if.h
 mdp:/usr/include/c++/12/bits/exception.h
 mdp:/usr/include/c++/12/bits/exception_defines.h
 mdp:/usr/include/c++/12/bits/exception_ptr.h
 mdp:/usr/include/c++/12/bits/functexcept.h
 mdp:/usr/include/c++/12/bits/functional_hash.h
 mdp:/usr/include/c++/12/bits/hash_bytes.h
 mdp:/usr/include/c++/12/bits/hashtable.h
 mdp:/usr/include/c++/12/bits/hashtable_policy.h
 mdp:/usr/include/c++/12/bits/invoke.h
 mdp:/usr/include/c++/12/bits/ios_base.h
 mdp:/usr/include/c++/12/bits/list.tcc
 mdp:/usr/include/c++/12/bits/locale_classes.h
 mdp:/usr/include/c++/12/bits/locale_classes.tcc
 mdp:/usr/include/c++/12/bits/localefwd.h
 mdp:/usr/include/c++/12/bits/memoryfwd.h
 mdp:/usr/include/c++/12/bits/move.h
 mdp:/usr/include/c++/12/bits/nested_exception.h
 mdp:/usr/include/c++/12/bits/new_allocator.h
 mdp:/usr/include/c++/12/bits/node_handle.h
 mdp:/usr/include/c++/12/bits/ostream_insert.h
 mdp:/usr/include/c++/12/bits/parse_numbers.h
 mdp:/usr/include/c++/12/bits/postypes.h
 mdp:/usr/include/c++/12/bits/predefined_ops.h
 mdp:/usr/include/c++/12/bits/ptr_traits.h
 mdp:/usr/include/c++/12/bits/range_access.h
 mdp:/usr/include/c++/12/bits/refwrap.h
 mdp:/usr/include/c++/12/bits/shared_ptr.h
 mdp:/usr/include/c++/12/bits/shared_ptr_atomic.h
 mdp:/usr/include/c++/12/bits/shared_ptr_base.h
 mdp:/usr/include/c++/12/bits/specfun.h
 mdp:/usr/include/c++/12/bits/std_abs.h
 mdp:/usr/include/c++/12/bits/std_function.h
 mdp:/usr/include/c++/12/bits/stl_algo.h
 mdp:/usr/include/c++/12/bits/stl_algobase.h
 mdp:/usr/include/c++/12/bits/stl_bvector.h
 mdp:/usr/include/c++/12/bits/stl_construct.h
 mdp:/usr/include/c++/12/bits/stl_function.h
 mdp:/usr/include/c++/12/bits/stl_heap.h
 mdp:/usr/include/c++/12/bits/stl_iterator.h
 mdp:/usr/include/c++/12/bits/stl_iterator_base_funcs.h
 mdp:/usr/include/c++/12/bits/stl_iterator_base_types.h
 mdp:/usr/include/c++/12/bits/stl_list.h
 mdp:/usr/include/c++/12/bits/stl_map.h
 mdp:/usr/include/c++/12/bits/stl_multimap.h
 mdp:/usr/include/c++/12/bits/stl_numeric.h
 mdp:/usr/include/c++/12/bits/stl_pair.h
 mdp:/usr/include/c++/12/bits/stl_raw_storage_iter.h
 mdp:/usr/include/c++/12/bits/stl_relops.h
 mdp:/usr/include/c++/12/bits/stl_tempbuf.h
 mdp:/usr/include/c++/12/bits/stl_tree.h
 mdp:/usr/include/c++/12/bits/stl_uninitialized.h
 mdp:/usr/include/c++/12/bits/stl_vector.h
 mdp:/usr/include/c++/12/bits/stream_iterator.h
 mdp:/usr/include/c++/12/bits/streambuf.tcc
 mdp:/usr/include/c++/12/bits/streambuf_iterator.h
 mdp:/usr/include/c++/12/bits/string_view.tcc
 mdp:/usr/include/c++/12/bits/stringfwd.h
 mdp:/usr/include/c++/12/bits/uniform_int_dist.h
 mdp:/usr/include/c++/12/bits/unique_ptr.h
 mdp:/usr/include/c++/12/bits/unordered_map.h
 mdp:/usr/include/c++/12/bits/uses_allocator.h
 mdp:/usr/include/c++/12/bits/utility.h
 mdp:/usr/include/c++/12/bits/vector.tcc
 mdp:/usr/include/c++/12/cctype
 mdp:/usr/include/c++/12/cerrno
 mdp:/usr/include/c++/12/chrono
 mdp:/usr/include/c++/12/clocale
 mdp:/usr/include/c++/12/cmath
 mdp:/usr/include/c++/12/compare
 mdp:/usr/include/c++/12/cstddef
 mdp:/usr/include/c++/12/cstdint
 mdp:/usr/include/c++/12/cstdio
 mdp:/usr/include/c++/12/cstdlib
 mdp:/usr/include/c++/12/cstring
 mdp:/usr/include/c++/12/ctime
 mdp:/usr/include/c++/12/cwchar
 mdp:/usr/include/c++/12/debug/assertions.h
 mdp:/usr/include/c++/12/debug/debug.h
 mdp:/usr/include/c++/12/exception
 mdp:/usr/include/c++/12/ext/aligned_buffer.h
 mdp:/usr/include/c++/12/ext/alloc_traits.h
 mdp:/usr/include/c++/12/ext/atomicity.h
 mdp:/usr/include/c++/12/ext/concurrence.h
 mdp:/usr/include/c++/12/ext/numeric_traits.h
 mdp:/usr/include/c++/12/ext/string_conversions.h
 mdp:/usr/include/c++/12/ext/type_traits.h
 mdp:/usr/include/c++/12/functional
 mdp:/usr/include/c++/12/initializer_list
 mdp:/usr/include/c++/12/iosfwd
 mdp:/usr/include/c++/12/iterator
 mdp:/usr/include/c++/12/limits
 mdp:/usr/include/c++/12/list
 mdp:/usr/include/c++/12/map
 mdp:/usr/include/c++/12/memory
 mdp:/usr/include/c++/12/new
 mdp:/usr/include/c++/12/numeric
 mdp:/usr/include/c++/12/optional
 mdp:/usr/include/c++/12/pstl/execution_defs.h
 mdp:/usr/include/c++/12/pstl/glue_algorithm_defs.h
 mdp:/usr/include/c++/12/pstl/glue_memory_defs.h
 mdp:/usr/include/c++/12/pstl/glue_numeric_defs.h
 mdp:/usr/include/c++/12/ratio
 mdp:/usr/include/c++/12/stdexcept
 mdp:/usr/include/c++/12/streambuf
 mdp:/usr/include/c++/12/string
 mdp:/usr/include/c++/12/string_view
 mdp:/usr/include/c++/12/system_error
 mdp:/usr/include/c++/12/tr1/bessel_function.tcc
 mdp:/usr/include/c++/12/tr1/beta_function.tcc
 mdp:/usr/include/c++/12/tr1/ell_integral.tcc
 mdp:/usr/include/c++/12/tr1/exp_integral.tcc
 mdp:/usr/include/c++/12/tr1/gamma.tcc
 mdp:/usr/include/c++/12/tr1/hypergeometric.tcc
 mdp:/usr/include/c++/12/tr1/legendre_function.tcc
 mdp:/usr/include/c++/12/tr1/modified_bessel_func.tcc
 mdp:/usr/include/c++/12/tr1/poly_hermite.tcc
 mdp:/usr/include/c++/12/tr1/poly_laguerre.tcc
 mdp:/usr/include/c++/12/tr1/riemann_zeta.tcc
 mdp:/usr/include/c++/12/tr1/special_function_util.h
 mdp:/usr/include/c++/12/tuple
 mdp:/usr/include/c++/12/type_traits
 mdp:/usr/include/c++/12/typeinfo
 mdp:/usr/include/c++/12/unordered_map
 mdp:/usr/include/c++/12/utility
 mdp:/usr/include/c++/12/variant
 mdp:/usr/include/c++/12/vector
 mdp:/usr/include/ctype.h
 mdp:/usr/include/dirent.h
 mdp:/usr/include/endian.h
 mdp:/usr/include/errno.h
 mdp:/usr/include/features-time64.h
 mdp:/usr/include/features.h
 mdp:/usr/include/glib-2.0/gio/gaction.h
 mdp:/usr/include/glib-2.0/gio/gactiongroup.h
 mdp:/usr/include/glib-2.0/gio/gactiongroupexporter.h
 mdp:/usr/include/glib-2.0/gio/gactionmap.h
 mdp:/usr/include/glib-2.0/gio/gappinfo.h
 mdp:/usr/include/glib-2.0/gio/gapplication.h
 mdp:/usr/include/glib-2.0/gio/gapplicationcommandline.h
 mdp:/usr/include/glib-2.0/gio/gasyncinitable.h
 mdp:/usr/include/glib-2.0/gio/gasyncresult.h
 mdp:/usr/include/glib-2.0/gio/gbufferedinputstream.h
 mdp:/usr/include/glib-2.0/gio/gbufferedoutputstream.h
 mdp:/usr/include/glib-2.0/gio/gbytesicon.h
 mdp:/usr/include/glib-2.0/gio/gcancellable.h
 mdp:/usr/include/glib-2.0/gio/gcharsetconverter.h
 mdp:/usr/include/glib-2.0/gio/gcontenttype.h
 mdp:/usr/include/glib-2.0/gio/gconverter.h
 mdp:/usr/include/glib-2.0/gio/gconverterinputstream.h
 mdp:/usr/include/glib-2.0/gio/gconverteroutputstream.h
 mdp:/usr/include/glib-2.0/gio/gcredentials.h
 mdp:/usr/include/glib-2.0/gio/gdatagrambased.h
 mdp:/usr/include/glib-2.0/gio/gdatainputstream.h
 mdp:/usr/include/glib-2.0/gio/gdataoutputstream.h
 mdp:/usr/include/glib-2.0/gio/gdbusactiongroup.h
 mdp:/usr/include/glib-2.0/gio/gdbusaddress.h
 mdp:/usr/include/glib-2.0/gio/gdbusauthobserver.h
 mdp:/usr/include/glib-2.0/gio/gdbusconnection.h
 mdp:/usr/include/glib-2.0/gio/gdbuserror.h
 mdp:/usr/include/glib-2.0/gio/gdbusinterface.h
 mdp:/usr/include/glib-2.0/gio/gdbusinterfaceskeleton.h
 mdp:/usr/include/glib-2.0/gio/gdbusintrospection.h
 mdp:/usr/include/glib-2.0/gio/gdbusmenumodel.h
 mdp:/usr/include/glib-2.0/gio/gdbusmessage.h
 mdp:/usr/include/glib-2.0/gio/gdbusmethodinvocation.h
 mdp:/usr/include/glib-2.0/gio/gdbusnameowning.h
 mdp:/usr/include/glib-2.0/gio/gdbusnamewatching.h
 mdp:/usr/include/glib-2.0/gio/gdbusobject.h
 mdp:/usr/include/glib-2.0/gio/gdbusobjectmanager.h
 mdp:/usr/include/glib-2.0/gio/gdbusobjectmanagerclient.h
 mdp:/usr/include/glib-2.0/gio/gdbusobjectmanagerserver.h
 mdp:/usr/include/glib-2.0/gio/gdbusobjectproxy.h
 mdp:/usr/include/glib-2.0/gio/gdbusobjectskeleton.h
 mdp:/usr/include/glib-2.0/gio/gdbusproxy.h
 mdp:/usr/include/glib-2.0/gio/gdbusserver.h
 mdp:/usr/include/glib-2.0/gio/gdbusutils.h
 mdp:/usr/include/glib-2.0/gio/gdebugcontroller.h
 mdp:/usr/include/glib-2.0/gio/gdebugcontrollerdbus.h
 mdp:/usr/include/glib-2.0/gio/gdrive.h
 mdp:/usr/include/glib-2.0/gio/gdtlsclientconnection.h
 mdp:/usr/include/glib-2.0/gio/gdtlsconnection.h
 mdp:/usr/include/glib-2.0/gio/gdtlsserverconnection.h
 mdp:/usr/include/glib-2.0/gio/gemblem.h
 mdp:/usr/include/glib-2.0/gio/gemblemedicon.h
 mdp:/usr/include/glib-2.0/gio/gfile.h
 mdp:/usr/include/glib-2.0/gio/gfileattribute.h
 mdp:/usr/include/glib-2.0/gio/gfileenumerator.h
 mdp:/usr/include/glib-2.0/gio/gfileicon.h
 mdp:/usr/include/glib-2.0/gio/gfileinfo.h
 mdp:/usr/include/glib-2.0/gio/gfileinputstream.h
 mdp:/usr/include/glib-2.0/gio/gfileiostream.h
 mdp:/usr/include/glib-2.0/gio/gfilemonitor.h
 mdp:/usr/include/glib-2.0/gio/gfilenamecompleter.h
 mdp:/usr/include/glib-2.0/gio/gfileoutputstream.h
 mdp:/usr/include/glib-2.0/gio/gfilterinputstream.h
 mdp:/usr/include/glib-2.0/gio/gfilteroutputstream.h
 mdp:/usr/include/glib-2.0/gio/gicon.h
 mdp:/usr/include/glib-2.0/gio/ginetaddress.h
 mdp:/usr/include/glib-2.0/gio/ginetaddressmask.h
 mdp:/usr/include/glib-2.0/gio/ginetsocketaddress.h
 mdp:/usr/include/glib-2.0/gio/ginitable.h
 mdp:/usr/include/glib-2.0/gio/ginputstream.h
 mdp:/usr/include/glib-2.0/gio/gio-autocleanups.h
 mdp:/usr/include/glib-2.0/gio/gio-visibility.h
 mdp:/usr/include/glib-2.0/gio/gio.h
 mdp:/usr/include/glib-2.0/gio/gioenums.h
 mdp:/usr/include/glib-2.0/gio/gioenumtypes.h
 mdp:/usr/include/glib-2.0/gio/gioerror.h
 mdp:/usr/include/glib-2.0/gio/giomodule.h
 mdp:/usr/include/glib-2.0/gio/gioscheduler.h
 mdp:/usr/include/glib-2.0/gio/giostream.h
 mdp:/usr/include/glib-2.0/gio/giotypes.h
 mdp:/usr/include/glib-2.0/gio/glistmodel.h
 mdp:/usr/include/glib-2.0/gio/gliststore.h
 mdp:/usr/include/glib-2.0/gio/gloadableicon.h
 mdp:/usr/include/glib-2.0/gio/gmemoryinputstream.h
 mdp:/usr/include/glib-2.0/gio/gmemorymonitor.h
 mdp:/usr/include/glib-2.0/gio/gmemoryoutputstream.h
 mdp:/usr/include/glib-2.0/gio/gmenu.h
 mdp:/usr/include/glib-2.0/gio/gmenuexporter.h
 mdp:/usr/include/glib-2.0/gio/gmenumodel.h
 mdp:/usr/include/glib-2.0/gio/gmount.h
 mdp:/usr/include/glib-2.0/gio/gmountoperation.h
 mdp:/usr/include/glib-2.0/gio/gnativesocketaddress.h
 mdp:/usr/include/glib-2.0/gio/gnativevolumemonitor.h
 mdp:/usr/include/glib-2.0/gio/gnetworkaddress.h
 mdp:/usr/include/glib-2.0/gio/gnetworkmonitor.h
 mdp:/usr/include/glib-2.0/gio/gnetworkservice.h
 mdp:/usr/include/glib-2.0/gio/gnotification.h
 mdp:/usr/include/glib-2.0/gio/goutputstream.h
 mdp:/usr/include/glib-2.0/gio/gpermission.h
 mdp:/usr/include/glib-2.0/gio/gpollableinputstream.h
 mdp:/usr/include/glib-2.0/gio/gpollableoutputstream.h
 mdp:/usr/include/glib-2.0/gio/gpollableutils.h
 mdp:/usr/include/glib-2.0/gio/gpowerprofilemonitor.h
 mdp:/usr/include/glib-2.0/gio/gpropertyaction.h
 mdp:/usr/include/glib-2.0/gio/gproxy.h
 mdp:/usr/include/glib-2.0/gio/gproxyaddress.h
 mdp:/usr/include/glib-2.0/gio/gproxyaddressenumerator.h
 mdp:/usr/include/glib-2.0/gio/gproxyresolver.h
 mdp:/usr/include/glib-2.0/gio/gremoteactiongroup.h
 mdp:/usr/include/glib-2.0/gio/gresolver.h
 mdp:/usr/include/glib-2.0/gio/gresource.h
 mdp:/usr/include/glib-2.0/gio/gseekable.h
 mdp:/usr/include/glib-2.0/gio/gsettings.h
 mdp:/usr/include/glib-2.0/gio/gsettingsschema.h
 mdp:/usr/include/glib-2.0/gio/gsimpleaction.h
 mdp:/usr/include/glib-2.0/gio/gsimpleactiongroup.h
 mdp:/usr/include/glib-2.0/gio/gsimpleasyncresult.h
 mdp:/usr/include/glib-2.0/gio/gsimpleiostream.h
 mdp:/usr/include/glib-2.0/gio/gsimplepermission.h
 mdp:/usr/include/glib-2.0/gio/gsimpleproxyresolver.h
 mdp:/usr/include/glib-2.0/gio/gsocket.h
 mdp:/usr/include/glib-2.0/gio/gsocketaddress.h
 mdp:/usr/include/glib-2.0/gio/gsocketaddressenumerator.h
 mdp:/usr/include/glib-2.0/gio/gsocketclient.h
 mdp:/usr/include/glib-2.0/gio/gsocketconnectable.h
 mdp:/usr/include/glib-2.0/gio/gsocketconnection.h
 mdp:/usr/include/glib-2.0/gio/gsocketcontrolmessage.h
 mdp:/usr/include/glib-2.0/gio/gsocketlistener.h
 mdp:/usr/include/glib-2.0/gio/gsocketservice.h
 mdp:/usr/include/glib-2.0/gio/gsrvtarget.h
 mdp:/usr/include/glib-2.0/gio/gsubprocess.h
 mdp:/usr/include/glib-2.0/gio/gsubprocesslauncher.h
 mdp:/usr/include/glib-2.0/gio/gtask.h
 mdp:/usr/include/glib-2.0/gio/gtcpconnection.h
 mdp:/usr/include/glib-2.0/gio/gtcpwrapperconnection.h
 mdp:/usr/include/glib-2.0/gio/gtestdbus.h
 mdp:/usr/include/glib-2.0/gio/gthemedicon.h
 mdp:/usr/include/glib-2.0/gio/gthreadedsocketservice.h
 mdp:/usr/include/glib-2.0/gio/gtlsbackend.h
 mdp:/usr/include/glib-2.0/gio/gtlscertificate.h
 mdp:/usr/include/glib-2.0/gio/gtlsclientconnection.h
 mdp:/usr/include/glib-2.0/gio/gtlsconnection.h
 mdp:/usr/include/glib-2.0/gio/gtlsdatabase.h
 mdp:/usr/include/glib-2.0/gio/gtlsfiledatabase.h
 mdp:/usr/include/glib-2.0/gio/gtlsinteraction.h
 mdp:/usr/include/glib-2.0/gio/gtlspassword.h
 mdp:/usr/include/glib-2.0/gio/gtlsserverconnection.h
 mdp:/usr/include/glib-2.0/gio/gunixconnection.h
 mdp:/usr/include/glib-2.0/gio/gunixcredentialsmessage.h
 mdp:/usr/include/glib-2.0/gio/gunixfdlist.h
 mdp:/usr/include/glib-2.0/gio/gunixsocketaddress.h
 mdp:/usr/include/glib-2.0/gio/gvfs.h
 mdp:/usr/include/glib-2.0/gio/gvolume.h
 mdp:/usr/include/glib-2.0/gio/gvolumemonitor.h
 mdp:/usr/include/glib-2.0/gio/gzlibcompressor.h
 mdp:/usr/include/glib-2.0/gio/gzlibdecompressor.h
 mdp:/usr/include/glib-2.0/glib-object.h
 mdp:/usr/include/glib-2.0/glib.h
 mdp:/usr/include/glib-2.0/glib/deprecated/gallocator.h
 mdp:/usr/include/glib-2.0/glib/deprecated/gcache.h
 mdp:/usr/include/glib-2.0/glib/deprecated/gcompletion.h
 mdp:/usr/include/glib-2.0/glib/deprecated/gmain.h
 mdp:/usr/include/glib-2.0/glib/deprecated/grel.h
 mdp:/usr/include/glib-2.0/glib/deprecated/gthread.h
 mdp:/usr/include/glib-2.0/glib/galloca.h
 mdp:/usr/include/glib-2.0/glib/garray.h
 mdp:/usr/include/glib-2.0/glib/gasyncqueue.h
 mdp:/usr/include/glib-2.0/glib/gatomic.h
 mdp:/usr/include/glib-2.0/glib/gbacktrace.h
 mdp:/usr/include/glib-2.0/glib/gbase64.h
 mdp:/usr/include/glib-2.0/glib/gbitlock.h
 mdp:/usr/include/glib-2.0/glib/gbookmarkfile.h
 mdp:/usr/include/glib-2.0/glib/gbytes.h
 mdp:/usr/include/glib-2.0/glib/gcharset.h
 mdp:/usr/include/glib-2.0/glib/gchecksum.h
 mdp:/usr/include/glib-2.0/glib/gconvert.h
 mdp:/usr/include/glib-2.0/glib/gdataset.h
 mdp:/usr/include/glib-2.0/glib/gdate.h
 mdp:/usr/include/glib-2.0/glib/gdatetime.h
 mdp:/usr/include/glib-2.0/glib/gdir.h
 mdp:/usr/include/glib-2.0/glib/genviron.h
 mdp:/usr/include/glib-2.0/glib/gerror.h
 mdp:/usr/include/glib-2.0/glib/gfileutils.h
 mdp:/usr/include/glib-2.0/glib/ggettext.h
 mdp:/usr/include/glib-2.0/glib/ghash.h
 mdp:/usr/include/glib-2.0/glib/ghmac.h
 mdp:/usr/include/glib-2.0/glib/ghook.h
 mdp:/usr/include/glib-2.0/glib/ghostutils.h
 mdp:/usr/include/glib-2.0/glib/giochannel.h
 mdp:/usr/include/glib-2.0/glib/gkeyfile.h
 mdp:/usr/include/glib-2.0/glib/glib-autocleanups.h
 mdp:/usr/include/glib-2.0/glib/glib-typeof.h
 mdp:/usr/include/glib-2.0/glib/glib-visibility.h
 mdp:/usr/include/glib-2.0/glib/glist.h
 mdp:/usr/include/glib-2.0/glib/gmacros.h
 mdp:/usr/include/glib-2.0/glib/gmain.h
 mdp:/usr/include/glib-2.0/glib/gmappedfile.h
 mdp:/usr/include/glib-2.0/glib/gmarkup.h
 mdp:/usr/include/glib-2.0/glib/gmem.h
 mdp:/usr/include/glib-2.0/glib/gmessages.h
 mdp:/usr/include/glib-2.0/glib/gnode.h
 mdp:/usr/include/glib-2.0/glib/goption.h
 mdp:/usr/include/glib-2.0/glib/gpathbuf.h
 mdp:/usr/include/glib-2.0/glib/gpattern.h
 mdp:/usr/include/glib-2.0/glib/gpoll.h
 mdp:/usr/include/glib-2.0/glib/gprimes.h
 mdp:/usr/include/glib-2.0/glib/gqsort.h
 mdp:/usr/include/glib-2.0/glib/gquark.h
 mdp:/usr/include/glib-2.0/glib/gqueue.h
 mdp:/usr/include/glib-2.0/glib/grand.h
 mdp:/usr/include/glib-2.0/glib/grcbox.h
 mdp:/usr/include/glib-2.0/glib/grefcount.h
 mdp:/usr/include/glib-2.0/glib/grefstring.h
 mdp:/usr/include/glib-2.0/glib/gregex.h
 mdp:/usr/include/glib-2.0/glib/gscanner.h
 mdp:/usr/include/glib-2.0/glib/gsequence.h
 mdp:/usr/include/glib-2.0/glib/gshell.h
 mdp:/usr/include/glib-2.0/glib/gslice.h
 mdp:/usr/include/glib-2.0/glib/gslist.h
 mdp:/usr/include/glib-2.0/glib/gspawn.h
 mdp:/usr/include/glib-2.0/glib/gstrfuncs.h
 mdp:/usr/include/glib-2.0/glib/gstring.h
 mdp:/usr/include/glib-2.0/glib/gstringchunk.h
 mdp:/usr/include/glib-2.0/glib/gstrvbuilder.h
 mdp:/usr/include/glib-2.0/glib/gtestutils.h
 mdp:/usr/include/glib-2.0/glib/gthread.h
 mdp:/usr/include/glib-2.0/glib/gthreadpool.h
 mdp:/usr/include/glib-2.0/glib/gtimer.h
 mdp:/usr/include/glib-2.0/glib/gtimezone.h
 mdp:/usr/include/glib-2.0/glib/gtrashstack.h
 mdp:/usr/include/glib-2.0/glib/gtree.h
 mdp:/usr/include/glib-2.0/glib/gtypes.h
 mdp:/usr/include/glib-2.0/glib/gunicode.h
 mdp:/usr/include/glib-2.0/glib/guri.h
 mdp:/usr/include/glib-2.0/glib/gutils.h
 mdp:/usr/include/glib-2.0/glib/guuid.h
 mdp:/usr/include/glib-2.0/glib/gvariant.h
 mdp:/usr/include/glib-2.0/glib/gvarianttype.h
 mdp:/usr/include/glib-2.0/glib/gversion.h
 mdp:/usr/include/glib-2.0/glib/gversionmacros.h
 mdp:/usr/include/glib-2.0/gmodule.h
 mdp:/usr/include/glib-2.0/gmodule/gmodule-visibility.h
 mdp:/usr/include/glib-2.0/gobject/gbinding.h
 mdp:/usr/include/glib-2.0/gobject/gbindinggroup.h
 mdp:/usr/include/glib-2.0/gobject/gboxed.h
 mdp:/usr/include/glib-2.0/gobject/gclosure.h
 mdp:/usr/include/glib-2.0/gobject/genums.h
 mdp:/usr/include/glib-2.0/gobject/glib-enumtypes.h
 mdp:/usr/include/glib-2.0/gobject/glib-types.h
 mdp:/usr/include/glib-2.0/gobject/gmarshal.h
 mdp:/usr/include/glib-2.0/gobject/gobject-autocleanups.h
 mdp:/usr/include/glib-2.0/gobject/gobject-visibility.h
 mdp:/usr/include/glib-2.0/gobject/gobject.h
 mdp:/usr/include/glib-2.0/gobject/gparam.h
 mdp:/usr/include/glib-2.0/gobject/gparamspecs.h
 mdp:/usr/include/glib-2.0/gobject/gsignal.h
 mdp:/usr/include/glib-2.0/gobject/gsignalgroup.h
 mdp:/usr/include/glib-2.0/gobject/gsourceclosure.h
 mdp:/usr/include/glib-2.0/gobject/gtype.h
 mdp:/usr/include/glib-2.0/gobject/gtypemodule.h
 mdp:/usr/include/glib-2.0/gobject/gtypeplugin.h
 mdp:/usr/include/glib-2.0/gobject/gvalue.h
 mdp:/usr/include/glib-2.0/gobject/gvaluearray.h
 mdp:/usr/include/glib-2.0/gobject/gvaluetypes.h
 mdp:/usr/include/limits.h
 mdp:/usr/include/linux/errno.h
 mdp:/usr/include/linux/limits.h
 mdp:/usr/include/locale.h
 mdp:/usr/include/pthread.h
 mdp:/usr/include/sched.h
 mdp:/usr/include/signal.h
 mdp:/usr/include/stdc-predef.h
 mdp:/usr/include/stdint.h
 mdp:/usr/include/stdio.h
 mdp:/usr/include/stdlib.h
 mdp:/usr/include/string.h
 mdp:/usr/include/strings.h
 mdp:/usr/include/time.h
 mdp:/usr/include/unistd.h
 mdp:/usr/include/wchar.h
 mdp:/usr/include/x86_64-linux-gnu/asm/errno.h
 mdp:/usr/include/x86_64-linux-gnu/bits/atomic_wide_counter.h
 mdp:/usr/include/x86_64-linux-gnu/bits/byteswap.h
 mdp:/usr/include/x86_64-linux-gnu/bits/confname.h
 mdp:/usr/include/x86_64-linux-gnu/bits/cpu-set.h
 mdp:/usr/include/x86_64-linux-gnu/bits/dirent.h
 mdp:/usr/include/x86_64-linux-gnu/bits/dirent_ext.h
 mdp:/usr/include/x86_64-linux-gnu/bits/endian.h
 mdp:/usr/include/x86_64-linux-gnu/bits/endianness.h
 mdp:/usr/include/x86_64-linux-gnu/bits/environments.h
 mdp:/usr/include/x86_64-linux-gnu/bits/errno.h
 mdp:/usr/include/x86_64-linux-gnu/bits/floatn-common.h
 mdp:/usr/include/x86_64-linux-gnu/bits/floatn.h
 mdp:/usr/include/x86_64-linux-gnu/bits/getopt_core.h
 mdp:/usr/include/x86_64-linux-gnu/bits/getopt_posix.h
 mdp:/usr/include/x86_64-linux-gnu/bits/libc-header-start.h
 mdp:/usr/include/x86_64-linux-gnu/bits/local_lim.h
 mdp:/usr/include/x86_64-linux-gnu/bits/locale.h
 mdp:/usr/include/x86_64-linux-gnu/bits/long-double.h
 mdp:/usr/include/x86_64-linux-gnu/bits/posix1_lim.h
 mdp:/usr/include/x86_64-linux-gnu/bits/posix2_lim.h
 mdp:/usr/include/x86_64-linux-gnu/bits/posix_opt.h
 mdp:/usr/include/x86_64-linux-gnu/bits/pthread_stack_min-dynamic.h
 mdp:/usr/include/x86_64-linux-gnu/bits/pthreadtypes-arch.h
 mdp:/usr/include/x86_64-linux-gnu/bits/pthreadtypes.h
 mdp:/usr/include/x86_64-linux-gnu/bits/sched.h
 mdp:/usr/include/x86_64-linux-gnu/bits/select.h
 mdp:/usr/include/x86_64-linux-gnu/bits/setjmp.h
 mdp:/usr/include/x86_64-linux-gnu/bits/sigaction.h
 mdp:/usr/include/x86_64-linux-gnu/bits/sigcontext.h
 mdp:/usr/include/x86_64-linux-gnu/bits/sigevent-consts.h
 mdp:/usr/include/x86_64-linux-gnu/bits/siginfo-arch.h
 mdp:/usr/include/x86_64-linux-gnu/bits/siginfo-consts-arch.h
 mdp:/usr/include/x86_64-linux-gnu/bits/siginfo-consts.h
 mdp:/usr/include/x86_64-linux-gnu/bits/signal_ext.h
 mdp:/usr/include/x86_64-linux-gnu/bits/signum-arch.h
 mdp:/usr/include/x86_64-linux-gnu/bits/signum-generic.h
 mdp:/usr/include/x86_64-linux-gnu/bits/sigstack.h
 mdp:/usr/include/x86_64-linux-gnu/bits/sigstksz.h
 mdp:/usr/include/x86_64-linux-gnu/bits/sigthread.h
 mdp:/usr/include/x86_64-linux-gnu/bits/ss_flags.h
 mdp:/usr/include/x86_64-linux-gnu/bits/stdint-intn.h
 mdp:/usr/include/x86_64-linux-gnu/bits/stdint-uintn.h
 mdp:/usr/include/x86_64-linux-gnu/bits/stdio_lim.h
 mdp:/usr/include/x86_64-linux-gnu/bits/stdlib-float.h
 mdp:/usr/include/x86_64-linux-gnu/bits/struct_mutex.h
 mdp:/usr/include/x86_64-linux-gnu/bits/struct_rwlock.h
 mdp:/usr/include/x86_64-linux-gnu/bits/thread-shared-types.h
 mdp:/usr/include/x86_64-linux-gnu/bits/time.h
 mdp:/usr/include/x86_64-linux-gnu/bits/time64.h
 mdp:/usr/include/x86_64-linux-gnu/bits/timesize.h
 mdp:/usr/include/x86_64-linux-gnu/bits/timex.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/FILE.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/__FILE.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/__fpos64_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/__fpos_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/__locale_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/__mbstate_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/__sigset_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/__sigval_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/clock_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/clockid_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/cookie_io_functions_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/error_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/locale_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/mbstate_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/sig_atomic_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/sigevent_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/siginfo_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/sigset_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/sigval_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/stack_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/struct_FILE.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/struct___jmp_buf_tag.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/struct_itimerspec.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/struct_sched_param.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/struct_sigstack.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/struct_timespec.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/struct_timeval.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/struct_tm.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/time_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/timer_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/wint_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/typesizes.h
 mdp:/usr/include/x86_64-linux-gnu/bits/uintn-identity.h
 mdp:/usr/include/x86_64-linux-gnu/bits/uio_lim.h
 mdp:/usr/include/x86_64-linux-gnu/bits/unistd_ext.h
 mdp:/usr/include/x86_64-linux-gnu/bits/waitflags.h
 mdp:/usr/include/x86_64-linux-gnu/bits/waitstatus.h
 mdp:/usr/include/x86_64-linux-gnu/bits/wchar.h
 mdp:/usr/include/x86_64-linux-gnu/bits/wordsize.h
 mdp:/usr/include/x86_64-linux-gnu/bits/xopen_lim.h
 mdp:/usr/include/x86_64-linux-gnu/c++/12/bits/atomic_word.h
 mdp:/usr/include/x86_64-linux-gnu/c++/12/bits/c++allocator.h
 mdp:/usr/include/x86_64-linux-gnu/c++/12/bits/c++config.h
 mdp:/usr/include/x86_64-linux-gnu/c++/12/bits/c++locale.h
 mdp:/usr/include/x86_64-linux-gnu/c++/12/bits/cpu_defines.h
 mdp:/usr/include/x86_64-linux-gnu/c++/12/bits/error_constants.h
 mdp:/usr/include/x86_64-linux-gnu/c++/12/bits/gthr-default.h
 mdp:/usr/include/x86_64-linux-gnu/c++/12/bits/gthr.h
 mdp:/usr/include/x86_64-linux-gnu/c++/12/bits/os_defines.h
 mdp:/usr/include/x86_64-linux-gnu/gnu/stubs-64.h
 mdp:/usr/include/x86_64-linux-gnu/gnu/stubs.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/QObject
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/q20functional.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/q20memory.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/q20type_traits.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qalgorithms.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qanystringview.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qarraydata.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qarraydataops.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qarraydatapointer.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qassert.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qatomic.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qatomic_cxx11.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qbasicatomic.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qbindingstorage.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qbytearray.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qbytearrayalgorithms.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qbytearraylist.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qbytearrayview.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qchar.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qcompare.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qcompare_impl.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qcomparehelpers.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qcompilerdetection.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qconfig.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qconstructormacros.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qcontainerfwd.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qcontainerinfo.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qcontainertools_impl.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qdarwinhelpers.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qdatastream.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qexceptionhandling.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qflags.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qfloat16.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qforeach.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qfunctionaltools_impl.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qfunctionpointer.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qgenericatomic.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qglobal.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qglobalstatic.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qhashfunctions.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qiodevicebase.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qiterable.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qiterator.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qlatin1stringview.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qlist.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qlogging.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qmalloc.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qmath.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qmetacontainer.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qmetatype.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qminmax.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qnamespace.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qnumeric.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qobject.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qobject_impl.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qobjectdefs.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qobjectdefs_impl.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qoverload.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qpair.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qprocessordetection.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qrefcount.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qscopedpointer.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qscopeguard.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qstring.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qstringalgorithms.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qstringbuilder.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qstringconverter.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qstringconverter_base.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qstringfwd.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qstringlist.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qstringliteral.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qstringmatcher.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qstringtokenizer.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qstringview.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qswap.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qsysinfo.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qsystemdetection.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtaggedpointer.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtclasshelpermacros.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtconfiginclude.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtconfigmacros.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtcore-config.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtcoreexports.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtdeprecationdefinitions.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtdeprecationmarkers.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtenvironmentvariables.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtmetamacros.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtnoop.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtpreprocessorsupport.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtresource.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qttranslation.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qttypetraits.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtversion.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtversionchecks.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtypeinfo.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtypes.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qutf8stringview.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qversiontagging.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qxptype_traits.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qyieldcpu.h
 mdp:/usr/include/x86_64-linux-gnu/sys/cdefs.h
 mdp:/usr/include/x86_64-linux-gnu/sys/select.h
 mdp:/usr/include/x86_64-linux-gnu/sys/types.h
 mdp:/usr/include/x86_64-linux-gnu/sys/ucontext.h
 mdp:/usr/lib/gcc/x86_64-linux-gnu/12/include/float.h
 mdp:/usr/lib/gcc/x86_64-linux-gnu/12/include/stdarg.h
 mdp:/usr/lib/gcc/x86_64-linux-gnu/12/include/stdbool.h
 mdp:/usr/lib/gcc/x86_64-linux-gnu/12/include/stddef.h
 mdp:/usr/lib/x86_64-linux-gnu/glib-2.0/include/glibconfig.h
/home/<USER>/Desktop/myrepo/dde-launchpad/src/gioutils/appinfo.h
