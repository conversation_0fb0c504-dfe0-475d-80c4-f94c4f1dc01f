{"BUILD_DIR": "/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/gioutils/gio-utils_autogen", "CMAKE_BINARY_DIR": "/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu", "CMAKE_CURRENT_BINARY_DIR": "/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/gioutils", "CMAKE_CURRENT_SOURCE_DIR": "/home/<USER>/Desktop/myrepo/dde-launchpad/src/gioutils", "CMAKE_EXECUTABLE": "/usr/bin/cmake", "CMAKE_LIST_FILES": ["/home/<USER>/Desktop/myrepo/dde-launchpad/src/gioutils/CMakeLists.txt", "/usr/share/cmake-3.31/Modules/FindPkgConfig.cmake", "/usr/share/cmake-3.31/Modules/FindPackageHandleStandardArgs.cmake", "/usr/share/cmake-3.31/Modules/FindPackageMessage.cmake"], "CMAKE_SOURCE_DIR": "/home/<USER>/Desktop/myrepo/dde-launchpad", "CROSS_CONFIG": false, "DEP_FILE": "/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/gioutils/gio-utils_autogen/deps", "DEP_FILE_RULE_NAME": "gio-utils_autogen/timestamp", "HEADERS": [["/home/<USER>/Desktop/myrepo/dde-launchpad/src/gioutils/appinfo.h", "Mu", "EWIEGA46WW/moc_appinfo.cpp", null], ["/home/<USER>/Desktop/myrepo/dde-launchpad/src/gioutils/appinfomonitor.h", "Mu", "EWIEGA46WW/moc_appinfomonitor.cpp", null], ["/home/<USER>/Desktop/myrepo/dde-launchpad/src/gioutils/trashmonitor.h", "Mu", "EWIEGA46WW/moc_trashmonitor.cpp", null]], "HEADER_EXTENSIONS": ["h", "hh", "h++", "hm", "hpp", "hxx", "in", "txx"], "INCLUDE_DIR": "/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/gioutils/gio-utils_autogen/include", "MOC_COMPILATION_FILE": "/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/gioutils/gio-utils_autogen/mocs_compilation.cpp", "MOC_DEFINITIONS": ["DSG_DATA_DIR=\"/usr/share/dsg\"", "QT_CORE_LIB", "QT_MESSAGELOGCONTEXT", "QT_NO_DEBUG", "QT_NO_KEYWORDS"], "MOC_DEPEND_FILTERS": [["Q_PLUGIN_METADATA", "[\n][ \t]*Q_PLUGIN_METADATA[ \t]*\\([^\\)]*FILE[ \t]*\"([^\"]+)\""]], "MOC_INCLUDES": ["/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/gioutils", "/home/<USER>/Desktop/myrepo/dde-launchpad/src/gioutils", "/usr/include/x86_64-linux-gnu/qt6/QtCore", "/usr/include/x86_64-linux-gnu/qt6", "/usr/lib/x86_64-linux-gnu/qt6/mkspecs/linux-g++", "/usr/include/glib-2.0", "/usr/lib/x86_64-linux-gnu/glib-2.0/include", "/usr/include/sysprof-6", "/usr/include/libmount", "/usr/include/blkid", "/usr/include/gio-unix-2.0", "/usr/include", "/usr/include/c++/12", "/usr/include/x86_64-linux-gnu/c++/12", "/usr/include/c++/12/backward", "/usr/lib/gcc/x86_64-linux-gnu/12/include", "/usr/include/x86_64-linux-gnu"], "MOC_MACRO_NAMES": ["Q_OBJECT", "Q_GADGET", "Q_NAMESPACE", "Q_NAMESPACE_EXPORT", "Q_GADGET_EXPORT", "Q_ENUM_NS"], "MOC_OPTIONS": [], "MOC_PATH_PREFIX": false, "MOC_PREDEFS_CMD": ["/usr/bin/c++", "-std=gnu++17", "-dM", "-E", "-c", "/usr/share/cmake-3.31/Modules/CMakeCXXCompilerABI.cpp"], "MOC_PREDEFS_FILE": "/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/gioutils/gio-utils_autogen/moc_predefs.h", "MOC_RELAXED_MODE": false, "MOC_SKIP": [], "MULTI_CONFIG": false, "PARALLEL": 8, "PARSE_CACHE_FILE": "/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/gioutils/CMakeFiles/gio-utils_autogen.dir/ParseCache.txt", "QT_MOC_EXECUTABLE": "/usr/lib/qt6/libexec/moc", "QT_UIC_EXECUTABLE": "", "QT_VERSION_MAJOR": 6, "QT_VERSION_MINOR": 8, "SETTINGS_FILE": "/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/gioutils/CMakeFiles/gio-utils_autogen.dir/AutogenUsed.txt", "SOURCES": [["/home/<USER>/Desktop/myrepo/dde-launchpad/src/gioutils/appinfo.cpp", "Mu", null], ["/home/<USER>/Desktop/myrepo/dde-launchpad/src/gioutils/appinfomonitor.cpp", "Mu", null], ["/home/<USER>/Desktop/myrepo/dde-launchpad/src/gioutils/trashmonitor.cpp", "Mu", null]], "USE_BETTER_GRAPH": true, "VERBOSITY": 0}