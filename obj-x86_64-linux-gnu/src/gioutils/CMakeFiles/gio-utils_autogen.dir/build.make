# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.31

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Produce verbose output by default.
VERBOSE = 1

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/Desktop/myrepo/dde-launchpad

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu

# Utility rule file for gio-utils_autogen.

# Include any custom commands dependencies for this target.
include src/gioutils/CMakeFiles/gio-utils_autogen.dir/compiler_depend.make

# Include the progress variables for this target.
include src/gioutils/CMakeFiles/gio-utils_autogen.dir/progress.make

src/gioutils/CMakeFiles/gio-utils_autogen: src/gioutils/gio-utils_autogen/timestamp

src/gioutils/gio-utils_autogen/timestamp: /usr/lib/qt6/libexec/moc
src/gioutils/gio-utils_autogen/timestamp: src/gioutils/CMakeFiles/gio-utils_autogen.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Automatic MOC for target gio-utils"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/gioutils && /usr/bin/cmake -E cmake_autogen /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/gioutils/CMakeFiles/gio-utils_autogen.dir/AutogenInfo.json None
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/gioutils && /usr/bin/cmake -E touch /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/gioutils/gio-utils_autogen/timestamp

src/gioutils/CMakeFiles/gio-utils_autogen.dir/codegen:
.PHONY : src/gioutils/CMakeFiles/gio-utils_autogen.dir/codegen

gio-utils_autogen: src/gioutils/CMakeFiles/gio-utils_autogen
gio-utils_autogen: src/gioutils/gio-utils_autogen/timestamp
gio-utils_autogen: src/gioutils/CMakeFiles/gio-utils_autogen.dir/build.make
.PHONY : gio-utils_autogen

# Rule to build all files generated by this target.
src/gioutils/CMakeFiles/gio-utils_autogen.dir/build: gio-utils_autogen
.PHONY : src/gioutils/CMakeFiles/gio-utils_autogen.dir/build

src/gioutils/CMakeFiles/gio-utils_autogen.dir/clean:
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/gioutils && $(CMAKE_COMMAND) -P CMakeFiles/gio-utils_autogen.dir/cmake_clean.cmake
.PHONY : src/gioutils/CMakeFiles/gio-utils_autogen.dir/clean

src/gioutils/CMakeFiles/gio-utils_autogen.dir/depend:
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/Desktop/myrepo/dde-launchpad /home/<USER>/Desktop/myrepo/dde-launchpad/src/gioutils /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/gioutils /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/gioutils/CMakeFiles/gio-utils_autogen.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : src/gioutils/CMakeFiles/gio-utils_autogen.dir/depend

