# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.31

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Produce verbose output by default.
VERBOSE = 1

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/Desktop/myrepo/dde-launchpad

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "No interactive CMake dialog available..."
	/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Available install components are: \"Unspecified\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components
.PHONY : list_install_components/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# The main all target
all: cmake_check_build_system
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu && $(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/CMakeFiles /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/gioutils//CMakeFiles/progress.marks
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 src/gioutils/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 src/gioutils/clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: cmake_check_build_system
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 src/gioutils/preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 src/gioutils/preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

# Convenience name for target.
src/gioutils/CMakeFiles/gio-utils.dir/rule:
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 src/gioutils/CMakeFiles/gio-utils.dir/rule
.PHONY : src/gioutils/CMakeFiles/gio-utils.dir/rule

# Convenience name for target.
gio-utils: src/gioutils/CMakeFiles/gio-utils.dir/rule
.PHONY : gio-utils

# fast build rule for target.
gio-utils/fast:
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu && $(MAKE) $(MAKESILENT) -f src/gioutils/CMakeFiles/gio-utils.dir/build.make src/gioutils/CMakeFiles/gio-utils.dir/build
.PHONY : gio-utils/fast

# Convenience name for target.
src/gioutils/CMakeFiles/gio-utils_autogen_timestamp_deps.dir/rule:
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 src/gioutils/CMakeFiles/gio-utils_autogen_timestamp_deps.dir/rule
.PHONY : src/gioutils/CMakeFiles/gio-utils_autogen_timestamp_deps.dir/rule

# Convenience name for target.
gio-utils_autogen_timestamp_deps: src/gioutils/CMakeFiles/gio-utils_autogen_timestamp_deps.dir/rule
.PHONY : gio-utils_autogen_timestamp_deps

# fast build rule for target.
gio-utils_autogen_timestamp_deps/fast:
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu && $(MAKE) $(MAKESILENT) -f src/gioutils/CMakeFiles/gio-utils_autogen_timestamp_deps.dir/build.make src/gioutils/CMakeFiles/gio-utils_autogen_timestamp_deps.dir/build
.PHONY : gio-utils_autogen_timestamp_deps/fast

# Convenience name for target.
src/gioutils/CMakeFiles/gio-utils_autogen.dir/rule:
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 src/gioutils/CMakeFiles/gio-utils_autogen.dir/rule
.PHONY : src/gioutils/CMakeFiles/gio-utils_autogen.dir/rule

# Convenience name for target.
gio-utils_autogen: src/gioutils/CMakeFiles/gio-utils_autogen.dir/rule
.PHONY : gio-utils_autogen

# fast build rule for target.
gio-utils_autogen/fast:
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu && $(MAKE) $(MAKESILENT) -f src/gioutils/CMakeFiles/gio-utils_autogen.dir/build.make src/gioutils/CMakeFiles/gio-utils_autogen.dir/build
.PHONY : gio-utils_autogen/fast

appinfo.o: appinfo.cpp.o
.PHONY : appinfo.o

# target to build an object file
appinfo.cpp.o:
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu && $(MAKE) $(MAKESILENT) -f src/gioutils/CMakeFiles/gio-utils.dir/build.make src/gioutils/CMakeFiles/gio-utils.dir/appinfo.cpp.o
.PHONY : appinfo.cpp.o

appinfo.i: appinfo.cpp.i
.PHONY : appinfo.i

# target to preprocess a source file
appinfo.cpp.i:
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu && $(MAKE) $(MAKESILENT) -f src/gioutils/CMakeFiles/gio-utils.dir/build.make src/gioutils/CMakeFiles/gio-utils.dir/appinfo.cpp.i
.PHONY : appinfo.cpp.i

appinfo.s: appinfo.cpp.s
.PHONY : appinfo.s

# target to generate assembly for a file
appinfo.cpp.s:
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu && $(MAKE) $(MAKESILENT) -f src/gioutils/CMakeFiles/gio-utils.dir/build.make src/gioutils/CMakeFiles/gio-utils.dir/appinfo.cpp.s
.PHONY : appinfo.cpp.s

appinfomonitor.o: appinfomonitor.cpp.o
.PHONY : appinfomonitor.o

# target to build an object file
appinfomonitor.cpp.o:
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu && $(MAKE) $(MAKESILENT) -f src/gioutils/CMakeFiles/gio-utils.dir/build.make src/gioutils/CMakeFiles/gio-utils.dir/appinfomonitor.cpp.o
.PHONY : appinfomonitor.cpp.o

appinfomonitor.i: appinfomonitor.cpp.i
.PHONY : appinfomonitor.i

# target to preprocess a source file
appinfomonitor.cpp.i:
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu && $(MAKE) $(MAKESILENT) -f src/gioutils/CMakeFiles/gio-utils.dir/build.make src/gioutils/CMakeFiles/gio-utils.dir/appinfomonitor.cpp.i
.PHONY : appinfomonitor.cpp.i

appinfomonitor.s: appinfomonitor.cpp.s
.PHONY : appinfomonitor.s

# target to generate assembly for a file
appinfomonitor.cpp.s:
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu && $(MAKE) $(MAKESILENT) -f src/gioutils/CMakeFiles/gio-utils.dir/build.make src/gioutils/CMakeFiles/gio-utils.dir/appinfomonitor.cpp.s
.PHONY : appinfomonitor.cpp.s

gio-utils_autogen/mocs_compilation.o: gio-utils_autogen/mocs_compilation.cpp.o
.PHONY : gio-utils_autogen/mocs_compilation.o

# target to build an object file
gio-utils_autogen/mocs_compilation.cpp.o:
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu && $(MAKE) $(MAKESILENT) -f src/gioutils/CMakeFiles/gio-utils.dir/build.make src/gioutils/CMakeFiles/gio-utils.dir/gio-utils_autogen/mocs_compilation.cpp.o
.PHONY : gio-utils_autogen/mocs_compilation.cpp.o

gio-utils_autogen/mocs_compilation.i: gio-utils_autogen/mocs_compilation.cpp.i
.PHONY : gio-utils_autogen/mocs_compilation.i

# target to preprocess a source file
gio-utils_autogen/mocs_compilation.cpp.i:
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu && $(MAKE) $(MAKESILENT) -f src/gioutils/CMakeFiles/gio-utils.dir/build.make src/gioutils/CMakeFiles/gio-utils.dir/gio-utils_autogen/mocs_compilation.cpp.i
.PHONY : gio-utils_autogen/mocs_compilation.cpp.i

gio-utils_autogen/mocs_compilation.s: gio-utils_autogen/mocs_compilation.cpp.s
.PHONY : gio-utils_autogen/mocs_compilation.s

# target to generate assembly for a file
gio-utils_autogen/mocs_compilation.cpp.s:
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu && $(MAKE) $(MAKESILENT) -f src/gioutils/CMakeFiles/gio-utils.dir/build.make src/gioutils/CMakeFiles/gio-utils.dir/gio-utils_autogen/mocs_compilation.cpp.s
.PHONY : gio-utils_autogen/mocs_compilation.cpp.s

trashmonitor.o: trashmonitor.cpp.o
.PHONY : trashmonitor.o

# target to build an object file
trashmonitor.cpp.o:
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu && $(MAKE) $(MAKESILENT) -f src/gioutils/CMakeFiles/gio-utils.dir/build.make src/gioutils/CMakeFiles/gio-utils.dir/trashmonitor.cpp.o
.PHONY : trashmonitor.cpp.o

trashmonitor.i: trashmonitor.cpp.i
.PHONY : trashmonitor.i

# target to preprocess a source file
trashmonitor.cpp.i:
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu && $(MAKE) $(MAKESILENT) -f src/gioutils/CMakeFiles/gio-utils.dir/build.make src/gioutils/CMakeFiles/gio-utils.dir/trashmonitor.cpp.i
.PHONY : trashmonitor.cpp.i

trashmonitor.s: trashmonitor.cpp.s
.PHONY : trashmonitor.s

# target to generate assembly for a file
trashmonitor.cpp.s:
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu && $(MAKE) $(MAKESILENT) -f src/gioutils/CMakeFiles/gio-utils.dir/build.make src/gioutils/CMakeFiles/gio-utils.dir/trashmonitor.cpp.s
.PHONY : trashmonitor.cpp.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... install"
	@echo "... install/local"
	@echo "... install/strip"
	@echo "... list_install_components"
	@echo "... rebuild_cache"
	@echo "... gio-utils_autogen"
	@echo "... gio-utils_autogen_timestamp_deps"
	@echo "... gio-utils"
	@echo "... appinfo.o"
	@echo "... appinfo.i"
	@echo "... appinfo.s"
	@echo "... appinfomonitor.o"
	@echo "... appinfomonitor.i"
	@echo "... appinfomonitor.s"
	@echo "... gio-utils_autogen/mocs_compilation.o"
	@echo "... gio-utils_autogen/mocs_compilation.i"
	@echo "... gio-utils_autogen/mocs_compilation.s"
	@echo "... trashmonitor.o"
	@echo "... trashmonitor.i"
	@echo "... trashmonitor.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

