/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/gioutils/gio-utils_autogen/EWIEGA46WW/moc_appinfomonitor.cpp: /home/<USER>/Desktop/myrepo/dde-launchpad/src/gioutils/appinfomonitor.h \
  /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/gioutils/gio-utils_autogen/moc_predefs.h \
  /usr/include/alloca.h \
  /usr/include/asm-generic/errno-base.h \
  /usr/include/asm-generic/errno.h \
  /usr/include/assert.h \
  /usr/include/c++/12/algorithm \
  /usr/include/c++/12/array \
  /usr/include/c++/12/atomic \
  /usr/include/c++/12/backward/auto_ptr.h \
  /usr/include/c++/12/backward/binders.h \
  /usr/include/c++/12/bit \
  /usr/include/c++/12/bits/algorithmfwd.h \
  /usr/include/c++/12/bits/align.h \
  /usr/include/c++/12/bits/alloc_traits.h \
  /usr/include/c++/12/bits/allocated_ptr.h \
  /usr/include/c++/12/bits/allocator.h \
  /usr/include/c++/12/bits/atomic_base.h \
  /usr/include/c++/12/bits/atomic_lockfree_defines.h \
  /usr/include/c++/12/bits/basic_string.h \
  /usr/include/c++/12/bits/basic_string.tcc \
  /usr/include/c++/12/bits/char_traits.h \
  /usr/include/c++/12/bits/charconv.h \
  /usr/include/c++/12/bits/chrono.h \
  /usr/include/c++/12/bits/concept_check.h \
  /usr/include/c++/12/bits/cpp_type_traits.h \
  /usr/include/c++/12/bits/cxxabi_forced.h \
  /usr/include/c++/12/bits/cxxabi_init_exception.h \
  /usr/include/c++/12/bits/enable_special_members.h \
  /usr/include/c++/12/bits/erase_if.h \
  /usr/include/c++/12/bits/exception.h \
  /usr/include/c++/12/bits/exception_defines.h \
  /usr/include/c++/12/bits/exception_ptr.h \
  /usr/include/c++/12/bits/functexcept.h \
  /usr/include/c++/12/bits/functional_hash.h \
  /usr/include/c++/12/bits/hash_bytes.h \
  /usr/include/c++/12/bits/hashtable.h \
  /usr/include/c++/12/bits/hashtable_policy.h \
  /usr/include/c++/12/bits/invoke.h \
  /usr/include/c++/12/bits/ios_base.h \
  /usr/include/c++/12/bits/list.tcc \
  /usr/include/c++/12/bits/locale_classes.h \
  /usr/include/c++/12/bits/locale_classes.tcc \
  /usr/include/c++/12/bits/localefwd.h \
  /usr/include/c++/12/bits/memoryfwd.h \
  /usr/include/c++/12/bits/move.h \
  /usr/include/c++/12/bits/nested_exception.h \
  /usr/include/c++/12/bits/new_allocator.h \
  /usr/include/c++/12/bits/node_handle.h \
  /usr/include/c++/12/bits/ostream_insert.h \
  /usr/include/c++/12/bits/parse_numbers.h \
  /usr/include/c++/12/bits/postypes.h \
  /usr/include/c++/12/bits/predefined_ops.h \
  /usr/include/c++/12/bits/ptr_traits.h \
  /usr/include/c++/12/bits/range_access.h \
  /usr/include/c++/12/bits/refwrap.h \
  /usr/include/c++/12/bits/shared_ptr.h \
  /usr/include/c++/12/bits/shared_ptr_atomic.h \
  /usr/include/c++/12/bits/shared_ptr_base.h \
  /usr/include/c++/12/bits/specfun.h \
  /usr/include/c++/12/bits/std_abs.h \
  /usr/include/c++/12/bits/std_function.h \
  /usr/include/c++/12/bits/stl_algo.h \
  /usr/include/c++/12/bits/stl_algobase.h \
  /usr/include/c++/12/bits/stl_bvector.h \
  /usr/include/c++/12/bits/stl_construct.h \
  /usr/include/c++/12/bits/stl_function.h \
  /usr/include/c++/12/bits/stl_heap.h \
  /usr/include/c++/12/bits/stl_iterator.h \
  /usr/include/c++/12/bits/stl_iterator_base_funcs.h \
  /usr/include/c++/12/bits/stl_iterator_base_types.h \
  /usr/include/c++/12/bits/stl_list.h \
  /usr/include/c++/12/bits/stl_map.h \
  /usr/include/c++/12/bits/stl_multimap.h \
  /usr/include/c++/12/bits/stl_numeric.h \
  /usr/include/c++/12/bits/stl_pair.h \
  /usr/include/c++/12/bits/stl_raw_storage_iter.h \
  /usr/include/c++/12/bits/stl_relops.h \
  /usr/include/c++/12/bits/stl_tempbuf.h \
  /usr/include/c++/12/bits/stl_tree.h \
  /usr/include/c++/12/bits/stl_uninitialized.h \
  /usr/include/c++/12/bits/stl_vector.h \
  /usr/include/c++/12/bits/stream_iterator.h \
  /usr/include/c++/12/bits/streambuf.tcc \
  /usr/include/c++/12/bits/streambuf_iterator.h \
  /usr/include/c++/12/bits/string_view.tcc \
  /usr/include/c++/12/bits/stringfwd.h \
  /usr/include/c++/12/bits/uniform_int_dist.h \
  /usr/include/c++/12/bits/unique_ptr.h \
  /usr/include/c++/12/bits/unordered_map.h \
  /usr/include/c++/12/bits/uses_allocator.h \
  /usr/include/c++/12/bits/utility.h \
  /usr/include/c++/12/bits/vector.tcc \
  /usr/include/c++/12/cctype \
  /usr/include/c++/12/cerrno \
  /usr/include/c++/12/chrono \
  /usr/include/c++/12/clocale \
  /usr/include/c++/12/cmath \
  /usr/include/c++/12/compare \
  /usr/include/c++/12/cstddef \
  /usr/include/c++/12/cstdint \
  /usr/include/c++/12/cstdio \
  /usr/include/c++/12/cstdlib \
  /usr/include/c++/12/cstring \
  /usr/include/c++/12/ctime \
  /usr/include/c++/12/cwchar \
  /usr/include/c++/12/debug/assertions.h \
  /usr/include/c++/12/debug/debug.h \
  /usr/include/c++/12/exception \
  /usr/include/c++/12/ext/aligned_buffer.h \
  /usr/include/c++/12/ext/alloc_traits.h \
  /usr/include/c++/12/ext/atomicity.h \
  /usr/include/c++/12/ext/concurrence.h \
  /usr/include/c++/12/ext/numeric_traits.h \
  /usr/include/c++/12/ext/string_conversions.h \
  /usr/include/c++/12/ext/type_traits.h \
  /usr/include/c++/12/functional \
  /usr/include/c++/12/initializer_list \
  /usr/include/c++/12/iosfwd \
  /usr/include/c++/12/iterator \
  /usr/include/c++/12/limits \
  /usr/include/c++/12/list \
  /usr/include/c++/12/map \
  /usr/include/c++/12/memory \
  /usr/include/c++/12/new \
  /usr/include/c++/12/numeric \
  /usr/include/c++/12/optional \
  /usr/include/c++/12/pstl/execution_defs.h \
  /usr/include/c++/12/pstl/glue_algorithm_defs.h \
  /usr/include/c++/12/pstl/glue_memory_defs.h \
  /usr/include/c++/12/pstl/glue_numeric_defs.h \
  /usr/include/c++/12/ratio \
  /usr/include/c++/12/stdexcept \
  /usr/include/c++/12/streambuf \
  /usr/include/c++/12/string \
  /usr/include/c++/12/string_view \
  /usr/include/c++/12/system_error \
  /usr/include/c++/12/tr1/bessel_function.tcc \
  /usr/include/c++/12/tr1/beta_function.tcc \
  /usr/include/c++/12/tr1/ell_integral.tcc \
  /usr/include/c++/12/tr1/exp_integral.tcc \
  /usr/include/c++/12/tr1/gamma.tcc \
  /usr/include/c++/12/tr1/hypergeometric.tcc \
  /usr/include/c++/12/tr1/legendre_function.tcc \
  /usr/include/c++/12/tr1/modified_bessel_func.tcc \
  /usr/include/c++/12/tr1/poly_hermite.tcc \
  /usr/include/c++/12/tr1/poly_laguerre.tcc \
  /usr/include/c++/12/tr1/riemann_zeta.tcc \
  /usr/include/c++/12/tr1/special_function_util.h \
  /usr/include/c++/12/tuple \
  /usr/include/c++/12/type_traits \
  /usr/include/c++/12/typeinfo \
  /usr/include/c++/12/unordered_map \
  /usr/include/c++/12/utility \
  /usr/include/c++/12/variant \
  /usr/include/c++/12/vector \
  /usr/include/ctype.h \
  /usr/include/dirent.h \
  /usr/include/endian.h \
  /usr/include/errno.h \
  /usr/include/features-time64.h \
  /usr/include/features.h \
  /usr/include/glib-2.0/gio/gaction.h \
  /usr/include/glib-2.0/gio/gactiongroup.h \
  /usr/include/glib-2.0/gio/gactiongroupexporter.h \
  /usr/include/glib-2.0/gio/gactionmap.h \
  /usr/include/glib-2.0/gio/gappinfo.h \
  /usr/include/glib-2.0/gio/gapplication.h \
  /usr/include/glib-2.0/gio/gapplicationcommandline.h \
  /usr/include/glib-2.0/gio/gasyncinitable.h \
  /usr/include/glib-2.0/gio/gasyncresult.h \
  /usr/include/glib-2.0/gio/gbufferedinputstream.h \
  /usr/include/glib-2.0/gio/gbufferedoutputstream.h \
  /usr/include/glib-2.0/gio/gbytesicon.h \
  /usr/include/glib-2.0/gio/gcancellable.h \
  /usr/include/glib-2.0/gio/gcharsetconverter.h \
  /usr/include/glib-2.0/gio/gcontenttype.h \
  /usr/include/glib-2.0/gio/gconverter.h \
  /usr/include/glib-2.0/gio/gconverterinputstream.h \
  /usr/include/glib-2.0/gio/gconverteroutputstream.h \
  /usr/include/glib-2.0/gio/gcredentials.h \
  /usr/include/glib-2.0/gio/gdatagrambased.h \
  /usr/include/glib-2.0/gio/gdatainputstream.h \
  /usr/include/glib-2.0/gio/gdataoutputstream.h \
  /usr/include/glib-2.0/gio/gdbusactiongroup.h \
  /usr/include/glib-2.0/gio/gdbusaddress.h \
  /usr/include/glib-2.0/gio/gdbusauthobserver.h \
  /usr/include/glib-2.0/gio/gdbusconnection.h \
  /usr/include/glib-2.0/gio/gdbuserror.h \
  /usr/include/glib-2.0/gio/gdbusinterface.h \
  /usr/include/glib-2.0/gio/gdbusinterfaceskeleton.h \
  /usr/include/glib-2.0/gio/gdbusintrospection.h \
  /usr/include/glib-2.0/gio/gdbusmenumodel.h \
  /usr/include/glib-2.0/gio/gdbusmessage.h \
  /usr/include/glib-2.0/gio/gdbusmethodinvocation.h \
  /usr/include/glib-2.0/gio/gdbusnameowning.h \
  /usr/include/glib-2.0/gio/gdbusnamewatching.h \
  /usr/include/glib-2.0/gio/gdbusobject.h \
  /usr/include/glib-2.0/gio/gdbusobjectmanager.h \
  /usr/include/glib-2.0/gio/gdbusobjectmanagerclient.h \
  /usr/include/glib-2.0/gio/gdbusobjectmanagerserver.h \
  /usr/include/glib-2.0/gio/gdbusobjectproxy.h \
  /usr/include/glib-2.0/gio/gdbusobjectskeleton.h \
  /usr/include/glib-2.0/gio/gdbusproxy.h \
  /usr/include/glib-2.0/gio/gdbusserver.h \
  /usr/include/glib-2.0/gio/gdbusutils.h \
  /usr/include/glib-2.0/gio/gdebugcontroller.h \
  /usr/include/glib-2.0/gio/gdebugcontrollerdbus.h \
  /usr/include/glib-2.0/gio/gdrive.h \
  /usr/include/glib-2.0/gio/gdtlsclientconnection.h \
  /usr/include/glib-2.0/gio/gdtlsconnection.h \
  /usr/include/glib-2.0/gio/gdtlsserverconnection.h \
  /usr/include/glib-2.0/gio/gemblem.h \
  /usr/include/glib-2.0/gio/gemblemedicon.h \
  /usr/include/glib-2.0/gio/gfile.h \
  /usr/include/glib-2.0/gio/gfileattribute.h \
  /usr/include/glib-2.0/gio/gfileenumerator.h \
  /usr/include/glib-2.0/gio/gfileicon.h \
  /usr/include/glib-2.0/gio/gfileinfo.h \
  /usr/include/glib-2.0/gio/gfileinputstream.h \
  /usr/include/glib-2.0/gio/gfileiostream.h \
  /usr/include/glib-2.0/gio/gfilemonitor.h \
  /usr/include/glib-2.0/gio/gfilenamecompleter.h \
  /usr/include/glib-2.0/gio/gfileoutputstream.h \
  /usr/include/glib-2.0/gio/gfilterinputstream.h \
  /usr/include/glib-2.0/gio/gfilteroutputstream.h \
  /usr/include/glib-2.0/gio/gicon.h \
  /usr/include/glib-2.0/gio/ginetaddress.h \
  /usr/include/glib-2.0/gio/ginetaddressmask.h \
  /usr/include/glib-2.0/gio/ginetsocketaddress.h \
  /usr/include/glib-2.0/gio/ginitable.h \
  /usr/include/glib-2.0/gio/ginputstream.h \
  /usr/include/glib-2.0/gio/gio-autocleanups.h \
  /usr/include/glib-2.0/gio/gio-visibility.h \
  /usr/include/glib-2.0/gio/gio.h \
  /usr/include/glib-2.0/gio/gioenums.h \
  /usr/include/glib-2.0/gio/gioenumtypes.h \
  /usr/include/glib-2.0/gio/gioerror.h \
  /usr/include/glib-2.0/gio/giomodule.h \
  /usr/include/glib-2.0/gio/gioscheduler.h \
  /usr/include/glib-2.0/gio/giostream.h \
  /usr/include/glib-2.0/gio/giotypes.h \
  /usr/include/glib-2.0/gio/glistmodel.h \
  /usr/include/glib-2.0/gio/gliststore.h \
  /usr/include/glib-2.0/gio/gloadableicon.h \
  /usr/include/glib-2.0/gio/gmemoryinputstream.h \
  /usr/include/glib-2.0/gio/gmemorymonitor.h \
  /usr/include/glib-2.0/gio/gmemoryoutputstream.h \
  /usr/include/glib-2.0/gio/gmenu.h \
  /usr/include/glib-2.0/gio/gmenuexporter.h \
  /usr/include/glib-2.0/gio/gmenumodel.h \
  /usr/include/glib-2.0/gio/gmount.h \
  /usr/include/glib-2.0/gio/gmountoperation.h \
  /usr/include/glib-2.0/gio/gnativesocketaddress.h \
  /usr/include/glib-2.0/gio/gnativevolumemonitor.h \
  /usr/include/glib-2.0/gio/gnetworkaddress.h \
  /usr/include/glib-2.0/gio/gnetworkmonitor.h \
  /usr/include/glib-2.0/gio/gnetworkservice.h \
  /usr/include/glib-2.0/gio/gnotification.h \
  /usr/include/glib-2.0/gio/goutputstream.h \
  /usr/include/glib-2.0/gio/gpermission.h \
  /usr/include/glib-2.0/gio/gpollableinputstream.h \
  /usr/include/glib-2.0/gio/gpollableoutputstream.h \
  /usr/include/glib-2.0/gio/gpollableutils.h \
  /usr/include/glib-2.0/gio/gpowerprofilemonitor.h \
  /usr/include/glib-2.0/gio/gpropertyaction.h \
  /usr/include/glib-2.0/gio/gproxy.h \
  /usr/include/glib-2.0/gio/gproxyaddress.h \
  /usr/include/glib-2.0/gio/gproxyaddressenumerator.h \
  /usr/include/glib-2.0/gio/gproxyresolver.h \
  /usr/include/glib-2.0/gio/gremoteactiongroup.h \
  /usr/include/glib-2.0/gio/gresolver.h \
  /usr/include/glib-2.0/gio/gresource.h \
  /usr/include/glib-2.0/gio/gseekable.h \
  /usr/include/glib-2.0/gio/gsettings.h \
  /usr/include/glib-2.0/gio/gsettingsschema.h \
  /usr/include/glib-2.0/gio/gsimpleaction.h \
  /usr/include/glib-2.0/gio/gsimpleactiongroup.h \
  /usr/include/glib-2.0/gio/gsimpleasyncresult.h \
  /usr/include/glib-2.0/gio/gsimpleiostream.h \
  /usr/include/glib-2.0/gio/gsimplepermission.h \
  /usr/include/glib-2.0/gio/gsimpleproxyresolver.h \
  /usr/include/glib-2.0/gio/gsocket.h \
  /usr/include/glib-2.0/gio/gsocketaddress.h \
  /usr/include/glib-2.0/gio/gsocketaddressenumerator.h \
  /usr/include/glib-2.0/gio/gsocketclient.h \
  /usr/include/glib-2.0/gio/gsocketconnectable.h \
  /usr/include/glib-2.0/gio/gsocketconnection.h \
  /usr/include/glib-2.0/gio/gsocketcontrolmessage.h \
  /usr/include/glib-2.0/gio/gsocketlistener.h \
  /usr/include/glib-2.0/gio/gsocketservice.h \
  /usr/include/glib-2.0/gio/gsrvtarget.h \
  /usr/include/glib-2.0/gio/gsubprocess.h \
  /usr/include/glib-2.0/gio/gsubprocesslauncher.h \
  /usr/include/glib-2.0/gio/gtask.h \
  /usr/include/glib-2.0/gio/gtcpconnection.h \
  /usr/include/glib-2.0/gio/gtcpwrapperconnection.h \
  /usr/include/glib-2.0/gio/gtestdbus.h \
  /usr/include/glib-2.0/gio/gthemedicon.h \
  /usr/include/glib-2.0/gio/gthreadedsocketservice.h \
  /usr/include/glib-2.0/gio/gtlsbackend.h \
  /usr/include/glib-2.0/gio/gtlscertificate.h \
  /usr/include/glib-2.0/gio/gtlsclientconnection.h \
  /usr/include/glib-2.0/gio/gtlsconnection.h \
  /usr/include/glib-2.0/gio/gtlsdatabase.h \
  /usr/include/glib-2.0/gio/gtlsfiledatabase.h \
  /usr/include/glib-2.0/gio/gtlsinteraction.h \
  /usr/include/glib-2.0/gio/gtlspassword.h \
  /usr/include/glib-2.0/gio/gtlsserverconnection.h \
  /usr/include/glib-2.0/gio/gunixconnection.h \
  /usr/include/glib-2.0/gio/gunixcredentialsmessage.h \
  /usr/include/glib-2.0/gio/gunixfdlist.h \
  /usr/include/glib-2.0/gio/gunixsocketaddress.h \
  /usr/include/glib-2.0/gio/gvfs.h \
  /usr/include/glib-2.0/gio/gvolume.h \
  /usr/include/glib-2.0/gio/gvolumemonitor.h \
  /usr/include/glib-2.0/gio/gzlibcompressor.h \
  /usr/include/glib-2.0/gio/gzlibdecompressor.h \
  /usr/include/glib-2.0/glib-object.h \
  /usr/include/glib-2.0/glib.h \
  /usr/include/glib-2.0/glib/deprecated/gallocator.h \
  /usr/include/glib-2.0/glib/deprecated/gcache.h \
  /usr/include/glib-2.0/glib/deprecated/gcompletion.h \
  /usr/include/glib-2.0/glib/deprecated/gmain.h \
  /usr/include/glib-2.0/glib/deprecated/grel.h \
  /usr/include/glib-2.0/glib/deprecated/gthread.h \
  /usr/include/glib-2.0/glib/galloca.h \
  /usr/include/glib-2.0/glib/garray.h \
  /usr/include/glib-2.0/glib/gasyncqueue.h \
  /usr/include/glib-2.0/glib/gatomic.h \
  /usr/include/glib-2.0/glib/gbacktrace.h \
  /usr/include/glib-2.0/glib/gbase64.h \
  /usr/include/glib-2.0/glib/gbitlock.h \
  /usr/include/glib-2.0/glib/gbookmarkfile.h \
  /usr/include/glib-2.0/glib/gbytes.h \
  /usr/include/glib-2.0/glib/gcharset.h \
  /usr/include/glib-2.0/glib/gchecksum.h \
  /usr/include/glib-2.0/glib/gconvert.h \
  /usr/include/glib-2.0/glib/gdataset.h \
  /usr/include/glib-2.0/glib/gdate.h \
  /usr/include/glib-2.0/glib/gdatetime.h \
  /usr/include/glib-2.0/glib/gdir.h \
  /usr/include/glib-2.0/glib/genviron.h \
  /usr/include/glib-2.0/glib/gerror.h \
  /usr/include/glib-2.0/glib/gfileutils.h \
  /usr/include/glib-2.0/glib/ggettext.h \
  /usr/include/glib-2.0/glib/ghash.h \
  /usr/include/glib-2.0/glib/ghmac.h \
  /usr/include/glib-2.0/glib/ghook.h \
  /usr/include/glib-2.0/glib/ghostutils.h \
  /usr/include/glib-2.0/glib/giochannel.h \
  /usr/include/glib-2.0/glib/gkeyfile.h \
  /usr/include/glib-2.0/glib/glib-autocleanups.h \
  /usr/include/glib-2.0/glib/glib-typeof.h \
  /usr/include/glib-2.0/glib/glib-visibility.h \
  /usr/include/glib-2.0/glib/glist.h \
  /usr/include/glib-2.0/glib/gmacros.h \
  /usr/include/glib-2.0/glib/gmain.h \
  /usr/include/glib-2.0/glib/gmappedfile.h \
  /usr/include/glib-2.0/glib/gmarkup.h \
  /usr/include/glib-2.0/glib/gmem.h \
  /usr/include/glib-2.0/glib/gmessages.h \
  /usr/include/glib-2.0/glib/gnode.h \
  /usr/include/glib-2.0/glib/goption.h \
  /usr/include/glib-2.0/glib/gpathbuf.h \
  /usr/include/glib-2.0/glib/gpattern.h \
  /usr/include/glib-2.0/glib/gpoll.h \
  /usr/include/glib-2.0/glib/gprimes.h \
  /usr/include/glib-2.0/glib/gqsort.h \
  /usr/include/glib-2.0/glib/gquark.h \
  /usr/include/glib-2.0/glib/gqueue.h \
  /usr/include/glib-2.0/glib/grand.h \
  /usr/include/glib-2.0/glib/grcbox.h \
  /usr/include/glib-2.0/glib/grefcount.h \
  /usr/include/glib-2.0/glib/grefstring.h \
  /usr/include/glib-2.0/glib/gregex.h \
  /usr/include/glib-2.0/glib/gscanner.h \
  /usr/include/glib-2.0/glib/gsequence.h \
  /usr/include/glib-2.0/glib/gshell.h \
  /usr/include/glib-2.0/glib/gslice.h \
  /usr/include/glib-2.0/glib/gslist.h \
  /usr/include/glib-2.0/glib/gspawn.h \
  /usr/include/glib-2.0/glib/gstrfuncs.h \
  /usr/include/glib-2.0/glib/gstring.h \
  /usr/include/glib-2.0/glib/gstringchunk.h \
  /usr/include/glib-2.0/glib/gstrvbuilder.h \
  /usr/include/glib-2.0/glib/gtestutils.h \
  /usr/include/glib-2.0/glib/gthread.h \
  /usr/include/glib-2.0/glib/gthreadpool.h \
  /usr/include/glib-2.0/glib/gtimer.h \
  /usr/include/glib-2.0/glib/gtimezone.h \
  /usr/include/glib-2.0/glib/gtrashstack.h \
  /usr/include/glib-2.0/glib/gtree.h \
  /usr/include/glib-2.0/glib/gtypes.h \
  /usr/include/glib-2.0/glib/gunicode.h \
  /usr/include/glib-2.0/glib/guri.h \
  /usr/include/glib-2.0/glib/gutils.h \
  /usr/include/glib-2.0/glib/guuid.h \
  /usr/include/glib-2.0/glib/gvariant.h \
  /usr/include/glib-2.0/glib/gvarianttype.h \
  /usr/include/glib-2.0/glib/gversion.h \
  /usr/include/glib-2.0/glib/gversionmacros.h \
  /usr/include/glib-2.0/gmodule.h \
  /usr/include/glib-2.0/gmodule/gmodule-visibility.h \
  /usr/include/glib-2.0/gobject/gbinding.h \
  /usr/include/glib-2.0/gobject/gbindinggroup.h \
  /usr/include/glib-2.0/gobject/gboxed.h \
  /usr/include/glib-2.0/gobject/gclosure.h \
  /usr/include/glib-2.0/gobject/genums.h \
  /usr/include/glib-2.0/gobject/glib-enumtypes.h \
  /usr/include/glib-2.0/gobject/glib-types.h \
  /usr/include/glib-2.0/gobject/gmarshal.h \
  /usr/include/glib-2.0/gobject/gobject-autocleanups.h \
  /usr/include/glib-2.0/gobject/gobject-visibility.h \
  /usr/include/glib-2.0/gobject/gobject.h \
  /usr/include/glib-2.0/gobject/gparam.h \
  /usr/include/glib-2.0/gobject/gparamspecs.h \
  /usr/include/glib-2.0/gobject/gsignal.h \
  /usr/include/glib-2.0/gobject/gsignalgroup.h \
  /usr/include/glib-2.0/gobject/gsourceclosure.h \
  /usr/include/glib-2.0/gobject/gtype.h \
  /usr/include/glib-2.0/gobject/gtypemodule.h \
  /usr/include/glib-2.0/gobject/gtypeplugin.h \
  /usr/include/glib-2.0/gobject/gvalue.h \
  /usr/include/glib-2.0/gobject/gvaluearray.h \
  /usr/include/glib-2.0/gobject/gvaluetypes.h \
  /usr/include/limits.h \
  /usr/include/linux/errno.h \
  /usr/include/linux/limits.h \
  /usr/include/locale.h \
  /usr/include/pthread.h \
  /usr/include/sched.h \
  /usr/include/signal.h \
  /usr/include/stdc-predef.h \
  /usr/include/stdint.h \
  /usr/include/stdio.h \
  /usr/include/stdlib.h \
  /usr/include/string.h \
  /usr/include/strings.h \
  /usr/include/time.h \
  /usr/include/unistd.h \
  /usr/include/wchar.h \
  /usr/include/x86_64-linux-gnu/asm/errno.h \
  /usr/include/x86_64-linux-gnu/bits/atomic_wide_counter.h \
  /usr/include/x86_64-linux-gnu/bits/byteswap.h \
  /usr/include/x86_64-linux-gnu/bits/confname.h \
  /usr/include/x86_64-linux-gnu/bits/cpu-set.h \
  /usr/include/x86_64-linux-gnu/bits/dirent.h \
  /usr/include/x86_64-linux-gnu/bits/dirent_ext.h \
  /usr/include/x86_64-linux-gnu/bits/endian.h \
  /usr/include/x86_64-linux-gnu/bits/endianness.h \
  /usr/include/x86_64-linux-gnu/bits/environments.h \
  /usr/include/x86_64-linux-gnu/bits/errno.h \
  /usr/include/x86_64-linux-gnu/bits/floatn-common.h \
  /usr/include/x86_64-linux-gnu/bits/floatn.h \
  /usr/include/x86_64-linux-gnu/bits/getopt_core.h \
  /usr/include/x86_64-linux-gnu/bits/getopt_posix.h \
  /usr/include/x86_64-linux-gnu/bits/libc-header-start.h \
  /usr/include/x86_64-linux-gnu/bits/local_lim.h \
  /usr/include/x86_64-linux-gnu/bits/locale.h \
  /usr/include/x86_64-linux-gnu/bits/long-double.h \
  /usr/include/x86_64-linux-gnu/bits/posix1_lim.h \
  /usr/include/x86_64-linux-gnu/bits/posix2_lim.h \
  /usr/include/x86_64-linux-gnu/bits/posix_opt.h \
  /usr/include/x86_64-linux-gnu/bits/pthread_stack_min-dynamic.h \
  /usr/include/x86_64-linux-gnu/bits/pthreadtypes-arch.h \
  /usr/include/x86_64-linux-gnu/bits/pthreadtypes.h \
  /usr/include/x86_64-linux-gnu/bits/sched.h \
  /usr/include/x86_64-linux-gnu/bits/select.h \
  /usr/include/x86_64-linux-gnu/bits/setjmp.h \
  /usr/include/x86_64-linux-gnu/bits/sigaction.h \
  /usr/include/x86_64-linux-gnu/bits/sigcontext.h \
  /usr/include/x86_64-linux-gnu/bits/sigevent-consts.h \
  /usr/include/x86_64-linux-gnu/bits/siginfo-arch.h \
  /usr/include/x86_64-linux-gnu/bits/siginfo-consts-arch.h \
  /usr/include/x86_64-linux-gnu/bits/siginfo-consts.h \
  /usr/include/x86_64-linux-gnu/bits/signal_ext.h \
  /usr/include/x86_64-linux-gnu/bits/signum-arch.h \
  /usr/include/x86_64-linux-gnu/bits/signum-generic.h \
  /usr/include/x86_64-linux-gnu/bits/sigstack.h \
  /usr/include/x86_64-linux-gnu/bits/sigstksz.h \
  /usr/include/x86_64-linux-gnu/bits/sigthread.h \
  /usr/include/x86_64-linux-gnu/bits/ss_flags.h \
  /usr/include/x86_64-linux-gnu/bits/stdint-intn.h \
  /usr/include/x86_64-linux-gnu/bits/stdint-uintn.h \
  /usr/include/x86_64-linux-gnu/bits/stdio_lim.h \
  /usr/include/x86_64-linux-gnu/bits/stdlib-float.h \
  /usr/include/x86_64-linux-gnu/bits/struct_mutex.h \
  /usr/include/x86_64-linux-gnu/bits/struct_rwlock.h \
  /usr/include/x86_64-linux-gnu/bits/thread-shared-types.h \
  /usr/include/x86_64-linux-gnu/bits/time.h \
  /usr/include/x86_64-linux-gnu/bits/time64.h \
  /usr/include/x86_64-linux-gnu/bits/timesize.h \
  /usr/include/x86_64-linux-gnu/bits/timex.h \
  /usr/include/x86_64-linux-gnu/bits/types.h \
  /usr/include/x86_64-linux-gnu/bits/types/FILE.h \
  /usr/include/x86_64-linux-gnu/bits/types/__FILE.h \
  /usr/include/x86_64-linux-gnu/bits/types/__fpos64_t.h \
  /usr/include/x86_64-linux-gnu/bits/types/__fpos_t.h \
  /usr/include/x86_64-linux-gnu/bits/types/__locale_t.h \
  /usr/include/x86_64-linux-gnu/bits/types/__mbstate_t.h \
  /usr/include/x86_64-linux-gnu/bits/types/__sigset_t.h \
  /usr/include/x86_64-linux-gnu/bits/types/__sigval_t.h \
  /usr/include/x86_64-linux-gnu/bits/types/clock_t.h \
  /usr/include/x86_64-linux-gnu/bits/types/clockid_t.h \
  /usr/include/x86_64-linux-gnu/bits/types/cookie_io_functions_t.h \
  /usr/include/x86_64-linux-gnu/bits/types/error_t.h \
  /usr/include/x86_64-linux-gnu/bits/types/locale_t.h \
  /usr/include/x86_64-linux-gnu/bits/types/mbstate_t.h \
  /usr/include/x86_64-linux-gnu/bits/types/sig_atomic_t.h \
  /usr/include/x86_64-linux-gnu/bits/types/sigevent_t.h \
  /usr/include/x86_64-linux-gnu/bits/types/siginfo_t.h \
  /usr/include/x86_64-linux-gnu/bits/types/sigset_t.h \
  /usr/include/x86_64-linux-gnu/bits/types/sigval_t.h \
  /usr/include/x86_64-linux-gnu/bits/types/stack_t.h \
  /usr/include/x86_64-linux-gnu/bits/types/struct_FILE.h \
  /usr/include/x86_64-linux-gnu/bits/types/struct___jmp_buf_tag.h \
  /usr/include/x86_64-linux-gnu/bits/types/struct_itimerspec.h \
  /usr/include/x86_64-linux-gnu/bits/types/struct_sched_param.h \
  /usr/include/x86_64-linux-gnu/bits/types/struct_sigstack.h \
  /usr/include/x86_64-linux-gnu/bits/types/struct_timespec.h \
  /usr/include/x86_64-linux-gnu/bits/types/struct_timeval.h \
  /usr/include/x86_64-linux-gnu/bits/types/struct_tm.h \
  /usr/include/x86_64-linux-gnu/bits/types/time_t.h \
  /usr/include/x86_64-linux-gnu/bits/types/timer_t.h \
  /usr/include/x86_64-linux-gnu/bits/types/wint_t.h \
  /usr/include/x86_64-linux-gnu/bits/typesizes.h \
  /usr/include/x86_64-linux-gnu/bits/uintn-identity.h \
  /usr/include/x86_64-linux-gnu/bits/uio_lim.h \
  /usr/include/x86_64-linux-gnu/bits/unistd_ext.h \
  /usr/include/x86_64-linux-gnu/bits/waitflags.h \
  /usr/include/x86_64-linux-gnu/bits/waitstatus.h \
  /usr/include/x86_64-linux-gnu/bits/wchar.h \
  /usr/include/x86_64-linux-gnu/bits/wordsize.h \
  /usr/include/x86_64-linux-gnu/bits/xopen_lim.h \
  /usr/include/x86_64-linux-gnu/c++/12/bits/atomic_word.h \
  /usr/include/x86_64-linux-gnu/c++/12/bits/c++allocator.h \
  /usr/include/x86_64-linux-gnu/c++/12/bits/c++config.h \
  /usr/include/x86_64-linux-gnu/c++/12/bits/c++locale.h \
  /usr/include/x86_64-linux-gnu/c++/12/bits/cpu_defines.h \
  /usr/include/x86_64-linux-gnu/c++/12/bits/error_constants.h \
  /usr/include/x86_64-linux-gnu/c++/12/bits/gthr-default.h \
  /usr/include/x86_64-linux-gnu/c++/12/bits/gthr.h \
  /usr/include/x86_64-linux-gnu/c++/12/bits/os_defines.h \
  /usr/include/x86_64-linux-gnu/gnu/stubs-64.h \
  /usr/include/x86_64-linux-gnu/gnu/stubs.h \
  /usr/include/x86_64-linux-gnu/qt6/QtCore/QObject \
  /usr/include/x86_64-linux-gnu/qt6/QtCore/q20functional.h \
  /usr/include/x86_64-linux-gnu/qt6/QtCore/q20memory.h \
  /usr/include/x86_64-linux-gnu/qt6/QtCore/q20type_traits.h \
  /usr/include/x86_64-linux-gnu/qt6/QtCore/qalgorithms.h \
  /usr/include/x86_64-linux-gnu/qt6/QtCore/qanystringview.h \
  /usr/include/x86_64-linux-gnu/qt6/QtCore/qarraydata.h \
  /usr/include/x86_64-linux-gnu/qt6/QtCore/qarraydataops.h \
  /usr/include/x86_64-linux-gnu/qt6/QtCore/qarraydatapointer.h \
  /usr/include/x86_64-linux-gnu/qt6/QtCore/qassert.h \
  /usr/include/x86_64-linux-gnu/qt6/QtCore/qatomic.h \
  /usr/include/x86_64-linux-gnu/qt6/QtCore/qatomic_cxx11.h \
  /usr/include/x86_64-linux-gnu/qt6/QtCore/qbasicatomic.h \
  /usr/include/x86_64-linux-gnu/qt6/QtCore/qbindingstorage.h \
  /usr/include/x86_64-linux-gnu/qt6/QtCore/qbytearray.h \
  /usr/include/x86_64-linux-gnu/qt6/QtCore/qbytearrayalgorithms.h \
  /usr/include/x86_64-linux-gnu/qt6/QtCore/qbytearraylist.h \
  /usr/include/x86_64-linux-gnu/qt6/QtCore/qbytearrayview.h \
  /usr/include/x86_64-linux-gnu/qt6/QtCore/qchar.h \
  /usr/include/x86_64-linux-gnu/qt6/QtCore/qcompare.h \
  /usr/include/x86_64-linux-gnu/qt6/QtCore/qcompare_impl.h \
  /usr/include/x86_64-linux-gnu/qt6/QtCore/qcomparehelpers.h \
  /usr/include/x86_64-linux-gnu/qt6/QtCore/qcompilerdetection.h \
  /usr/include/x86_64-linux-gnu/qt6/QtCore/qconfig.h \
  /usr/include/x86_64-linux-gnu/qt6/QtCore/qconstructormacros.h \
  /usr/include/x86_64-linux-gnu/qt6/QtCore/qcontainerfwd.h \
  /usr/include/x86_64-linux-gnu/qt6/QtCore/qcontainerinfo.h \
  /usr/include/x86_64-linux-gnu/qt6/QtCore/qcontainertools_impl.h \
  /usr/include/x86_64-linux-gnu/qt6/QtCore/qdarwinhelpers.h \
  /usr/include/x86_64-linux-gnu/qt6/QtCore/qdatastream.h \
  /usr/include/x86_64-linux-gnu/qt6/QtCore/qexceptionhandling.h \
  /usr/include/x86_64-linux-gnu/qt6/QtCore/qflags.h \
  /usr/include/x86_64-linux-gnu/qt6/QtCore/qfloat16.h \
  /usr/include/x86_64-linux-gnu/qt6/QtCore/qforeach.h \
  /usr/include/x86_64-linux-gnu/qt6/QtCore/qfunctionaltools_impl.h \
  /usr/include/x86_64-linux-gnu/qt6/QtCore/qfunctionpointer.h \
  /usr/include/x86_64-linux-gnu/qt6/QtCore/qgenericatomic.h \
  /usr/include/x86_64-linux-gnu/qt6/QtCore/qglobal.h \
  /usr/include/x86_64-linux-gnu/qt6/QtCore/qglobalstatic.h \
  /usr/include/x86_64-linux-gnu/qt6/QtCore/qhashfunctions.h \
  /usr/include/x86_64-linux-gnu/qt6/QtCore/qiodevicebase.h \
  /usr/include/x86_64-linux-gnu/qt6/QtCore/qiterable.h \
  /usr/include/x86_64-linux-gnu/qt6/QtCore/qiterator.h \
  /usr/include/x86_64-linux-gnu/qt6/QtCore/qlatin1stringview.h \
  /usr/include/x86_64-linux-gnu/qt6/QtCore/qlist.h \
  /usr/include/x86_64-linux-gnu/qt6/QtCore/qlogging.h \
  /usr/include/x86_64-linux-gnu/qt6/QtCore/qmalloc.h \
  /usr/include/x86_64-linux-gnu/qt6/QtCore/qmath.h \
  /usr/include/x86_64-linux-gnu/qt6/QtCore/qmetacontainer.h \
  /usr/include/x86_64-linux-gnu/qt6/QtCore/qmetatype.h \
  /usr/include/x86_64-linux-gnu/qt6/QtCore/qminmax.h \
  /usr/include/x86_64-linux-gnu/qt6/QtCore/qnamespace.h \
  /usr/include/x86_64-linux-gnu/qt6/QtCore/qnumeric.h \
  /usr/include/x86_64-linux-gnu/qt6/QtCore/qobject.h \
  /usr/include/x86_64-linux-gnu/qt6/QtCore/qobject_impl.h \
  /usr/include/x86_64-linux-gnu/qt6/QtCore/qobjectdefs.h \
  /usr/include/x86_64-linux-gnu/qt6/QtCore/qobjectdefs_impl.h \
  /usr/include/x86_64-linux-gnu/qt6/QtCore/qoverload.h \
  /usr/include/x86_64-linux-gnu/qt6/QtCore/qpair.h \
  /usr/include/x86_64-linux-gnu/qt6/QtCore/qprocessordetection.h \
  /usr/include/x86_64-linux-gnu/qt6/QtCore/qrefcount.h \
  /usr/include/x86_64-linux-gnu/qt6/QtCore/qscopedpointer.h \
  /usr/include/x86_64-linux-gnu/qt6/QtCore/qscopeguard.h \
  /usr/include/x86_64-linux-gnu/qt6/QtCore/qstring.h \
  /usr/include/x86_64-linux-gnu/qt6/QtCore/qstringalgorithms.h \
  /usr/include/x86_64-linux-gnu/qt6/QtCore/qstringbuilder.h \
  /usr/include/x86_64-linux-gnu/qt6/QtCore/qstringconverter.h \
  /usr/include/x86_64-linux-gnu/qt6/QtCore/qstringconverter_base.h \
  /usr/include/x86_64-linux-gnu/qt6/QtCore/qstringfwd.h \
  /usr/include/x86_64-linux-gnu/qt6/QtCore/qstringlist.h \
  /usr/include/x86_64-linux-gnu/qt6/QtCore/qstringliteral.h \
  /usr/include/x86_64-linux-gnu/qt6/QtCore/qstringmatcher.h \
  /usr/include/x86_64-linux-gnu/qt6/QtCore/qstringtokenizer.h \
  /usr/include/x86_64-linux-gnu/qt6/QtCore/qstringview.h \
  /usr/include/x86_64-linux-gnu/qt6/QtCore/qswap.h \
  /usr/include/x86_64-linux-gnu/qt6/QtCore/qsysinfo.h \
  /usr/include/x86_64-linux-gnu/qt6/QtCore/qsystemdetection.h \
  /usr/include/x86_64-linux-gnu/qt6/QtCore/qtaggedpointer.h \
  /usr/include/x86_64-linux-gnu/qt6/QtCore/qtclasshelpermacros.h \
  /usr/include/x86_64-linux-gnu/qt6/QtCore/qtconfiginclude.h \
  /usr/include/x86_64-linux-gnu/qt6/QtCore/qtconfigmacros.h \
  /usr/include/x86_64-linux-gnu/qt6/QtCore/qtcore-config.h \
  /usr/include/x86_64-linux-gnu/qt6/QtCore/qtcoreexports.h \
  /usr/include/x86_64-linux-gnu/qt6/QtCore/qtdeprecationdefinitions.h \
  /usr/include/x86_64-linux-gnu/qt6/QtCore/qtdeprecationmarkers.h \
  /usr/include/x86_64-linux-gnu/qt6/QtCore/qtenvironmentvariables.h \
  /usr/include/x86_64-linux-gnu/qt6/QtCore/qtmetamacros.h \
  /usr/include/x86_64-linux-gnu/qt6/QtCore/qtnoop.h \
  /usr/include/x86_64-linux-gnu/qt6/QtCore/qtpreprocessorsupport.h \
  /usr/include/x86_64-linux-gnu/qt6/QtCore/qtresource.h \
  /usr/include/x86_64-linux-gnu/qt6/QtCore/qttranslation.h \
  /usr/include/x86_64-linux-gnu/qt6/QtCore/qttypetraits.h \
  /usr/include/x86_64-linux-gnu/qt6/QtCore/qtversion.h \
  /usr/include/x86_64-linux-gnu/qt6/QtCore/qtversionchecks.h \
  /usr/include/x86_64-linux-gnu/qt6/QtCore/qtypeinfo.h \
  /usr/include/x86_64-linux-gnu/qt6/QtCore/qtypes.h \
  /usr/include/x86_64-linux-gnu/qt6/QtCore/qutf8stringview.h \
  /usr/include/x86_64-linux-gnu/qt6/QtCore/qversiontagging.h \
  /usr/include/x86_64-linux-gnu/qt6/QtCore/qxptype_traits.h \
  /usr/include/x86_64-linux-gnu/qt6/QtCore/qyieldcpu.h \
  /usr/include/x86_64-linux-gnu/sys/cdefs.h \
  /usr/include/x86_64-linux-gnu/sys/select.h \
  /usr/include/x86_64-linux-gnu/sys/types.h \
  /usr/include/x86_64-linux-gnu/sys/ucontext.h \
  /usr/lib/gcc/x86_64-linux-gnu/12/include/float.h \
  /usr/lib/gcc/x86_64-linux-gnu/12/include/stdarg.h \
  /usr/lib/gcc/x86_64-linux-gnu/12/include/stdbool.h \
  /usr/lib/gcc/x86_64-linux-gnu/12/include/stddef.h \
  /usr/lib/x86_64-linux-gnu/glib-2.0/include/glibconfig.h
