[{"moduleFiles": [{"entries": [{"codegenSuccessfull": false, "column": 5, "durationMicroseconds": 0, "errorMessage": "Functions without type annotations won't be compiled", "functionName": "dropOnItem", "line": 53}, {"codegenSuccessfull": false, "column": 5, "durationMicroseconds": 0, "errorMessage": "Functions without type annotations won't be compiled", "functionName": "dropOnPage", "line": 58}, {"codegenSuccessfull": true, "column": 22, "durationMicroseconds": 564, "errorMessage": "", "functionName": "onInputReceived", "line": 744}, {"codegenSuccessfull": true, "column": 22, "durationMicroseconds": 50, "errorMessage": "", "functionName": "onInputReceived", "line": 744}, {"codegenSuccessfull": false, "column": 19, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type  for binding on fill.", "functionName": "fill", "line": 19}, {"codegenSuccessfull": false, "column": 21, "durationMicroseconds": 256, "errorMessage": "Cannot generate efficient code for internal conversion with incompatible or ambiguous types: QVariant -> Dtk::Quick::DColor stored as QVariant", "functionName": "common", "line": 24}, {"codegenSuccessfull": false, "column": 22, "durationMicroseconds": 222, "errorMessage": "Cannot generate efficient code for internal conversion with incompatible or ambiguous types: QVariant -> Dtk::Quick::DColor stored as QVariant", "functionName": "crystal", "line": 25}, {"codegenSuccessfull": false, "column": 21, "durationMicroseconds": 261, "errorMessage": "Cannot generate efficient code for internal conversion with incompatible or ambiguous types: QVariant -> Dtk::Quick::DColor stored as QVariant", "functionName": "common", "line": 28}, {"codegenSuccessfull": false, "column": 22, "durationMicroseconds": 240, "errorMessage": "Cannot generate efficient code for internal conversion with incompatible or ambiguous types: QVariant -> Dtk::Quick::DColor stored as QVariant", "functionName": "crystal", "line": 29}, {"codegenSuccessfull": false, "column": 18, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type  for binding on visible.", "functionName": "visible", "line": 40}, {"codegenSuccessfull": false, "column": 31, "durationMicroseconds": 56, "errorMessage": "Cannot find name text", "functionName": "onActiveChanged", "line": 43}, {"codegenSuccessfull": true, "column": 50, "durationMicroseconds": 387, "errorMessage": "", "functionName": "isHorizontalDock", "line": 78}, {"codegenSuccessfull": true, "column": 44, "durationMicroseconds": 358, "errorMessage": "", "functionName": "dockSpacing", "line": 79}, {"codegenSuccessfull": true, "column": 22, "durationMicroseconds": 240, "errorMessage": "", "functionName": "leftPadding", "line": 81}, {"codegenSuccessfull": true, "column": 23, "durationMicroseconds": 246, "errorMessage": "", "functionName": "rightPadding", "line": 82}, {"codegenSuccessfull": true, "column": 21, "durationMicroseconds": 288, "errorMessage": "", "functionName": "topPadding", "line": 83}, {"codegenSuccessfull": true, "column": 24, "durationMicroseconds": 289, "errorMessage": "", "functionName": "bottomPadding", "line": 84}, {"codegenSuccessfull": false, "column": 37, "durationMicroseconds": 47, "errorMessage": "Cannot access value for name appTextColor", "functionName": "textColor", "line": 86}, {"codegenSuccessfull": false, "column": 9, "durationMicroseconds": 323, "errorMessage": "Cannot access value for name ItemArrangementProxyModel", "functionName": "tryToRemoveEmptyPage", "line": 89}, {"codegenSuccessfull": true, "column": 23, "durationMicroseconds": 55, "errorMessage": "", "functionName": "fill", "line": 67}, {"codegenSuccessfull": true, "column": 22, "durationMicroseconds": 80, "errorMessage": "", "functionName": "context", "line": 72}, {"codegenSuccessfull": true, "column": 24, "durationMicroseconds": 184, "errorMessage": "", "functionName": "sequences", "line": 73}, {"codegenSuccessfull": true, "column": 26, "durationMicroseconds": 81, "errorMessage": "", "functionName": "onActivated", "line": 74}, {"codegenSuccessfull": true, "column": 37, "durationMicroseconds": 75, "errorMessage": "", "functionName": "onActivatedAmbiguously", "line": 75}, {"codegenSuccessfull": false, "column": 29, "durationMicroseconds": 31, "errorMessage": "Cannot load property textColor from Dtk::Quick::DQuickControlColorSelector.", "functionName": "windowText", "line": 87}, {"codegenSuccessfull": true, "column": 55, "durationMicroseconds": 166, "errorMessage": "", "functionName": "horizontalPadding", "line": 97}, {"codegenSuccessfull": true, "column": 13, "durationMicroseconds": 897, "errorMessage": "", "functionName": "checkDragMove", "line": 102}, {"codegenSuccessfull": true, "column": 19, "durationMicroseconds": 110, "errorMessage": "", "functionName": "keys", "line": 116}, {"codegenSuccessfull": true, "column": 24, "durationMicroseconds": 198, "errorMessage": "", "functionName": "onEntered", "line": 117}, {"codegenSuccessfull": true, "column": 32, "durationMicroseconds": 65, "errorMessage": "", "functionName": "onPositionChanged", "line": 122}, {"codegenSuccessfull": false, "column": 25, "durationMicroseconds": 185, "errorMessage": "method dropOnPage cannot be resolved.", "functionName": "onDropped", "line": 125}, {"codegenSuccessfull": true, "column": 25, "durationMicroseconds": 46, "errorMessage": "", "functionName": "onDropped", "line": 125}, {"codegenSuccessfull": true, "column": 23, "durationMicroseconds": 54, "errorMessage": "", "functionName": "onExited", "line": 136}, {"codegenSuccessfull": true, "column": 34, "durationMicroseconds": 247, "errorMessage": "", "functionName": "onPageIntentChanged", "line": 139}, {"codegenSuccessfull": true, "column": 27, "durationMicroseconds": 54, "errorMessage": "", "functionName": "fill", "line": 98}, {"codegenSuccessfull": false, "column": 30, "durationMicroseconds": 46, "errorMessage": "Cannot load property pageIntent from (component in /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/FullscreenFrame.qml)::parent with type QObject.", "functionName": "onTriggered", "line": 150}, {"codegenSuccessfull": false, "column": 25, "durationMicroseconds": 21, "errorMessage": "Cannot retrieve a non-object type by ID: dndItem", "functionName": "target", "line": 174}, {"codegenSuccessfull": true, "column": 17, "durationMicroseconds": 243, "errorMessage": "", "functionName": "onDragEnded", "line": 175}, {"codegenSuccessfull": false, "column": 21, "durationMicroseconds": 66, "errorMessage": "cannot convert from conversion to QJSPrimitiveValue to QUrl", "functionName": "source", "line": 191}, {"codegenSuccessfull": true, "column": 25, "durationMicroseconds": 384, "errorMessage": "", "functionName": "sourceSize", "line": 192}, {"codegenSuccessfull": false, "column": 24, "durationMicroseconds": 724, "errorMessage": "Cannot generate efficient code for internal conversion with incompatible or ambiguous types: conversion to QVariant -> QColor stored as QVariant", "functionName": "color", "line": 196}, {"codegenSuccessfull": true, "column": 31, "durationMicroseconds": 69, "errorMessage": "", "functionName": "fill", "line": 195}, {"codegenSuccessfull": true, "column": 32, "durationMicroseconds": 329, "errorMessage": "", "functionName": "onClicked", "line": 204}, {"codegenSuccessfull": false, "column": 30, "durationMicroseconds": 629, "errorMessage": "method decrementPageIndex cannot be resolved.", "functionName": "onWheel", "line": 210}, {"codegenSuccessfull": true, "column": 30, "durationMicroseconds": 50, "errorMessage": "", "functionName": "onWheel", "line": 210}, {"codegenSuccessfull": true, "column": 35, "durationMicroseconds": 53, "errorMessage": "", "functionName": "fill", "line": 202}, {"codegenSuccessfull": true, "column": 37, "durationMicroseconds": 83, "errorMessage": "", "functionName": "implicitHeight", "line": 249}, {"codegenSuccessfull": true, "column": 30, "durationMicroseconds": 139, "errorMessage": "", "functionName": "opacity", "line": 250}, {"codegenSuccessfull": true, "column": 36, "durationMicroseconds": 587, "errorMessage": "", "functionName": "onClicked", "line": 265}, {"codegenSuccessfull": true, "column": 40, "durationMicroseconds": 112, "errorMessage": "", "functionName": "right", "line": 256}, {"codegenSuccessfull": true, "column": 47, "durationMicroseconds": 70, "errorMessage": "", "functionName": "family", "line": 257}, {"codegenSuccessfull": true, "column": 42, "durationMicroseconds": 51, "errorMessage": "", "functionName": "visible", "line": 259}, {"codegenSuccessfull": false, "column": 37, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type  for binding on button.", "functionName": "button", "line": 263}, {"codegenSuccessfull": true, "column": 34, "durationMicroseconds": 102, "errorMessage": "", "functionName": "visible", "line": 278}, {"codegenSuccessfull": true, "column": 32, "durationMicroseconds": 169, "errorMessage": "", "functionName": "count", "line": 279}, {"codegenSuccessfull": true, "column": 39, "durationMicroseconds": 168, "errorMessage": "", "functionName": "currentIndex", "line": 280}, {"codegenSuccessfull": true, "column": 51, "durationMicroseconds": 109, "errorMessage": "", "functionName": "horizontalCenter", "line": 276}, {"codegenSuccessfull": true, "column": 49, "durationMicroseconds": 102, "errorMessage": "", "functionName": "verticalCenter", "line": 277}, {"codegenSuccessfull": true, "column": 37, "durationMicroseconds": 126, "errorMessage": "", "functionName": "radius", "line": 287}, {"codegenSuccessfull": false, "column": 36, "durationMicroseconds": 83, "errorMessage": "Cannot access value for name index", "functionName": "color", "line": 288}, {"codegenSuccessfull": false, "column": 41, "durationMicroseconds": 47, "errorMessage": "Cannot load property radius from (component in /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/FullscreenFrame.qml)::parent with type QQuickItem.", "functionName": "radius", "line": 291}, {"codegenSuccessfull": false, "column": 40, "durationMicroseconds": 239, "errorMessage": "Cannot generate efficient code for internal conversion with incompatible or ambiguous types: QVariant -> QColor stored as QVariant", "functionName": "color", "line": 293}, {"codegenSuccessfull": true, "column": 47, "durationMicroseconds": 52, "errorMessage": "", "functionName": "fill", "line": 290}, {"codegenSuccessfull": false, "column": 34, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type  for binding on sourceModel.", "functionName": "sourceModel", "line": 307}, {"codegenSuccessfull": true, "column": 31, "durationMicroseconds": 82, "errorMessage": "", "functionName": "snapMode", "line": 314}, {"codegenSuccessfull": true, "column": 34, "durationMicroseconds": 72, "errorMessage": "", "functionName": "orientation", "line": 315}, {"codegenSuccessfull": true, "column": 41, "durationMicroseconds": 71, "errorMessage": "", "functionName": "highlightRangeMode", "line": 316}, {"codegenSuccessfull": true, "column": 30, "durationMicroseconds": 144, "errorMessage": "", "functionName": "visible", "line": 323}, {"codegenSuccessfull": true, "column": 35, "durationMicroseconds": 72, "errorMessage": "", "functionName": "currentIndex", "line": 325}, {"codegenSuccessfull": false, "column": 21, "durationMicroseconds": 0, "errorMessage": "Functions without type annotations won't be compiled", "functionName": "setCurrentIndex", "line": 326}, {"codegenSuccessfull": false, "column": 28, "durationMicroseconds": 20, "errorMessage": "Cannot retrieve a non-object type by ID: itemPageModel", "functionName": "model", "line": 332}, {"codegenSuccessfull": true, "column": 35, "durationMicroseconds": 52, "errorMessage": "", "functionName": "fill", "line": 313}, {"codegenSuccessfull": false, "column": 32, "durationMicroseconds": 33, "errorMessage": "Cannot access value for name listviewPage", "functionName": "width", "line": 336}, {"codegenSuccessfull": false, "column": 33, "durationMicroseconds": 23, "errorMessage": "Cannot access value for name listviewPage", "functionName": "height", "line": 337}, {"codegenSuccessfull": false, "column": 49, "durationMicroseconds": 16, "errorMessage": "Cannot access value for name index", "functionName": "viewIndex", "line": 339}, {"codegenSuccessfull": false, "column": 39, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type  for binding on sortRole.", "functionName": "sortRole", "line": 349}, {"codegenSuccessfull": false, "column": 46, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type  for binding on sourceModel.", "functionName": "sourceModel", "line": 345}, {"codegenSuccessfull": false, "column": 41, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type  for binding on pageId.", "functionName": "pageId", "line": 346}, {"codegenSuccessfull": false, "column": 52, "durationMicroseconds": 23, "errorMessage": "Cannot retrieve a non-object type by ID: proxyModel", "functionName": "onCompleted", "line": 350}, {"codegenSuccessfull": true, "column": 46, "durationMicroseconds": 195, "errorMessage": "", "functionName": "acceptedButtons", "line": 357}, {"codegenSuccessfull": true, "column": 40, "durationMicroseconds": 384, "errorMessage": "", "functionName": "onClicked", "line": 358}, {"codegenSuccessfull": true, "column": 40, "durationMicroseconds": 45, "errorMessage": "", "functionName": "onClicked", "line": 358}, {"codegenSuccessfull": true, "column": 43, "durationMicroseconds": 51, "errorMessage": "", "functionName": "fill", "line": 356}, {"codegenSuccessfull": false, "column": 36, "durationMicroseconds": 20, "errorMessage": "Cannot retrieve a non-object type by ID: proxyModel", "functionName": "model", "line": 375}, {"codegenSuccessfull": false, "column": 29, "durationMicroseconds": 63, "errorMessage": "Cannot access value for name listviewPage", "functionName": "checkPageSwitchState", "line": 380}, {"codegenSuccessfull": false, "column": 38, "durationMicroseconds": 38, "errorMessage": "Cannot access value for name folderGridViewPopup", "functionName": "opacity", "line": 416}, {"codegenSuccessfull": false, "column": 55, "durationMicroseconds": 36, "errorMessage": "Cannot access value for name listviewPage", "functionName": "activeGridViewFocusOnTab", "line": 420}, {"codegenSuccessfull": true, "column": 43, "durationMicroseconds": 54, "errorMessage": "", "functionName": "fill", "line": 371}, {"codegenSuccessfull": false, "column": 49, "durationMicroseconds": 100, "errorMessage": "Cannot access value for name itemPageModel", "functionName": "onLeftPressed", "line": 398}, {"codegenSuccessfull": true, "column": 49, "durationMicroseconds": 45, "errorMessage": "", "functionName": "onLeftPressed", "line": 398}, {"codegenSuccessfull": false, "column": 50, "durationMicroseconds": 62, "errorMessage": "Cannot access value for name itemPageModel", "functionName": "onRightPressed", "line": 407}, {"codegenSuccessfull": true, "column": 50, "durationMicroseconds": 42, "errorMessage": "", "functionName": "onRightPressed", "line": 407}, {"codegenSuccessfull": true, "column": 79, "durationMicroseconds": 69, "errorMessage": "", "functionName": "type", "line": 418}, {"codegenSuccessfull": true, "column": 50, "durationMicroseconds": 70, "errorMessage": "", "functionName": "type", "line": 427}, {"codegenSuccessfull": false, "column": 42, "durationMicroseconds": 67, "errorMessage": "Cannot access value for name folderGridViewPopup", "functionName": "visible", "line": 433}, {"codegenSuccessfull": false, "column": 40, "durationMicroseconds": 42, "errorMessage": "Cannot access value for name gridViewContainer", "functionName": "width", "line": 434}, {"codegenSuccessfull": false, "column": 41, "durationMicroseconds": 38, "errorMessage": "Cannot access value for name gridViewContainer", "functionName": "height", "line": 435}, {"codegenSuccessfull": false, "column": 44, "durationMicroseconds": 56, "errorMessage": "Cannot access value for name folderGridViewPopup", "functionName": "onEntered", "line": 436}, {"codegenSuccessfull": true, "column": 44, "durationMicroseconds": 63, "errorMessage": "", "functionName": "onEntered", "line": 436}, {"codegenSuccessfull": true, "column": 43, "durationMicroseconds": 249, "errorMessage": "", "functionName": "onExited", "line": 443}, {"codegenSuccessfull": false, "column": 44, "durationMicroseconds": 306, "errorMessage": "Cannot access value for name model", "functionName": "onDropped", "line": 447}, {"codegenSuccessfull": true, "column": 44, "durationMicroseconds": 62, "errorMessage": "", "functionName": "onDropped", "line": 447}, {"codegenSuccessfull": true, "column": 49, "durationMicroseconds": 166, "errorMessage": "", "functionName": "forwardTo", "line": 431}, {"codegenSuccessfull": false, "column": 50, "durationMicroseconds": 107, "errorMessage": "Cannot access value for name width", "functionName": "onTriggered", "line": 466}, {"codegenSuccessfull": true, "column": 50, "durationMicroseconds": 47, "errorMessage": "", "functionName": "onTriggered", "line": 466}, {"codegenSuccessfull": false, "column": 49, "durationMicroseconds": 53, "errorMessage": "Cannot access value for name folderGridViewPopup", "functionName": "dndEnabled", "line": 490}, {"codegenSuccessfull": false, "column": 46, "durationMicroseconds": 33, "errorMessage": "Cannot access value for name dndItem", "functionName": "visible", "line": 492}, {"codegenSuccessfull": false, "column": 49, "durationMicroseconds": 197, "errorMessage": "Cannot access value for name iconName", "functionName": "iconSource", "line": 493}, {"codegenSuccessfull": false, "column": 44, "durationMicroseconds": 184, "errorMessage": "Cannot access value for name folderIcons", "functionName": "icons", "line": 494}, {"codegenSuccessfull": false, "column": 52, "durationMicroseconds": 178, "errorMessage": "Cannot access value for name desktopId", "functionName": "onItemClicked", "line": 495}, {"codegenSuccessfull": false, "column": 54, "durationMicroseconds": 68, "errorMessage": "Cannot access value for name model", "functionName": "onFolderClicked", "line": 498}, {"codegenSuccessfull": false, "column": 54, "durationMicroseconds": 200, "errorMessage": "Cannot access value for name folderIcons", "functionName": "onMenuTriggered", "line": 509}, {"codegenSuccessfull": true, "column": 47, "durationMicroseconds": 63, "errorMessage": "", "functionName": "fill", "line": 487}, {"codegenSuccessfull": false, "column": 52, "durationMicroseconds": 2065, "errorMessage": "Cannot access value for name model", "functionName": "mimeData", "line": 491}, {"codegenSuccessfull": false, "column": 41, "durationMicroseconds": 28, "errorMessage": "Cannot access value for name listviewPage", "functionName": "target", "line": 517}, {"codegenSuccessfull": true, "column": 33, "durationMicroseconds": 89, "errorMessage": "", "functionName": "onCurrentIndexChanged", "line": 518}, {"codegenSuccessfull": false, "column": 41, "durationMicroseconds": 28, "errorMessage": "Cannot access value for name dropArea", "functionName": "target", "line": 523}, {"codegenSuccessfull": true, "column": 33, "durationMicroseconds": 86, "errorMessage": "", "functionName": "onDropped", "line": 524}, {"codegenSuccessfull": true, "column": 46, "durationMicroseconds": 82, "errorMessage": "", "functionName": "onTriggered", "line": 531}, {"codegenSuccessfull": true, "column": 46, "durationMicroseconds": 33, "errorMessage": "", "functionName": "onTriggered", "line": 531}, {"codegenSuccessfull": true, "column": 41, "durationMicroseconds": 56, "errorMessage": "", "functionName": "target", "line": 536}, {"codegenSuccessfull": true, "column": 33, "durationMicroseconds": 289, "errorMessage": "", "functionName": "onCurrentFrameChanged", "line": 537}, {"codegenSuccessfull": true, "column": 52, "durationMicroseconds": 93, "errorMessage": "", "functionName": "onCompleted", "line": 546}, {"codegenSuccessfull": true, "column": 54, "durationMicroseconds": 22, "errorMessage": "", "functionName": "onDestruction", "line": 549}, {"codegenSuccessfull": false, "column": 44, "durationMicroseconds": 124, "errorMessage": "Cannot generate efficient code for call to untyped JavaScript function", "functionName": "onCompleted", "line": 554}, {"codegenSuccessfull": false, "column": 28, "durationMicroseconds": 89, "errorMessage": "Cannot access value for name SearchFilterProxyModel", "functionName": "model", "line": 561}, {"codegenSuccessfull": false, "column": 37, "durationMicroseconds": 177, "errorMessage": "Cannot access value for name iconName", "functionName": "iconSource", "line": 563}, {"codegenSuccessfull": false, "column": 32, "durationMicroseconds": 28, "errorMessage": "Cannot access value for name searchResultGridViewContainer", "functionName": "width", "line": 564}, {"codegenSuccessfull": false, "column": 33, "durationMicroseconds": 27, "errorMessage": "Cannot access value for name searchResultGridViewContainer", "functionName": "height", "line": 565}, {"codegenSuccessfull": false, "column": 40, "durationMicroseconds": 186, "errorMessage": "Cannot access value for name desktopId", "functionName": "onItemClicked", "line": 567}, {"codegenSuccessfull": false, "column": 42, "durationMicroseconds": 29, "errorMessage": "Cannot access value for name model", "functionName": "onMenuTriggered", "line": 570}, {"codegenSuccessfull": true, "column": 30, "durationMicroseconds": 133, "errorMessage": "", "functionName": "visible", "line": 580}, {"codegenSuccessfull": true, "column": 39, "durationMicroseconds": 96, "errorMessage": "", "functionName": "activeFocusOnTab", "line": 581}, {"codegenSuccessfull": true, "column": 28, "durationMicroseconds": 67, "errorMessage": "", "functionName": "model", "line": 591}, {"codegenSuccessfull": true, "column": 35, "durationMicroseconds": 77, "errorMessage": "", "functionName": "fill", "line": 579}, {"codegenSuccessfull": false, "column": 34, "durationMicroseconds": 50, "errorMessage": "Cannot load property model from (component in /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/FullscreenFrame.qml)::parent with type QQuickItem.", "functionName": "visible", "line": 595}, {"codegenSuccessfull": false, "column": 33, "durationMicroseconds": 41, "errorMessage": "Cannot load property model from (component in /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/FullscreenFrame.qml)::parent with type QQuickItem.", "functionName": "active", "line": 596}, {"codegenSuccessfull": true, "column": 32, "durationMicroseconds": 389, "errorMessage": "", "functionName": "implicitWidth", "line": 606}, {"codegenSuccessfull": true, "column": 26, "durationMicroseconds": 142, "errorMessage": "", "functionName": "opacity", "line": 607}, {"codegenSuccessfull": true, "column": 39, "durationMicroseconds": 121, "errorMessage": "", "functionName": "placeholderTextColor", "line": 617}, {"codegenSuccessfull": false, "column": 32, "durationMicroseconds": 559, "errorMessage": "Cannot access value for name SearchFilterProxyModel", "functionName": "onTextChanged", "line": 629}, {"codegenSuccessfull": true, "column": 35, "durationMicroseconds": 82, "errorMessage": "", "functionName": "alignment", "line": 605}, {"codegenSuccessfull": false, "column": 34, "durationMicroseconds": 214, "errorMessage": "Cannot generate efficient code for internal conversion with incompatible or ambiguous types: QVariant -> Dtk::Quick::DColor stored as QVariant", "functionName": "crystal", "line": 611}, {"codegenSuccessfull": false, "column": 34, "durationMicroseconds": 229, "errorMessage": "Cannot generate efficient code for internal conversion with incompatible or ambiguous types: QVariant -> Dtk::Quick::DColor stored as QVariant", "functionName": "crystal", "line": 614}, {"codegenSuccessfull": false, "column": 37, "durationMicroseconds": 49, "errorMessage": "Cannot load property iconPalette from Dtk::Quick::DQuickControlColorSelector.", "functionName": "windowText", "line": 618}, {"codegenSuccessfull": true, "column": 35, "durationMicroseconds": 258, "errorMessage": "", "functionName": "up", "line": 620}, {"codegenSuccessfull": true, "column": 37, "durationMicroseconds": 71, "errorMessage": "", "functionName": "down", "line": 621}, {"codegenSuccessfull": false, "column": 39, "durationMicroseconds": 130, "errorMessage": "Cannot load property itemClicked from GridViewContainer::currentItem with type QQuickItem.", "functionName": "onReturnPressed", "line": 622}, {"codegenSuccessfull": true, "column": 17, "durationMicroseconds": 76, "errorMessage": "", "functionName": "cs", "line": 644}, {"codegenSuccessfull": true, "column": 29, "durationMicroseconds": 251, "errorMessage": "", "functionName": "centerPosition", "line": 645}, {"codegenSuccessfull": false, "column": 47, "durationMicroseconds": 82, "errorMessage": "Cannot load property rightPadding from (component in /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/FullscreenFrame.qml)::parent with type QQuickItem.", "functionName": "endPoint", "line": 649}, {"codegenSuccessfull": true, "column": 38, "durationMicroseconds": 70, "errorMessage": "", "functionName": "type", "line": 658}, {"codegenSuccessfull": true, "column": 31, "durationMicroseconds": 89, "errorMessage": "", "functionName": "from", "line": 666}, {"codegenSuccessfull": true, "column": 29, "durationMicroseconds": 113, "errorMessage": "", "functionName": "to", "line": 667}, {"codegenSuccessfull": true, "column": 38, "durationMicroseconds": 66, "errorMessage": "", "functionName": "type", "line": 665}, {"codegenSuccessfull": true, "column": 31, "durationMicroseconds": 90, "errorMessage": "", "functionName": "from", "line": 673}, {"codegenSuccessfull": true, "column": 29, "durationMicroseconds": 113, "errorMessage": "", "functionName": "to", "line": 674}, {"codegenSuccessfull": true, "column": 38, "durationMicroseconds": 65, "errorMessage": "", "functionName": "type", "line": 672}, {"codegenSuccessfull": true, "column": 38, "durationMicroseconds": 69, "errorMessage": "", "functionName": "type", "line": 684}, {"codegenSuccessfull": true, "column": 29, "durationMicroseconds": 89, "errorMessage": "", "functionName": "to", "line": 692}, {"codegenSuccessfull": true, "column": 31, "durationMicroseconds": 116, "errorMessage": "", "functionName": "from", "line": 693}, {"codegenSuccessfull": true, "column": 38, "durationMicroseconds": 65, "errorMessage": "", "functionName": "type", "line": 691}, {"codegenSuccessfull": true, "column": 29, "durationMicroseconds": 87, "errorMessage": "", "functionName": "to", "line": 699}, {"codegenSuccessfull": true, "column": 31, "durationMicroseconds": 112, "errorMessage": "", "functionName": "from", "line": 700}, {"codegenSuccessfull": true, "column": 38, "durationMicroseconds": 65, "errorMessage": "", "functionName": "type", "line": 698}, {"codegenSuccessfull": true, "column": 25, "durationMicroseconds": 135, "errorMessage": "", "functionName": "forwardTo", "line": 706}, {"codegenSuccessfull": true, "column": 25, "durationMicroseconds": 832, "errorMessage": "", "functionName": "onPressed", "line": 707}, {"codegenSuccessfull": true, "column": 25, "durationMicroseconds": 46, "errorMessage": "", "functionName": "onPressed", "line": 707}, {"codegenSuccessfull": true, "column": 31, "durationMicroseconds": 205, "errorMessage": "", "functionName": "onEscapePressed", "line": 722}, {"codegenSuccessfull": true, "column": 21, "durationMicroseconds": 54, "errorMessage": "", "functionName": "target", "line": 729}, {"codegenSuccessfull": false, "column": 13, "durationMicroseconds": 459, "errorMessage": "Cannot generate efficient code for call to untyped JavaScript function", "functionName": "onVisibleChanged", "line": 730}], "filepath": "/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/FullscreenFrame.qml"}], "moduleId": "org.deepin.launchpad(launchpadcommon)"}]