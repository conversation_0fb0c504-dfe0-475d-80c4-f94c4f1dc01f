[{"moduleFiles": [{"entries": [{"codegenSuccessfull": false, "column": 5, "durationMicroseconds": 0, "errorMessage": "Functions without type annotations won't be compiled", "functionName": "dropOnItem", "line": 53}, {"codegenSuccessfull": false, "column": 5, "durationMicroseconds": 0, "errorMessage": "Functions without type annotations won't be compiled", "functionName": "dropOnPage", "line": 58}, {"codegenSuccessfull": true, "column": 22, "durationMicroseconds": 506, "errorMessage": "", "functionName": "onInputReceived", "line": 742}, {"codegenSuccessfull": true, "column": 22, "durationMicroseconds": 47, "errorMessage": "", "functionName": "onInputReceived", "line": 742}, {"codegenSuccessfull": false, "column": 19, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type  for binding on fill.", "functionName": "fill", "line": 19}, {"codegenSuccessfull": false, "column": 21, "durationMicroseconds": 220, "errorMessage": "Cannot generate efficient code for internal conversion with incompatible or ambiguous types: QVariant -> Dtk::Quick::DColor stored as QVariant", "functionName": "common", "line": 24}, {"codegenSuccessfull": false, "column": 22, "durationMicroseconds": 216, "errorMessage": "Cannot generate efficient code for internal conversion with incompatible or ambiguous types: QVariant -> Dtk::Quick::DColor stored as QVariant", "functionName": "crystal", "line": 25}, {"codegenSuccessfull": false, "column": 21, "durationMicroseconds": 213, "errorMessage": "Cannot generate efficient code for internal conversion with incompatible or ambiguous types: QVariant -> Dtk::Quick::DColor stored as QVariant", "functionName": "common", "line": 28}, {"codegenSuccessfull": false, "column": 22, "durationMicroseconds": 207, "errorMessage": "Cannot generate efficient code for internal conversion with incompatible or ambiguous types: QVariant -> Dtk::Quick::DColor stored as QVariant", "functionName": "crystal", "line": 29}, {"codegenSuccessfull": false, "column": 18, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type  for binding on visible.", "functionName": "visible", "line": 40}, {"codegenSuccessfull": false, "column": 31, "durationMicroseconds": 50, "errorMessage": "Cannot find name text", "functionName": "onActiveChanged", "line": 43}, {"codegenSuccessfull": true, "column": 50, "durationMicroseconds": 360, "errorMessage": "", "functionName": "isHorizontalDock", "line": 78}, {"codegenSuccessfull": true, "column": 44, "durationMicroseconds": 337, "errorMessage": "", "functionName": "dockSpacing", "line": 79}, {"codegenSuccessfull": true, "column": 22, "durationMicroseconds": 229, "errorMessage": "", "functionName": "leftPadding", "line": 81}, {"codegenSuccessfull": true, "column": 23, "durationMicroseconds": 242, "errorMessage": "", "functionName": "rightPadding", "line": 82}, {"codegenSuccessfull": true, "column": 21, "durationMicroseconds": 293, "errorMessage": "", "functionName": "topPadding", "line": 83}, {"codegenSuccessfull": true, "column": 24, "durationMicroseconds": 285, "errorMessage": "", "functionName": "bottomPadding", "line": 84}, {"codegenSuccessfull": false, "column": 37, "durationMicroseconds": 41, "errorMessage": "Cannot access value for name appTextColor", "functionName": "textColor", "line": 86}, {"codegenSuccessfull": false, "column": 9, "durationMicroseconds": 303, "errorMessage": "Cannot access value for name ItemArrangementProxyModel", "functionName": "tryToRemoveEmptyPage", "line": 89}, {"codegenSuccessfull": true, "column": 23, "durationMicroseconds": 54, "errorMessage": "", "functionName": "fill", "line": 67}, {"codegenSuccessfull": true, "column": 22, "durationMicroseconds": 79, "errorMessage": "", "functionName": "context", "line": 72}, {"codegenSuccessfull": true, "column": 24, "durationMicroseconds": 176, "errorMessage": "", "functionName": "sequences", "line": 73}, {"codegenSuccessfull": true, "column": 26, "durationMicroseconds": 82, "errorMessage": "", "functionName": "onActivated", "line": 74}, {"codegenSuccessfull": true, "column": 37, "durationMicroseconds": 87, "errorMessage": "", "functionName": "onActivatedAmbiguously", "line": 75}, {"codegenSuccessfull": false, "column": 29, "durationMicroseconds": 34, "errorMessage": "Cannot load property textColor from Dtk::Quick::DQuickControlColorSelector.", "functionName": "windowText", "line": 87}, {"codegenSuccessfull": true, "column": 55, "durationMicroseconds": 151, "errorMessage": "", "functionName": "horizontalPadding", "line": 97}, {"codegenSuccessfull": true, "column": 13, "durationMicroseconds": 889, "errorMessage": "", "functionName": "checkDragMove", "line": 102}, {"codegenSuccessfull": true, "column": 19, "durationMicroseconds": 99, "errorMessage": "", "functionName": "keys", "line": 116}, {"codegenSuccessfull": true, "column": 24, "durationMicroseconds": 199, "errorMessage": "", "functionName": "onEntered", "line": 117}, {"codegenSuccessfull": true, "column": 32, "durationMicroseconds": 63, "errorMessage": "", "functionName": "onPositionChanged", "line": 122}, {"codegenSuccessfull": false, "column": 25, "durationMicroseconds": 181, "errorMessage": "method dropOnPage cannot be resolved.", "functionName": "onDropped", "line": 125}, {"codegenSuccessfull": true, "column": 25, "durationMicroseconds": 44, "errorMessage": "", "functionName": "onDropped", "line": 125}, {"codegenSuccessfull": true, "column": 23, "durationMicroseconds": 53, "errorMessage": "", "functionName": "onExited", "line": 136}, {"codegenSuccessfull": true, "column": 34, "durationMicroseconds": 241, "errorMessage": "", "functionName": "onPageIntentChanged", "line": 139}, {"codegenSuccessfull": true, "column": 27, "durationMicroseconds": 51, "errorMessage": "", "functionName": "fill", "line": 98}, {"codegenSuccessfull": false, "column": 30, "durationMicroseconds": 47, "errorMessage": "Cannot load property pageIntent from (component in /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/FullscreenFrame.qml)::parent with type QObject.", "functionName": "onTriggered", "line": 150}, {"codegenSuccessfull": false, "column": 25, "durationMicroseconds": 19, "errorMessage": "Cannot retrieve a non-object type by ID: dndItem", "functionName": "target", "line": 174}, {"codegenSuccessfull": true, "column": 17, "durationMicroseconds": 234, "errorMessage": "", "functionName": "onDragEnded", "line": 175}, {"codegenSuccessfull": false, "column": 21, "durationMicroseconds": 64, "errorMessage": "cannot convert from conversion to QJSPrimitiveValue to QUrl", "functionName": "source", "line": 191}, {"codegenSuccessfull": true, "column": 25, "durationMicroseconds": 385, "errorMessage": "", "functionName": "sourceSize", "line": 192}, {"codegenSuccessfull": false, "column": 24, "durationMicroseconds": 716, "errorMessage": "Cannot generate efficient code for internal conversion with incompatible or ambiguous types: conversion to QVariant -> QColor stored as QVariant", "functionName": "color", "line": 196}, {"codegenSuccessfull": true, "column": 31, "durationMicroseconds": 52, "errorMessage": "", "functionName": "fill", "line": 195}, {"codegenSuccessfull": true, "column": 32, "durationMicroseconds": 209, "errorMessage": "", "functionName": "onClicked", "line": 204}, {"codegenSuccessfull": false, "column": 30, "durationMicroseconds": 548, "errorMessage": "method decrementPageIndex cannot be resolved.", "functionName": "onWheel", "line": 210}, {"codegenSuccessfull": true, "column": 30, "durationMicroseconds": 46, "errorMessage": "", "functionName": "onWheel", "line": 210}, {"codegenSuccessfull": true, "column": 35, "durationMicroseconds": 51, "errorMessage": "", "functionName": "fill", "line": 202}, {"codegenSuccessfull": true, "column": 37, "durationMicroseconds": 79, "errorMessage": "", "functionName": "implicitHeight", "line": 249}, {"codegenSuccessfull": true, "column": 30, "durationMicroseconds": 138, "errorMessage": "", "functionName": "opacity", "line": 250}, {"codegenSuccessfull": true, "column": 36, "durationMicroseconds": 155, "errorMessage": "", "functionName": "onClicked", "line": 265}, {"codegenSuccessfull": true, "column": 40, "durationMicroseconds": 107, "errorMessage": "", "functionName": "right", "line": 256}, {"codegenSuccessfull": true, "column": 47, "durationMicroseconds": 68, "errorMessage": "", "functionName": "family", "line": 257}, {"codegenSuccessfull": true, "column": 42, "durationMicroseconds": 50, "errorMessage": "", "functionName": "visible", "line": 259}, {"codegenSuccessfull": false, "column": 37, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type  for binding on button.", "functionName": "button", "line": 263}, {"codegenSuccessfull": true, "column": 34, "durationMicroseconds": 95, "errorMessage": "", "functionName": "visible", "line": 276}, {"codegenSuccessfull": true, "column": 32, "durationMicroseconds": 168, "errorMessage": "", "functionName": "count", "line": 277}, {"codegenSuccessfull": true, "column": 39, "durationMicroseconds": 158, "errorMessage": "", "functionName": "currentIndex", "line": 278}, {"codegenSuccessfull": true, "column": 51, "durationMicroseconds": 108, "errorMessage": "", "functionName": "horizontalCenter", "line": 274}, {"codegenSuccessfull": true, "column": 49, "durationMicroseconds": 102, "errorMessage": "", "functionName": "verticalCenter", "line": 275}, {"codegenSuccessfull": true, "column": 37, "durationMicroseconds": 108, "errorMessage": "", "functionName": "radius", "line": 285}, {"codegenSuccessfull": false, "column": 36, "durationMicroseconds": 52, "errorMessage": "Cannot access value for name index", "functionName": "color", "line": 286}, {"codegenSuccessfull": false, "column": 41, "durationMicroseconds": 43, "errorMessage": "Cannot load property radius from (component in /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/FullscreenFrame.qml)::parent with type QQuickItem.", "functionName": "radius", "line": 289}, {"codegenSuccessfull": false, "column": 40, "durationMicroseconds": 241, "errorMessage": "Cannot generate efficient code for internal conversion with incompatible or ambiguous types: QVariant -> QColor stored as QVariant", "functionName": "color", "line": 291}, {"codegenSuccessfull": true, "column": 47, "durationMicroseconds": 51, "errorMessage": "", "functionName": "fill", "line": 288}, {"codegenSuccessfull": false, "column": 34, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type  for binding on sourceModel.", "functionName": "sourceModel", "line": 305}, {"codegenSuccessfull": true, "column": 31, "durationMicroseconds": 80, "errorMessage": "", "functionName": "snapMode", "line": 312}, {"codegenSuccessfull": true, "column": 34, "durationMicroseconds": 70, "errorMessage": "", "functionName": "orientation", "line": 313}, {"codegenSuccessfull": true, "column": 41, "durationMicroseconds": 71, "errorMessage": "", "functionName": "highlightRangeMode", "line": 314}, {"codegenSuccessfull": true, "column": 30, "durationMicroseconds": 136, "errorMessage": "", "functionName": "visible", "line": 321}, {"codegenSuccessfull": true, "column": 35, "durationMicroseconds": 69, "errorMessage": "", "functionName": "currentIndex", "line": 323}, {"codegenSuccessfull": false, "column": 21, "durationMicroseconds": 0, "errorMessage": "Functions without type annotations won't be compiled", "functionName": "setCurrentIndex", "line": 324}, {"codegenSuccessfull": false, "column": 28, "durationMicroseconds": 19, "errorMessage": "Cannot retrieve a non-object type by ID: itemPageModel", "functionName": "model", "line": 330}, {"codegenSuccessfull": true, "column": 35, "durationMicroseconds": 61, "errorMessage": "", "functionName": "fill", "line": 311}, {"codegenSuccessfull": false, "column": 32, "durationMicroseconds": 31, "errorMessage": "Cannot access value for name listviewPage", "functionName": "width", "line": 334}, {"codegenSuccessfull": false, "column": 33, "durationMicroseconds": 22, "errorMessage": "Cannot access value for name listviewPage", "functionName": "height", "line": 335}, {"codegenSuccessfull": false, "column": 49, "durationMicroseconds": 16, "errorMessage": "Cannot access value for name index", "functionName": "viewIndex", "line": 337}, {"codegenSuccessfull": false, "column": 39, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type  for binding on sortRole.", "functionName": "sortRole", "line": 347}, {"codegenSuccessfull": false, "column": 46, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type  for binding on sourceModel.", "functionName": "sourceModel", "line": 343}, {"codegenSuccessfull": false, "column": 41, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type  for binding on pageId.", "functionName": "pageId", "line": 344}, {"codegenSuccessfull": false, "column": 52, "durationMicroseconds": 28, "errorMessage": "Cannot retrieve a non-object type by ID: proxyModel", "functionName": "onCompleted", "line": 348}, {"codegenSuccessfull": true, "column": 46, "durationMicroseconds": 193, "errorMessage": "", "functionName": "acceptedButtons", "line": 355}, {"codegenSuccessfull": true, "column": 40, "durationMicroseconds": 374, "errorMessage": "", "functionName": "onClicked", "line": 356}, {"codegenSuccessfull": true, "column": 40, "durationMicroseconds": 44, "errorMessage": "", "functionName": "onClicked", "line": 356}, {"codegenSuccessfull": true, "column": 43, "durationMicroseconds": 51, "errorMessage": "", "functionName": "fill", "line": 354}, {"codegenSuccessfull": false, "column": 36, "durationMicroseconds": 20, "errorMessage": "Cannot retrieve a non-object type by ID: proxyModel", "functionName": "model", "line": 373}, {"codegenSuccessfull": false, "column": 29, "durationMicroseconds": 66, "errorMessage": "Cannot access value for name listviewPage", "functionName": "checkPageSwitchState", "line": 378}, {"codegenSuccessfull": false, "column": 38, "durationMicroseconds": 36, "errorMessage": "Cannot access value for name folderGridViewPopup", "functionName": "opacity", "line": 414}, {"codegenSuccessfull": false, "column": 55, "durationMicroseconds": 35, "errorMessage": "Cannot access value for name listviewPage", "functionName": "activeGridViewFocusOnTab", "line": 418}, {"codegenSuccessfull": true, "column": 43, "durationMicroseconds": 53, "errorMessage": "", "functionName": "fill", "line": 369}, {"codegenSuccessfull": false, "column": 49, "durationMicroseconds": 88, "errorMessage": "Cannot access value for name itemPageModel", "functionName": "onLeftPressed", "line": 396}, {"codegenSuccessfull": true, "column": 49, "durationMicroseconds": 50, "errorMessage": "", "functionName": "onLeftPressed", "line": 396}, {"codegenSuccessfull": false, "column": 50, "durationMicroseconds": 61, "errorMessage": "Cannot access value for name itemPageModel", "functionName": "onRightPressed", "line": 405}, {"codegenSuccessfull": true, "column": 50, "durationMicroseconds": 42, "errorMessage": "", "functionName": "onRightPressed", "line": 405}, {"codegenSuccessfull": true, "column": 79, "durationMicroseconds": 71, "errorMessage": "", "functionName": "type", "line": 416}, {"codegenSuccessfull": true, "column": 50, "durationMicroseconds": 69, "errorMessage": "", "functionName": "type", "line": 425}, {"codegenSuccessfull": false, "column": 42, "durationMicroseconds": 48, "errorMessage": "Cannot access value for name folderGridViewPopup", "functionName": "visible", "line": 431}, {"codegenSuccessfull": false, "column": 40, "durationMicroseconds": 26, "errorMessage": "Cannot access value for name gridViewContainer", "functionName": "width", "line": 432}, {"codegenSuccessfull": false, "column": 41, "durationMicroseconds": 24, "errorMessage": "Cannot access value for name gridViewContainer", "functionName": "height", "line": 433}, {"codegenSuccessfull": false, "column": 44, "durationMicroseconds": 33, "errorMessage": "Cannot access value for name folderGridViewPopup", "functionName": "onEntered", "line": 434}, {"codegenSuccessfull": true, "column": 44, "durationMicroseconds": 44, "errorMessage": "", "functionName": "onEntered", "line": 434}, {"codegenSuccessfull": true, "column": 43, "durationMicroseconds": 175, "errorMessage": "", "functionName": "onExited", "line": 441}, {"codegenSuccessfull": false, "column": 44, "durationMicroseconds": 202, "errorMessage": "Cannot access value for name model", "functionName": "onDropped", "line": 445}, {"codegenSuccessfull": true, "column": 44, "durationMicroseconds": 44, "errorMessage": "", "functionName": "onDropped", "line": 445}, {"codegenSuccessfull": true, "column": 49, "durationMicroseconds": 121, "errorMessage": "", "functionName": "forwardTo", "line": 429}, {"codegenSuccessfull": false, "column": 50, "durationMicroseconds": 74, "errorMessage": "Cannot access value for name width", "functionName": "onTriggered", "line": 464}, {"codegenSuccessfull": true, "column": 50, "durationMicroseconds": 34, "errorMessage": "", "functionName": "onTriggered", "line": 464}, {"codegenSuccessfull": false, "column": 49, "durationMicroseconds": 40, "errorMessage": "Cannot access value for name folderGridViewPopup", "functionName": "dndEnabled", "line": 488}, {"codegenSuccessfull": false, "column": 46, "durationMicroseconds": 31, "errorMessage": "Cannot access value for name dndItem", "functionName": "visible", "line": 490}, {"codegenSuccessfull": false, "column": 49, "durationMicroseconds": 192, "errorMessage": "Cannot access value for name iconName", "functionName": "iconSource", "line": 491}, {"codegenSuccessfull": false, "column": 44, "durationMicroseconds": 181, "errorMessage": "Cannot access value for name folderIcons", "functionName": "icons", "line": 492}, {"codegenSuccessfull": false, "column": 52, "durationMicroseconds": 174, "errorMessage": "Cannot access value for name desktopId", "functionName": "onItemClicked", "line": 493}, {"codegenSuccessfull": false, "column": 54, "durationMicroseconds": 66, "errorMessage": "Cannot access value for name model", "functionName": "onFolderClicked", "line": 496}, {"codegenSuccessfull": false, "column": 54, "durationMicroseconds": 207, "errorMessage": "Cannot access value for name folderIcons", "functionName": "onMenuTriggered", "line": 507}, {"codegenSuccessfull": true, "column": 47, "durationMicroseconds": 55, "errorMessage": "", "functionName": "fill", "line": 485}, {"codegenSuccessfull": false, "column": 52, "durationMicroseconds": 2019, "errorMessage": "Cannot access value for name model", "functionName": "mimeData", "line": 489}, {"codegenSuccessfull": false, "column": 41, "durationMicroseconds": 28, "errorMessage": "Cannot access value for name listviewPage", "functionName": "target", "line": 515}, {"codegenSuccessfull": true, "column": 33, "durationMicroseconds": 87, "errorMessage": "", "functionName": "onCurrentIndexChanged", "line": 516}, {"codegenSuccessfull": false, "column": 41, "durationMicroseconds": 27, "errorMessage": "Cannot access value for name dropArea", "functionName": "target", "line": 521}, {"codegenSuccessfull": true, "column": 33, "durationMicroseconds": 83, "errorMessage": "", "functionName": "onDropped", "line": 522}, {"codegenSuccessfull": true, "column": 46, "durationMicroseconds": 87, "errorMessage": "", "functionName": "onTriggered", "line": 529}, {"codegenSuccessfull": true, "column": 46, "durationMicroseconds": 33, "errorMessage": "", "functionName": "onTriggered", "line": 529}, {"codegenSuccessfull": true, "column": 41, "durationMicroseconds": 53, "errorMessage": "", "functionName": "target", "line": 534}, {"codegenSuccessfull": true, "column": 33, "durationMicroseconds": 282, "errorMessage": "", "functionName": "onCurrentFrameChanged", "line": 535}, {"codegenSuccessfull": true, "column": 52, "durationMicroseconds": 91, "errorMessage": "", "functionName": "onCompleted", "line": 544}, {"codegenSuccessfull": true, "column": 54, "durationMicroseconds": 22, "errorMessage": "", "functionName": "onDestruction", "line": 547}, {"codegenSuccessfull": false, "column": 44, "durationMicroseconds": 127, "errorMessage": "Cannot generate efficient code for call to untyped JavaScript function", "functionName": "onCompleted", "line": 552}, {"codegenSuccessfull": false, "column": 28, "durationMicroseconds": 92, "errorMessage": "Cannot access value for name SearchFilterProxyModel", "functionName": "model", "line": 559}, {"codegenSuccessfull": false, "column": 37, "durationMicroseconds": 180, "errorMessage": "Cannot access value for name iconName", "functionName": "iconSource", "line": 561}, {"codegenSuccessfull": false, "column": 32, "durationMicroseconds": 28, "errorMessage": "Cannot access value for name searchResultGridViewContainer", "functionName": "width", "line": 562}, {"codegenSuccessfull": false, "column": 33, "durationMicroseconds": 24, "errorMessage": "Cannot access value for name searchResultGridViewContainer", "functionName": "height", "line": 563}, {"codegenSuccessfull": false, "column": 40, "durationMicroseconds": 164, "errorMessage": "Cannot access value for name desktopId", "functionName": "onItemClicked", "line": 565}, {"codegenSuccessfull": false, "column": 42, "durationMicroseconds": 27, "errorMessage": "Cannot access value for name model", "functionName": "onMenuTriggered", "line": 568}, {"codegenSuccessfull": true, "column": 30, "durationMicroseconds": 131, "errorMessage": "", "functionName": "visible", "line": 578}, {"codegenSuccessfull": true, "column": 39, "durationMicroseconds": 194, "errorMessage": "", "functionName": "activeFocusOnTab", "line": 579}, {"codegenSuccessfull": true, "column": 28, "durationMicroseconds": 66, "errorMessage": "", "functionName": "model", "line": 589}, {"codegenSuccessfull": true, "column": 35, "durationMicroseconds": 49, "errorMessage": "", "functionName": "fill", "line": 577}, {"codegenSuccessfull": false, "column": 34, "durationMicroseconds": 46, "errorMessage": "Cannot load property model from (component in /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/FullscreenFrame.qml)::parent with type QQuickItem.", "functionName": "visible", "line": 593}, {"codegenSuccessfull": false, "column": 33, "durationMicroseconds": 38, "errorMessage": "Cannot load property model from (component in /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/FullscreenFrame.qml)::parent with type QQuickItem.", "functionName": "active", "line": 594}, {"codegenSuccessfull": true, "column": 32, "durationMicroseconds": 370, "errorMessage": "", "functionName": "implicitWidth", "line": 604}, {"codegenSuccessfull": true, "column": 26, "durationMicroseconds": 134, "errorMessage": "", "functionName": "opacity", "line": 605}, {"codegenSuccessfull": true, "column": 39, "durationMicroseconds": 111, "errorMessage": "", "functionName": "placeholderTextColor", "line": 615}, {"codegenSuccessfull": false, "column": 32, "durationMicroseconds": 473, "errorMessage": "Cannot access value for name SearchFilterProxyModel", "functionName": "onTextChanged", "line": 627}, {"codegenSuccessfull": true, "column": 35, "durationMicroseconds": 79, "errorMessage": "", "functionName": "alignment", "line": 603}, {"codegenSuccessfull": false, "column": 34, "durationMicroseconds": 209, "errorMessage": "Cannot generate efficient code for internal conversion with incompatible or ambiguous types: QVariant -> Dtk::Quick::DColor stored as QVariant", "functionName": "crystal", "line": 609}, {"codegenSuccessfull": false, "column": 34, "durationMicroseconds": 220, "errorMessage": "Cannot generate efficient code for internal conversion with incompatible or ambiguous types: QVariant -> Dtk::Quick::DColor stored as QVariant", "functionName": "crystal", "line": 612}, {"codegenSuccessfull": false, "column": 37, "durationMicroseconds": 33, "errorMessage": "Cannot load property iconPalette from Dtk::Quick::DQuickControlColorSelector.", "functionName": "windowText", "line": 616}, {"codegenSuccessfull": true, "column": 35, "durationMicroseconds": 255, "errorMessage": "", "functionName": "up", "line": 618}, {"codegenSuccessfull": true, "column": 37, "durationMicroseconds": 69, "errorMessage": "", "functionName": "down", "line": 619}, {"codegenSuccessfull": false, "column": 39, "durationMicroseconds": 127, "errorMessage": "Cannot load property itemClicked from GridViewContainer::currentItem with type QQuickItem.", "functionName": "onReturnPressed", "line": 620}, {"codegenSuccessfull": true, "column": 17, "durationMicroseconds": 74, "errorMessage": "", "functionName": "cs", "line": 642}, {"codegenSuccessfull": true, "column": 29, "durationMicroseconds": 246, "errorMessage": "", "functionName": "centerPosition", "line": 643}, {"codegenSuccessfull": false, "column": 47, "durationMicroseconds": 80, "errorMessage": "Cannot load property rightPadding from (component in /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/FullscreenFrame.qml)::parent with type QQuickItem.", "functionName": "endPoint", "line": 647}, {"codegenSuccessfull": true, "column": 38, "durationMicroseconds": 68, "errorMessage": "", "functionName": "type", "line": 656}, {"codegenSuccessfull": true, "column": 31, "durationMicroseconds": 82, "errorMessage": "", "functionName": "from", "line": 664}, {"codegenSuccessfull": true, "column": 29, "durationMicroseconds": 108, "errorMessage": "", "functionName": "to", "line": 665}, {"codegenSuccessfull": true, "column": 38, "durationMicroseconds": 65, "errorMessage": "", "functionName": "type", "line": 663}, {"codegenSuccessfull": true, "column": 31, "durationMicroseconds": 85, "errorMessage": "", "functionName": "from", "line": 671}, {"codegenSuccessfull": true, "column": 29, "durationMicroseconds": 111, "errorMessage": "", "functionName": "to", "line": 672}, {"codegenSuccessfull": true, "column": 38, "durationMicroseconds": 64, "errorMessage": "", "functionName": "type", "line": 670}, {"codegenSuccessfull": true, "column": 38, "durationMicroseconds": 67, "errorMessage": "", "functionName": "type", "line": 682}, {"codegenSuccessfull": true, "column": 29, "durationMicroseconds": 86, "errorMessage": "", "functionName": "to", "line": 690}, {"codegenSuccessfull": true, "column": 31, "durationMicroseconds": 113, "errorMessage": "", "functionName": "from", "line": 691}, {"codegenSuccessfull": true, "column": 38, "durationMicroseconds": 64, "errorMessage": "", "functionName": "type", "line": 689}, {"codegenSuccessfull": true, "column": 29, "durationMicroseconds": 88, "errorMessage": "", "functionName": "to", "line": 697}, {"codegenSuccessfull": true, "column": 31, "durationMicroseconds": 111, "errorMessage": "", "functionName": "from", "line": 698}, {"codegenSuccessfull": true, "column": 38, "durationMicroseconds": 64, "errorMessage": "", "functionName": "type", "line": 696}, {"codegenSuccessfull": true, "column": 25, "durationMicroseconds": 136, "errorMessage": "", "functionName": "forwardTo", "line": 704}, {"codegenSuccessfull": true, "column": 25, "durationMicroseconds": 815, "errorMessage": "", "functionName": "onPressed", "line": 705}, {"codegenSuccessfull": true, "column": 25, "durationMicroseconds": 50, "errorMessage": "", "functionName": "onPressed", "line": 705}, {"codegenSuccessfull": true, "column": 31, "durationMicroseconds": 198, "errorMessage": "", "functionName": "onEscapePressed", "line": 720}, {"codegenSuccessfull": true, "column": 21, "durationMicroseconds": 55, "errorMessage": "", "functionName": "target", "line": 727}, {"codegenSuccessfull": false, "column": 13, "durationMicroseconds": 446, "errorMessage": "Cannot generate efficient code for call to untyped JavaScript function", "functionName": "onVisibleChanged", "line": 728}], "filepath": "/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/FullscreenFrame.qml"}], "moduleId": "org.deepin.launchpad(launchpadcommon)"}]