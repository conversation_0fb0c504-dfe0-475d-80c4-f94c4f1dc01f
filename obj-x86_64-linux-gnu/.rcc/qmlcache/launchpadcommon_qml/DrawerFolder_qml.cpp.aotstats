[{"moduleFiles": [{"entries": [{"codegenSuccessfull": true, "column": 16, "durationMicroseconds": 276, "errorMessage": "", "functionName": "onClicked", "line": 14}, {"codegenSuccessfull": true, "column": 16, "durationMicroseconds": 81, "errorMessage": "", "functionName": "width", "line": 24}, {"codegenSuccessfull": true, "column": 17, "durationMicroseconds": 71, "errorMessage": "", "functionName": "height", "line": 25}, {"codegenSuccessfull": true, "column": 27, "durationMicroseconds": 49, "errorMessage": "", "functionName": "centerIn", "line": 22}, {"codegenSuccessfull": false, "column": 27, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type  for binding on fill.", "functionName": "fill", "line": 29}, {"codegenSuccessfull": false, "column": 29, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type QQuickItem for binding on sourceItem.", "functionName": "sourceItem", "line": 36}, {"codegenSuccessfull": false, "column": 25, "durationMicroseconds": 76, "errorMessage": "Cannot load property radius from  stored as QQuickControl.", "functionName": "radius", "line": 37}, {"codegenSuccessfull": false, "column": 31, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type  for binding on fill.", "functionName": "fill", "line": 34}, {"codegenSuccessfull": false, "column": 30, "durationMicroseconds": 41, "errorMessage": "Cannot load property dropShadowColor from Dtk::Quick::DQuickControlColorSelector.", "functionName": "shadowColor", "line": 45}, {"codegenSuccessfull": true, "column": 31, "durationMicroseconds": 74, "errorMessage": "", "functionName": "cornerRadius", "line": 47}, {"codegenSuccessfull": true, "column": 31, "durationMicroseconds": 62, "errorMessage": "", "functionName": "fill", "line": 42}, {"codegenSuccessfull": false, "column": 25, "durationMicroseconds": 69, "errorMessage": "Cannot load property radius from  stored as QQuickControl.", "functionName": "radius", "line": 55}, {"codegenSuccessfull": true, "column": 31, "durationMicroseconds": 50, "errorMessage": "", "functionName": "fill", "line": 54}], "filepath": "/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/DrawerFolder.qml"}], "moduleId": "org.deepin.launchpad(launchpadcommon)"}]