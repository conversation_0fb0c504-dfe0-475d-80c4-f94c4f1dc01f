[{"moduleFiles": [{"entries": [{"codegenSuccessfull": true, "column": 44, "durationMicroseconds": 225, "errorMessage": "", "functionName": "isWindowedMode", "line": 38}, {"codegenSuccessfull": false, "column": 5, "durationMicroseconds": 0, "errorMessage": "Functions without type annotations won't be compiled", "functionName": "setPreviousPageSwitch", "line": 40}, {"codegenSuccessfull": true, "column": 5, "durationMicroseconds": 126, "errorMessage": "", "functionName": "positionViewAtBeginning", "line": 47}, {"codegenSuccessfull": false, "column": 5, "durationMicroseconds": 0, "errorMessage": "Functions without type annotations won't be compiled", "functionName": "itemAt", "line": 51}, {"codegenSuccessfull": false, "column": 5, "durationMicroseconds": 0, "errorMessage": "Functions without type annotations won't be compiled", "functionName": "indexAt", "line": 56}, {"codegenSuccessfull": true, "column": 34, "durationMicroseconds": 1372, "errorMessage": "", "functionName": "cellHeight", "line": 66}, {"codegenSuccessfull": true, "column": 33, "durationMicroseconds": 73, "errorMessage": "", "functionName": "cellWidth", "line": 67}, {"codegenSuccessfull": true, "column": 23, "durationMicroseconds": 77, "errorMessage": "", "functionName": "fill", "line": 64}, {"codegenSuccessfull": true, "column": 20, "durationMicroseconds": 1086, "errorMessage": "", "functionName": "width", "line": 70}, {"codegenSuccessfull": true, "column": 21, "durationMicroseconds": 1217, "errorMessage": "", "functionName": "height", "line": 77}, {"codegenSuccessfull": true, "column": 31, "durationMicroseconds": 76, "errorMessage": "", "functionName": "centerIn", "line": 69}, {"codegenSuccessfull": true, "column": 35, "durationMicroseconds": 205, "errorMessage": "", "functionName": "activeFocusOnTab", "line": 96}, {"codegenSuccessfull": true, "column": 24, "durationMicroseconds": 145, "errorMessage": "", "functionName": "focus", "line": 97}, {"codegenSuccessfull": true, "column": 39, "durationMicroseconds": 1205, "errorMessage": "", "functionName": "onActiveFocusChanged", "line": 98}, {"codegenSuccessfull": true, "column": 29, "durationMicroseconds": 117, "errorMessage": "", "functionName": "cellHeight", "line": 109}, {"codegenSuccessfull": true, "column": 28, "durationMicroseconds": 109, "errorMessage": "", "functionName": "cellWidth", "line": 110}, {"codegenSuccessfull": true, "column": 28, "durationMicroseconds": 99, "errorMessage": "", "functionName": "displaced", "line": 134}, {"codegenSuccessfull": true, "column": 23, "durationMicroseconds": 96, "errorMessage": "", "functionName": "move", "line": 136}, {"codegenSuccessfull": true, "column": 32, "durationMicroseconds": 98, "errorMessage": "", "functionName": "moveDisplaced", "line": 137}, {"codegenSuccessfull": true, "column": 37, "durationMicroseconds": 127, "errorMessage": "", "functionName": "vertical", "line": 89}, {"codegenSuccessfull": true, "column": 31, "durationMicroseconds": 75, "errorMessage": "", "functionName": "fill", "line": 91}, {"codegenSuccessfull": false, "column": 33, "durationMicroseconds": 47, "errorMessage": "Cannot access value for name isWindowedMode", "functionName": "radius", "line": 118}, {"codegenSuccessfull": false, "column": 32, "durationMicroseconds": 66, "errorMessage": "Cannot use shadowable base type for further lookups: QQuickItem::palette with type QQuickPalette (stored as QVariant)", "functionName": "color", "line": 119}, {"codegenSuccessfull": false, "column": 34, "durationMicroseconds": 40, "errorMessage": "Cannot access value for name gridView", "functionName": "visible", "line": 120}, {"codegenSuccessfull": true, "column": 35, "durationMicroseconds": 79, "errorMessage": "", "functionName": "fill", "line": 115}, {"codegenSuccessfull": false, "column": 32, "durationMicroseconds": 311, "errorMessage": "Cannot generate efficient code for internal conversion with incompatible or ambiguous types: QVariant -> QColor stored as QVariant", "functionName": "color", "line": 128}, {"codegenSuccessfull": false, "column": 34, "durationMicroseconds": 41, "errorMessage": "Cannot access value for name alwaysShowHighlighted", "functionName": "visible", "line": 129}, {"codegenSuccessfull": true, "column": 35, "durationMicroseconds": 77, "errorMessage": "", "functionName": "fill", "line": 124}, {"codegenSuccessfull": false, "column": 22, "durationMicroseconds": 38, "errorMessage": "Cannot retrieve a non-object type by ID: placeholderLabel", "functionName": "visible", "line": 142}, {"codegenSuccessfull": true, "column": 31, "durationMicroseconds": 81, "errorMessage": "", "functionName": "centerIn", "line": 143}, {"codegenSuccessfull": false, "column": 30, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type  for binding on visible.", "functionName": "visible", "line": 149}, {"codegenSuccessfull": false, "column": 30, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type Dtk::Gui::DDciIconPalette for binding on palette.", "functionName": "palette", "line": 154}, {"codegenSuccessfull": false, "column": 28, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type Dtk::Gui::DGuiApplicationHelper::ColorType for binding on theme.", "functionName": "theme", "line": 155}, {"codegenSuccessfull": false, "column": 33, "durationMicroseconds": 49, "errorMessage": "Cannot access value for name width", "functionName": "height", "line": 152}, {"codegenSuccessfull": true, "column": 35, "durationMicroseconds": 113, "errorMessage": "", "functionName": "alignment", "line": 161}], "filepath": "/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/GridViewContainer.qml"}], "moduleId": "org.deepin.launchpad(launchpadcommon)"}]