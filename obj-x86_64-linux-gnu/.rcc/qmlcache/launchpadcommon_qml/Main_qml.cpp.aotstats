[{"moduleFiles": [{"entries": [{"codegenSuccessfull": false, "column": 5, "durationMicroseconds": 0, "errorMessage": "Functions without type annotations won't be compiled", "functionName": "getCategoryName", "line": 19}, {"codegenSuccessfull": false, "column": 5, "durationMicroseconds": 0, "errorMessage": "Functions without type annotations won't be compiled", "functionName": "launchApp", "line": 46}, {"codegenSuccessfull": false, "column": 5, "durationMicroseconds": 0, "errorMessage": "Functions without type annotations won't be compiled", "functionName": "showContextMenu", "line": 63}, {"codegenSuccessfull": false, "column": 5, "durationMicroseconds": 225, "errorMessage": "Cannot generate efficient code for call to untyped JavaScript function", "functionName": "closeContextMenu", "line": 97}, {"codegenSuccessfull": false, "column": 5, "durationMicroseconds": 0, "errorMessage": "Functions without type annotations won't be compiled", "functionName": "decrementPageIndex", "line": 104}, {"codegenSuccessfull": false, "column": 5, "durationMicroseconds": 0, "errorMessage": "Functions without type annotations won't be compiled", "functionName": "incrementPageIndex", "line": 114}, {"codegenSuccessfull": false, "column": 5, "durationMicroseconds": 0, "errorMessage": "Functions without type annotations won't be compiled", "functionName": "descaledRect", "line": 124}, {"codegenSuccessfull": false, "column": 5, "durationMicroseconds": 0, "errorMessage": "Functions without type annotations won't be compiled", "functionName": "descaledPos", "line": 128}, {"codegenSuccessfull": false, "column": 5, "durationMicroseconds": 76, "errorMessage": "Cannot access value for name DS", "functionName": "updateWindowVisibilityAndPosition", "line": 133}, {"codegenSuccessfull": true, "column": 47, "durationMicroseconds": 173, "errorMessage": "", "functionName": "windowedFrameSize", "line": 149}, {"codegenSuccessfull": true, "column": 33, "durationMicroseconds": 164, "errorMessage": "", "functionName": "windowedPos", "line": 152}, {"codegenSuccessfull": true, "column": 18, "durationMicroseconds": 253, "errorMessage": "", "functionName": "visible", "line": 157}, {"codegenSuccessfull": false, "column": 16, "durationMicroseconds": 47, "errorMessage": "Cannot access value for name windowedFrameSize", "functionName": "width", "line": 159}, {"codegenSuccessfull": false, "column": 17, "durationMicroseconds": 45, "errorMessage": "Cannot access value for name windowedFrameSize", "functionName": "height", "line": 160}, {"codegenSuccessfull": true, "column": 16, "durationMicroseconds": 274, "errorMessage": "", "functionName": "flags", "line": 203}, {"codegenSuccessfull": false, "column": 9, "durationMicroseconds": 0, "errorMessage": "Functions without type annotations won't be compiled", "functionName": "blendColorAlpha", "line": 208}, {"codegenSuccessfull": false, "column": 27, "durationMicroseconds": 307, "errorMessage": "method updateWindowVisibilityAndPosition cannot be resolved.", "functionName": "onVisibleChanged", "line": 244}, {"codegenSuccessfull": true, "column": 26, "durationMicroseconds": 489, "errorMessage": "", "functionName": "onActiveChanged", "line": 250}, {"codegenSuccessfull": true, "column": 31, "durationMicroseconds": 71, "errorMessage": "", "functionName": "family", "line": 161}, {"codegenSuccessfull": false, "column": 38, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type  for binding on topMargin.", "functionName": "<PERSON><PERSON><PERSON><PERSON>", "line": 162}, {"codegenSuccessfull": false, "column": 41, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type  for binding on rightMargin.", "functionName": "<PERSON><PERSON><PERSON><PERSON>", "line": 170}, {"codegenSuccessfull": false, "column": 41, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type  for binding on bottomMargin.", "functionName": "bottom<PERSON>argin", "line": 176}, {"codegenSuccessfull": false, "column": 39, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type  for binding on leftMargin.", "functionName": "leftMargin", "line": 182}, {"codegenSuccessfull": false, "column": 36, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type  for binding on anchors.", "functionName": "anchors", "line": 191}, {"codegenSuccessfull": false, "column": 50, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type  for binding on keyboardInteractivity.", "functionName": "keyboardInteractivity", "line": 202}, {"codegenSuccessfull": false, "column": 22, "durationMicroseconds": 29, "errorMessage": "Cannot access value for name parent", "functionName": "control", "line": 216}, {"codegenSuccessfull": false, "column": 25, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type QColor for binding on blendColor.", "functionName": "blendColor", "line": 218}, {"codegenSuccessfull": false, "column": 27, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type  for binding on fill.", "functionName": "fill", "line": 217}, {"codegenSuccessfull": false, "column": 21, "durationMicroseconds": 153, "errorMessage": "Cannot access value for name WindowManagerHelper", "functionName": "radius", "line": 231}, {"codegenSuccessfull": false, "column": 20, "durationMicroseconds": 39, "errorMessage": "Cannot load property themeType from Dtk::Quick::DQMLGlobalObject.", "functionName": "color", "line": 232}, {"codegenSuccessfull": true, "column": 27, "durationMicroseconds": 55, "errorMessage": "", "functionName": "fill", "line": 230}, {"codegenSuccessfull": true, "column": 26, "durationMicroseconds": 83, "errorMessage": "", "functionName": "enabled", "line": 237}, {"codegenSuccessfull": false, "column": 31, "durationMicroseconds": 20, "errorMessage": "Cannot load property platformTheme from Dtk::Quick::DQMLGlobalObject.", "functionName": "windowRadius", "line": 238}, {"codegenSuccessfull": false, "column": 30, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type QColor for binding on borderColor.", "functionName": "borderColor", "line": 242}, {"codegenSuccessfull": true, "column": 27, "durationMicroseconds": 56, "errorMessage": "", "functionName": "fill", "line": 265}, {"codegenSuccessfull": false, "column": 26, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type  for binding on visible.", "functionName": "visible", "line": 270}, {"codegenSuccessfull": false, "column": 32, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type  for binding on right.", "functionName": "right", "line": 273}, {"codegenSuccessfull": false, "column": 33, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type  for binding on bottom.", "functionName": "bottom", "line": 274}, {"codegenSuccessfull": false, "column": 28, "durationMicroseconds": 232, "errorMessage": "Cannot generate efficient code for internal conversion with incompatible or ambiguous types: QVariant -> QColor stored as QVariant", "functionName": "color", "line": 278}, {"codegenSuccessfull": true, "column": 32, "durationMicroseconds": 106, "errorMessage": "", "functionName": "onClicked", "line": 283}, {"codegenSuccessfull": true, "column": 35, "durationMicroseconds": 50, "errorMessage": "", "functionName": "fill", "line": 282}, {"codegenSuccessfull": true, "column": 18, "durationMicroseconds": 218, "errorMessage": "", "functionName": "visible", "line": 292}, {"codegenSuccessfull": true, "column": 16, "durationMicroseconds": 232, "errorMessage": "", "functionName": "color", "line": 294}, {"codegenSuccessfull": true, "column": 16, "durationMicroseconds": 270, "errorMessage": "", "functionName": "flags", "line": 303}, {"codegenSuccessfull": true, "column": 27, "durationMicroseconds": 114, "errorMessage": "", "functionName": "onVisibleChanged", "line": 316}, {"codegenSuccessfull": true, "column": 26, "durationMicroseconds": 489, "errorMessage": "", "functionName": "onActiveChanged", "line": 322}, {"codegenSuccessfull": false, "column": 36, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type  for binding on anchors.", "functionName": "anchors", "line": 296}, {"codegenSuccessfull": false, "column": 34, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type  for binding on layer.", "functionName": "layer", "line": 297}, {"codegenSuccessfull": false, "column": 50, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type  for binding on keyboardInteractivity.", "functionName": "keyboardInteractivity", "line": 298}, {"codegenSuccessfull": true, "column": 26, "durationMicroseconds": 85, "errorMessage": "", "functionName": "enabled", "line": 308}, {"codegenSuccessfull": false, "column": 28, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type Dtk::Gui::DGuiApplicationHelper::ColorType for binding on themeType.", "functionName": "themeType", "line": 313}, {"codegenSuccessfull": false, "column": 38, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type Dtk::Gui::DPlatformHandle::EffectType for binding on windowStartUpEffect.", "functionName": "windowStartUpEffect", "line": 314}, {"codegenSuccessfull": true, "column": 27, "durationMicroseconds": 51, "errorMessage": "", "functionName": "fill", "line": 336}, {"codegenSuccessfull": false, "column": 26, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type  for binding on visible.", "functionName": "visible", "line": 341}, {"codegenSuccessfull": false, "column": 32, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type  for binding on right.", "functionName": "right", "line": 344}, {"codegenSuccessfull": false, "column": 33, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type  for binding on bottom.", "functionName": "bottom", "line": 345}, {"codegenSuccessfull": false, "column": 28, "durationMicroseconds": 232, "errorMessage": "Cannot generate efficient code for internal conversion with incompatible or ambiguous types: QVariant -> QColor stored as QVariant", "functionName": "color", "line": 349}, {"codegenSuccessfull": true, "column": 32, "durationMicroseconds": 105, "errorMessage": "", "functionName": "onClicked", "line": 354}, {"codegenSuccessfull": true, "column": 35, "durationMicroseconds": 51, "errorMessage": "", "functionName": "fill", "line": 353}, {"codegenSuccessfull": true, "column": 17, "durationMicroseconds": 52, "errorMessage": "", "functionName": "target", "line": 361}, {"codegenSuccessfull": false, "column": 9, "durationMicroseconds": 62, "errorMessage": "method updateWindowVisibilityAndPosition cannot be resolved.", "functionName": "onDockGeometryChanged", "line": 362}, {"codegenSuccessfull": true, "column": 9, "durationMicroseconds": 47, "errorMessage": "", "functionName": "open", "line": 372}, {"codegenSuccessfull": true, "column": 21, "durationMicroseconds": 84, "errorMessage": "", "functionName": "active", "line": 377}, {"codegenSuccessfull": false, "column": 23, "durationMicroseconds": 145, "errorMessage": "Cannot access value for name DStyle", "functionName": "minimumWidth", "line": 391}, {"codegenSuccessfull": false, "column": 24, "durationMicroseconds": 109, "errorMessage": "Cannot access value for name DStyle", "functionName": "minimumHeight", "line": 392}, {"codegenSuccessfull": true, "column": 23, "durationMicroseconds": 50, "errorMessage": "", "functionName": "maximumWidth", "line": 393}, {"codegenSuccessfull": true, "column": 24, "durationMicroseconds": 45, "errorMessage": "", "functionName": "maximumHeight", "line": 394}, {"codegenSuccessfull": false, "column": 36, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type  for binding on anchors.", "functionName": "anchors", "line": 389}, {"codegenSuccessfull": false, "column": 23, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type  for binding on font.", "functionName": "font", "line": 399}, {"codegenSuccessfull": false, "column": 23, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type  for binding on text.", "functionName": "text", "line": 400}, {"codegenSuccessfull": false, "column": 27, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type  for binding on wrapMode.", "functionName": "wrapMode", "line": 401}, {"codegenSuccessfull": false, "column": 38, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type  for binding on horizontalAlignment.", "functionName": "horizontalAlignment", "line": 402}, {"codegenSuccessfull": true, "column": 35, "durationMicroseconds": 77, "errorMessage": "", "functionName": "alignment", "line": 404}, {"codegenSuccessfull": true, "column": 36, "durationMicroseconds": 113, "errorMessage": "", "functionName": "onClicked", "line": 413}, {"codegenSuccessfull": true, "column": 43, "durationMicroseconds": 52, "errorMessage": "", "functionName": "centerIn", "line": 416}, {"codegenSuccessfull": true, "column": 45, "durationMicroseconds": 79, "errorMessage": "", "functionName": "preferredHeight", "line": 419}, {"codegenSuccessfull": true, "column": 36, "durationMicroseconds": 238, "errorMessage": "", "functionName": "onClicked", "line": 427}, {"codegenSuccessfull": true, "column": 43, "durationMicroseconds": 54, "errorMessage": "", "functionName": "centerIn", "line": 431}, {"codegenSuccessfull": true, "column": 45, "durationMicroseconds": 75, "errorMessage": "", "functionName": "preferredHeight", "line": 434}], "filepath": "/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/Main.qml"}], "moduleId": "org.deepin.launchpad(launchpadcommon)"}]