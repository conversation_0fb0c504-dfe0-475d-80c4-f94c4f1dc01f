[{"moduleFiles": [{"entries": [{"codegenSuccessfull": false, "column": 5, "durationMicroseconds": 0, "errorMessage": "Functions without type annotations won't be compiled", "functionName": "generateDragMimeData", "line": 47}, {"codegenSuccessfull": false, "column": 21, "durationMicroseconds": 379, "errorMessage": "Cannot generate efficient code for internal conversion with incompatible or ambiguous types: QVariant -> Dtk::Quick::DColor stored as QVariant", "functionName": "common", "line": 30}, {"codegenSuccessfull": false, "column": 22, "durationMicroseconds": 316, "errorMessage": "Cannot generate efficient code for internal conversion with incompatible or ambiguous types: QVariant -> Dtk::Quick::DColor stored as QVariant", "functionName": "crystal", "line": 31}, {"codegenSuccessfull": false, "column": 21, "durationMicroseconds": 296, "errorMessage": "Cannot generate efficient code for internal conversion with incompatible or ambiguous types: QVariant -> Dtk::Quick::DColor stored as QVariant", "functionName": "common", "line": 34}, {"codegenSuccessfull": false, "column": 22, "durationMicroseconds": 291, "errorMessage": "Cannot generate efficient code for internal conversion with incompatible or ambiguous types: QVariant -> Dtk::Quick::DColor stored as QVariant", "functionName": "crystal", "line": 35}, {"codegenSuccessfull": false, "column": 21, "durationMicroseconds": 292, "errorMessage": "Cannot generate efficient code for internal conversion with incompatible or ambiguous types: QVariant -> Dtk::Quick::DColor stored as QVariant", "functionName": "common", "line": 38}, {"codegenSuccessfull": false, "column": 22, "durationMicroseconds": 281, "errorMessage": "Cannot generate efficient code for internal conversion with incompatible or ambiguous types: QVariant -> Dtk::Quick::DColor stored as QVariant", "functionName": "crystal", "line": 39}, {"codegenSuccessfull": false, "column": 21, "durationMicroseconds": 286, "errorMessage": "Cannot generate efficient code for internal conversion with incompatible or ambiguous types: QVariant -> Dtk::Quick::DColor stored as QVariant", "functionName": "common", "line": 42}, {"codegenSuccessfull": false, "column": 22, "durationMicroseconds": 289, "errorMessage": "Cannot generate efficient code for internal conversion with incompatible or ambiguous types: QVariant -> Dtk::Quick::DColor stored as QVariant", "functionName": "crystal", "line": 43}], "filepath": "/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/Helper.qml"}], "moduleId": "org.deepin.launchpad(launchpadcommon)"}]