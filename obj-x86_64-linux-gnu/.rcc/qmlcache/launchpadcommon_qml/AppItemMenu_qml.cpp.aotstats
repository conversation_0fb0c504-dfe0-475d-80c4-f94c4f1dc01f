[{"moduleFiles": [{"entries": [{"codegenSuccessfull": true, "column": 42, "durationMicroseconds": 139, "errorMessage": "", "functionName": "isFullscreen", "line": 22}, {"codegenSuccessfull": true, "column": 46, "durationMicroseconds": 328, "errorMessage": "", "functionName": "isHorizontalDock", "line": 23}, {"codegenSuccessfull": true, "column": 40, "durationMicroseconds": 325, "errorMessage": "", "functionName": "dockSpacing", "line": 24}, {"codegenSuccessfull": true, "column": 22, "durationMicroseconds": 56, "errorMessage": "", "functionName": "sourceComponent", "line": 173}, {"codegenSuccessfull": true, "column": 5, "durationMicroseconds": 41, "errorMessage": "", "functionName": "popup", "line": 175}, {"codegenSuccessfull": true, "column": 5, "durationMicroseconds": 37, "errorMessage": "", "functionName": "close", "line": 179}, {"codegenSuccessfull": false, "column": 22, "durationMicroseconds": 57, "errorMessage": "Type (component in /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/AppItemMenu.qml)::item with type QObject does not have a property popup for calling", "functionName": "onStatusChanged", "line": 183}, {"codegenSuccessfull": false, "column": 24, "durationMicroseconds": 38, "errorMessage": "Cannot access value for name isFullscreen", "functionName": "<PERSON><PERSON><PERSON><PERSON>", "line": 33}, {"codegenSuccessfull": false, "column": 27, "durationMicroseconds": 31, "errorMessage": "Cannot access value for name isFullscreen", "functionName": "bottom<PERSON>argin", "line": 34}, {"codegenSuccessfull": false, "column": 25, "durationMicroseconds": 29, "errorMessage": "Cannot access value for name isFullscreen", "functionName": "leftMargin", "line": 35}, {"codegenSuccessfull": false, "column": 26, "durationMicroseconds": 28, "errorMessage": "Cannot access value for name isFullscreen", "functionName": "<PERSON><PERSON><PERSON><PERSON>", "line": 36}, {"codegenSuccessfull": false, "column": 22, "durationMicroseconds": 15, "errorMessage": "Cannot load property palette from Dtk::Quick::DQMLGlobalObject.", "functionName": "palette", "line": 41}, {"codegenSuccessfull": false, "column": 23, "durationMicroseconds": 32, "errorMessage": "Cannot access value for name root", "functionName": "onClosed", "line": 156}, {"codegenSuccessfull": false, "column": 26, "durationMicroseconds": 26, "errorMessage": "Cannot access value for name root", "functionName": "enabled", "line": 45}, {"codegenSuccessfull": false, "column": 30, "durationMicroseconds": 24, "errorMessage": "Cannot access value for name root", "functionName": "onTriggered", "line": 46}, {"codegenSuccessfull": false, "column": 26, "durationMicroseconds": 24, "errorMessage": "Cannot access value for name isFavoriteItem", "functionName": "visible", "line": 54}, {"codegenSuccessfull": true, "column": 25, "durationMicroseconds": 127, "errorMessage": "", "functionName": "height", "line": 55}, {"codegenSuccessfull": false, "column": 30, "durationMicroseconds": 330, "errorMessage": "Cannot access value for name FavoritedProxyModel", "functionName": "onTriggered", "line": 57}, {"codegenSuccessfull": false, "column": 26, "durationMicroseconds": 25, "errorMessage": "Cannot access value for name hideMoveToTopMenu", "functionName": "visible", "line": 63}, {"codegenSuccessfull": true, "column": 25, "durationMicroseconds": 121, "errorMessage": "", "functionName": "height", "line": 64}, {"codegenSuccessfull": false, "column": 30, "durationMicroseconds": 376, "errorMessage": "Cannot access value for name ItemArrangementProxyModel", "functionName": "onTriggered", "line": 66}, {"codegenSuccessfull": false, "column": 26, "durationMicroseconds": 27, "errorMessage": "Cannot access value for name hideFavoriteMenu", "functionName": "visible", "line": 72}, {"codegenSuccessfull": true, "column": 25, "durationMicroseconds": 121, "errorMessage": "", "functionName": "height", "line": 74}, {"codegenSuccessfull": false, "column": 23, "durationMicroseconds": 318, "errorMessage": "Cannot access value for name FavoritedProxyModel", "functionName": "text", "line": 75}, {"codegenSuccessfull": false, "column": 30, "durationMicroseconds": 352, "errorMessage": "Cannot access value for name FavoritedProxyModel", "functionName": "onTriggered", "line": 76}, {"codegenSuccessfull": true, "column": 26, "durationMicroseconds": 168, "errorMessage": "", "functionName": "visible", "line": 85}, {"codegenSuccessfull": true, "column": 25, "durationMicroseconds": 117, "errorMessage": "", "functionName": "height", "line": 86}, {"codegenSuccessfull": false, "column": 26, "durationMicroseconds": 31, "errorMessage": "Cannot access value for name root", "functionName": "enabled", "line": 89}, {"codegenSuccessfull": false, "column": 23, "durationMicroseconds": 51, "errorMessage": "Cannot access value for name root", "functionName": "text", "line": 90}, {"codegenSuccessfull": false, "column": 30, "durationMicroseconds": 33, "errorMessage": "Cannot access value for name root", "functionName": "onTriggered", "line": 91}, {"codegenSuccessfull": false, "column": 26, "durationMicroseconds": 33, "errorMessage": "Cannot access value for name root", "functionName": "enabled", "line": 100}, {"codegenSuccessfull": false, "column": 23, "durationMicroseconds": 35, "errorMessage": "Cannot access value for name root", "functionName": "text", "line": 101}, {"codegenSuccessfull": false, "column": 30, "durationMicroseconds": 36, "errorMessage": "Cannot access value for name root", "functionName": "onTriggered", "line": 102}, {"codegenSuccessfull": false, "column": 26, "durationMicroseconds": 28, "errorMessage": "Cannot access value for name root", "functionName": "enabled", "line": 112}, {"codegenSuccessfull": false, "column": 23, "durationMicroseconds": 40, "errorMessage": "Cannot access value for name root", "functionName": "text", "line": 113}, {"codegenSuccessfull": false, "column": 30, "durationMicroseconds": 40, "errorMessage": "Cannot access value for name root", "functionName": "onTriggered", "line": 114}, {"codegenSuccessfull": true, "column": 25, "durationMicroseconds": 128, "errorMessage": "", "functionName": "height", "line": 122}, {"codegenSuccessfull": false, "column": 26, "durationMicroseconds": 28, "errorMessage": "Cannot access value for name hideDisplayScalingMenu", "functionName": "visible", "line": 125}, {"codegenSuccessfull": false, "column": 26, "durationMicroseconds": 29, "errorMessage": "Cannot access value for name root", "functionName": "enabled", "line": 126}, {"codegenSuccessfull": true, "column": 25, "durationMicroseconds": 134, "errorMessage": "", "functionName": "height", "line": 127}, {"codegenSuccessfull": false, "column": 26, "durationMicroseconds": 35, "errorMessage": "Cannot access value for name root", "functionName": "checked", "line": 129}, {"codegenSuccessfull": false, "column": 30, "durationMicroseconds": 39, "errorMessage": "Cannot access value for name root", "functionName": "onTriggered", "line": 131}, {"codegenSuccessfull": false, "column": 26, "durationMicroseconds": 30, "errorMessage": "Cannot access value for name root", "functionName": "enabled", "line": 136}, {"codegenSuccessfull": false, "column": 30, "durationMicroseconds": 171, "errorMessage": "Cannot access value for name root", "functionName": "onTriggered", "line": 138}, {"codegenSuccessfull": true, "column": 17, "durationMicroseconds": 55, "errorMessage": "", "functionName": "target", "line": 164}, {"codegenSuccessfull": false, "column": 9, "durationMicroseconds": 0, "errorMessage": "Functions without type annotations won't be compiled", "functionName": "onVisibleChanged", "line": 165}], "filepath": "/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/AppItemMenu.qml"}], "moduleId": "org.deepin.launchpad(launchpadcommon)"}]