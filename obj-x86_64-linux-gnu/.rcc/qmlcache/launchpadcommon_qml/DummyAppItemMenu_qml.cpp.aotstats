[{"moduleFiles": [{"entries": [{"codegenSuccessfull": true, "column": 42, "durationMicroseconds": 138, "errorMessage": "", "functionName": "isFullscreen", "line": 16}, {"codegenSuccessfull": true, "column": 46, "durationMicroseconds": 325, "errorMessage": "", "functionName": "isHorizontalDock", "line": 17}, {"codegenSuccessfull": true, "column": 40, "durationMicroseconds": 329, "errorMessage": "", "functionName": "dockSpacing", "line": 18}, {"codegenSuccessfull": true, "column": 22, "durationMicroseconds": 55, "errorMessage": "", "functionName": "sourceComponent", "line": 63}, {"codegenSuccessfull": true, "column": 5, "durationMicroseconds": 45, "errorMessage": "", "functionName": "popup", "line": 65}, {"codegenSuccessfull": true, "column": 5, "durationMicroseconds": 38, "errorMessage": "", "functionName": "close", "line": 69}, {"codegenSuccessfull": false, "column": 22, "durationMicroseconds": 58, "errorMessage": "Type (component in /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/DummyAppItemMenu.qml)::item with type QObject does not have a property popup for calling", "functionName": "onStatusChanged", "line": 73}, {"codegenSuccessfull": false, "column": 24, "durationMicroseconds": 30, "errorMessage": "Cannot access value for name isFullscreen", "functionName": "<PERSON><PERSON><PERSON><PERSON>", "line": 26}, {"codegenSuccessfull": false, "column": 27, "durationMicroseconds": 22, "errorMessage": "Cannot access value for name isFullscreen", "functionName": "bottom<PERSON>argin", "line": 27}, {"codegenSuccessfull": false, "column": 25, "durationMicroseconds": 21, "errorMessage": "Cannot access value for name isFullscreen", "functionName": "leftMargin", "line": 28}, {"codegenSuccessfull": false, "column": 26, "durationMicroseconds": 20, "errorMessage": "Cannot access value for name isFullscreen", "functionName": "<PERSON><PERSON><PERSON><PERSON>", "line": 29}, {"codegenSuccessfull": false, "column": 23, "durationMicroseconds": 24, "errorMessage": "Cannot access value for name root", "functionName": "onClosed", "line": 46}, {"codegenSuccessfull": false, "column": 30, "durationMicroseconds": 25, "errorMessage": "Cannot access value for name root", "functionName": "onTriggered", "line": 34}, {"codegenSuccessfull": false, "column": 30, "durationMicroseconds": 32, "errorMessage": "Cannot access value for name root", "functionName": "onTriggered", "line": 41}, {"codegenSuccessfull": true, "column": 17, "durationMicroseconds": 55, "errorMessage": "", "functionName": "target", "line": 54}, {"codegenSuccessfull": false, "column": 9, "durationMicroseconds": 0, "errorMessage": "Functions without type annotations won't be compiled", "functionName": "onVisibleChanged", "line": 55}], "filepath": "/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/DummyAppItemMenu.qml"}], "moduleId": "org.deepin.launchpad(launchpadcommon)"}]