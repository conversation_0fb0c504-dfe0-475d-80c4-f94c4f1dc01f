[{"moduleFiles": [{"entries": [{"codegenSuccessfull": false, "column": 34, "durationMicroseconds": 31, "errorMessage": "Cannot load property fontManager from Dtk::Quick::DQMLGlobalObject.", "functionName": "folderNameFont", "line": 20}, {"codegenSuccessfull": true, "column": 44, "durationMicroseconds": 142, "errorMessage": "", "functionName": "isWindowedMode", "line": 22}, {"codegenSuccessfull": true, "column": 18, "durationMicroseconds": 136, "errorMessage": "", "functionName": "closePolicy", "line": 27}, {"codegenSuccessfull": true, "column": 12, "durationMicroseconds": 159, "errorMessage": "", "functionName": "width", "line": 32}, {"codegenSuccessfull": true, "column": 13, "durationMicroseconds": 562, "errorMessage": "", "functionName": "height", "line": 36}, {"codegenSuccessfull": true, "column": 8, "durationMicroseconds": 192, "errorMessage": "", "functionName": "x", "line": 37}, {"codegenSuccessfull": true, "column": 8, "durationMicroseconds": 178, "errorMessage": "", "functionName": "y", "line": 38}, {"codegenSuccessfull": true, "column": 15, "durationMicroseconds": 95, "errorMessage": "", "functionName": "onClosed", "line": 40}, {"codegenSuccessfull": true, "column": 17, "durationMicroseconds": 88, "errorMessage": "", "functionName": "active", "line": 51}, {"codegenSuccessfull": true, "column": 23, "durationMicroseconds": 49, "errorMessage": "", "functionName": "fill", "line": 52}, {"codegenSuccessfull": false, "column": 28, "durationMicroseconds": 36, "errorMessage": "Cannot access value for name drop", "functionName": "onDropped", "line": 58}, {"codegenSuccessfull": true, "column": 31, "durationMicroseconds": 58, "errorMessage": "", "functionName": "fill", "line": 57}, {"codegenSuccessfull": false, "column": 26, "durationMicroseconds": 30, "errorMessage": "Cannot access value for name isWindowedMode", "functionName": "spacing", "line": 66}, {"codegenSuccessfull": false, "column": 43, "durationMicroseconds": 24, "errorMessage": "Cannot access value for name isWindowedMode", "functionName": "<PERSON><PERSON><PERSON><PERSON>", "line": 69}, {"codegenSuccessfull": true, "column": 31, "durationMicroseconds": 50, "errorMessage": "", "functionName": "fill", "line": 67}, {"codegenSuccessfull": false, "column": 30, "durationMicroseconds": 23, "errorMessage": "Cannot access value for name isWindowedMode", "functionName": "visible", "line": 72}, {"codegenSuccessfull": false, "column": 33, "durationMicroseconds": 242, "errorMessage": "Cannot generate efficient code for internal conversion with incompatible or ambiguous types: QVariant -> Dtk::Quick::DColor stored as QVariant", "functionName": "common", "line": 78}, {"codegenSuccessfull": false, "column": 34, "durationMicroseconds": 211, "errorMessage": "Cannot generate efficient code for internal conversion with incompatible or ambiguous types: QVariant -> Dtk::Quick::DColor stored as QVariant", "functionName": "crystal", "line": 79}, {"codegenSuccessfull": false, "column": 33, "durationMicroseconds": 220, "errorMessage": "Cannot generate efficient code for internal conversion with incompatible or ambiguous types: QVariant -> Dtk::Quick::DColor stored as QVariant", "functionName": "common", "line": 82}, {"codegenSuccessfull": false, "column": 34, "durationMicroseconds": 214, "errorMessage": "Cannot generate efficient code for internal conversion with incompatible or ambiguous types: QVariant -> Dtk::Quick::DColor stored as QVariant", "functionName": "crystal", "line": 83}, {"codegenSuccessfull": true, "column": 30, "durationMicroseconds": 73, "errorMessage": "", "functionName": "visible", "line": 92}, {"codegenSuccessfull": false, "column": 27, "durationMicroseconds": 32, "errorMessage": "Cannot access value for name folderNameFont", "functionName": "font", "line": 94}, {"codegenSuccessfull": true, "column": 42, "durationMicroseconds": 70, "errorMessage": "", "functionName": "horizontalAlignment", "line": 95}, {"codegenSuccessfull": false, "column": 27, "durationMicroseconds": 30, "errorMessage": "Cannot access value for name folderLoader", "functionName": "text", "line": 96}, {"codegenSuccessfull": false, "column": 28, "durationMicroseconds": 10, "errorMessage": "Cannot access value for name palette", "functionName": "color", "line": 97}, {"codegenSuccessfull": false, "column": 40, "durationMicroseconds": 75, "errorMessage": "Cannot access value for name folderLoader", "functionName": "onEditingFinished", "line": 100}, {"codegenSuccessfull": false, "column": 37, "durationMicroseconds": 10, "errorMessage": "Cannot access value for name palette", "functionName": "selectionColor", "line": 111}, {"codegenSuccessfull": false, "column": 40, "durationMicroseconds": 42, "errorMessage": "Cannot access value for name root", "functionName": "leftMargin", "line": 90}, {"codegenSuccessfull": false, "column": 41, "durationMicroseconds": 39, "errorMessage": "Cannot access value for name root", "functionName": "<PERSON><PERSON><PERSON><PERSON>", "line": 91}, {"codegenSuccessfull": false, "column": 27, "durationMicroseconds": 32, "errorMessage": "Cannot access value for name folderNameFont", "functionName": "font", "line": 119}, {"codegenSuccessfull": true, "column": 42, "durationMicroseconds": 75, "errorMessage": "", "functionName": "horizontalAlignment", "line": 120}, {"codegenSuccessfull": false, "column": 27, "durationMicroseconds": 37, "errorMessage": "Cannot access value for name folderLoader", "functionName": "text", "line": 121}, {"codegenSuccessfull": false, "column": 28, "durationMicroseconds": 43, "errorMessage": "Cannot load property titleTextColor from Dtk::Quick::DQuickControlColorSelector.", "functionName": "color", "line": 122}, {"codegenSuccessfull": true, "column": 30, "durationMicroseconds": 95, "errorMessage": "", "functionName": "visible", "line": 123}, {"codegenSuccessfull": true, "column": 28, "durationMicroseconds": 70, "errorMessage": "", "functionName": "elide", "line": 124}, {"codegenSuccessfull": false, "column": 40, "durationMicroseconds": 46, "errorMessage": "Cannot access value for name root", "functionName": "leftMargin", "line": 116}, {"codegenSuccessfull": false, "column": 41, "durationMicroseconds": 43, "errorMessage": "Cannot access value for name root", "functionName": "<PERSON><PERSON><PERSON><PERSON>", "line": 117}, {"codegenSuccessfull": true, "column": 38, "durationMicroseconds": 230, "errorMessage": "", "functionName": "visible", "line": 125}, {"codegenSuccessfull": true, "column": 35, "durationMicroseconds": 50, "errorMessage": "", "functionName": "text", "line": 128}, {"codegenSuccessfull": true, "column": 36, "durationMicroseconds": 241, "errorMessage": "", "functionName": "onClicked", "line": 135}, {"codegenSuccessfull": true, "column": 39, "durationMicroseconds": 49, "errorMessage": "", "functionName": "fill", "line": 133}, {"codegenSuccessfull": true, "column": 66, "durationMicroseconds": 141, "errorMessage": "", "functionName": "horizontalPadding", "line": 152}, {"codegenSuccessfull": false, "column": 25, "durationMicroseconds": 132, "errorMessage": "Cannot retrieve a non-object type by ID: folderPagesView", "functionName": "checkDragMove", "line": 156}, {"codegenSuccessfull": true, "column": 31, "durationMicroseconds": 97, "errorMessage": "", "functionName": "keys", "line": 170}, {"codegenSuccessfull": true, "column": 44, "durationMicroseconds": 62, "errorMessage": "", "functionName": "onPositionChanged", "line": 171}, {"codegenSuccessfull": false, "column": 37, "durationMicroseconds": 87, "errorMessage": "Cannot access value for name folderLoader", "functionName": "onDropped", "line": 174}, {"codegenSuccessfull": true, "column": 37, "durationMicroseconds": 43, "errorMessage": "", "functionName": "onDropped", "line": 174}, {"codegenSuccessfull": true, "column": 35, "durationMicroseconds": 74, "errorMessage": "", "functionName": "onExited", "line": 186}, {"codegenSuccessfull": true, "column": 46, "durationMicroseconds": 275, "errorMessage": "", "functionName": "onPageIntentChanged", "line": 190}, {"codegenSuccessfull": true, "column": 39, "durationMicroseconds": 49, "errorMessage": "", "functionName": "fill", "line": 153}, {"codegenSuccessfull": false, "column": 42, "durationMicroseconds": 41, "errorMessage": "Cannot load property pageIntent from (component in /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/FolderGridViewPopup.qml)::parent with type QObject.", "functionName": "onTriggered", "line": 201}, {"codegenSuccessfull": false, "column": 34, "durationMicroseconds": 226, "errorMessage": "Cannot retrieve a non-object type by ID: folderPagesView", "functionName": "onWheel", "line": 229}, {"codegenSuccessfull": true, "column": 34, "durationMicroseconds": 45, "errorMessage": "", "functionName": "onWheel", "line": 229}, {"codegenSuccessfull": true, "column": 39, "durationMicroseconds": 51, "errorMessage": "", "functionName": "fill", "line": 225}, {"codegenSuccessfull": false, "column": 31, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type  for binding on clip.", "functionName": "clip", "line": 248}, {"codegenSuccessfull": false, "column": 39, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type  for binding on currentIndex.", "functionName": "currentIndex", "line": 252}, {"codegenSuccessfull": false, "column": 39, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type  for binding on fill.", "functionName": "fill", "line": 250}, {"codegenSuccessfull": false, "column": 37, "durationMicroseconds": 56, "errorMessage": "Cannot access value for name ItemArrangementProxyModel", "functionName": "target", "line": 255}, {"codegenSuccessfull": false, "column": 29, "durationMicroseconds": 0, "errorMessage": "Functions without type annotations won't be compiled", "functionName": "onFolderPageCountChanged", "line": 256}, {"codegenSuccessfull": false, "column": 36, "durationMicroseconds": 189, "errorMessage": "Cannot access value for name ItemArrangementProxyModel", "functionName": "model", "line": 264}, {"codegenSuccessfull": false, "column": 41, "durationMicroseconds": 20, "errorMessage": "Cannot load property isCurrentItem from const QMetaObject.", "functionName": "active", "line": 267}, {"codegenSuccessfull": true, "column": 51, "durationMicroseconds": 52, "errorMessage": "", "functionName": "fill", "line": 272}, {"codegenSuccessfull": false, "column": 54, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type  for binding on sourceModel.", "functionName": "sourceModel", "line": 278}, {"codegenSuccessfull": false, "column": 49, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type  for binding on pageId.", "functionName": "pageId", "line": 279}, {"codegenSuccessfull": false, "column": 51, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type  for binding on folderId.", "functionName": "folderId", "line": 280}, {"codegenSuccessfull": false, "column": 54, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type  for binding on sourceModel.", "functionName": "sourceModel", "line": 285}, {"codegenSuccessfull": false, "column": 51, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type  for binding on sortRole.", "functionName": "sortRole", "line": 286}, {"codegenSuccessfull": false, "column": 64, "durationMicroseconds": 20, "errorMessage": "Cannot retrieve a non-object type by ID: sortProxyModel", "functionName": "onCompleted", "line": 287}, {"codegenSuccessfull": false, "column": 58, "durationMicroseconds": 31, "errorMessage": "Cannot access value for name isWindowedMode", "functionName": "sourceComponent", "line": 296}, {"codegenSuccessfull": true, "column": 55, "durationMicroseconds": 50, "errorMessage": "", "functionName": "fill", "line": 295}, {"codegenSuccessfull": true, "column": 62, "durationMicroseconds": 74, "errorMessage": "", "functionName": "type", "line": 303}, {"codegenSuccessfull": false, "column": 52, "durationMicroseconds": 38, "errorMessage": "Cannot access value for name sortProxyModel", "functionName": "model", "line": 316}, {"codegenSuccessfull": false, "column": 71, "durationMicroseconds": 32, "errorMessage": "Cannot access value for name folderGridViewLoader", "functionName": "activeGridViewFocusOnTab", "line": 321}, {"codegenSuccessfull": false, "column": 55, "durationMicroseconds": 47, "errorMessage": "Cannot load property itemMove from (component in /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/FolderGridViewPopup.qml)::parent with type QQuickItem.", "functionName": "itemMove", "line": 322}, {"codegenSuccessfull": true, "column": 59, "durationMicroseconds": 54, "errorMessage": "", "functionName": "fill", "line": 313}, {"codegenSuccessfull": false, "column": 56, "durationMicroseconds": 31, "errorMessage": "Cannot access value for name folderGridViewContainer", "functionName": "width", "line": 324}, {"codegenSuccessfull": false, "column": 57, "durationMicroseconds": 26, "errorMessage": "Cannot access value for name folderGridViewContainer", "functionName": "height", "line": 325}, {"codegenSuccessfull": false, "column": 52, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type  for binding on model.", "functionName": "model", "line": 337}, {"codegenSuccessfull": false, "column": 71, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type  for binding on activeGridViewFocusOnTab.", "functionName": "activeGridViewFocusOnTab", "line": 344}, {"codegenSuccessfull": false, "column": 55, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type  for binding on itemMove.", "functionName": "itemMove", "line": 345}, {"codegenSuccessfull": false, "column": 59, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type  for binding on fill.", "functionName": "fill", "line": 334}, {"codegenSuccessfull": false, "column": 56, "durationMicroseconds": 19, "errorMessage": "Cannot retrieve a non-object type by ID: folderGridViewContainer", "functionName": "width", "line": 347}, {"codegenSuccessfull": false, "column": 57, "durationMicroseconds": 14, "errorMessage": "Cannot retrieve a non-object type by ID: folderGridViewContainer", "functionName": "height", "line": 348}, {"codegenSuccessfull": true, "column": 52, "durationMicroseconds": 258, "errorMessage": "", "functionName": "onEntered", "line": 354}, {"codegenSuccessfull": true, "column": 52, "durationMicroseconds": 43, "errorMessage": "", "functionName": "onEntered", "line": 354}, {"codegenSuccessfull": false, "column": 60, "durationMicroseconds": 51, "errorMessage": "Cannot access value for name model", "functionName": "onPositionChanged", "line": 358}, {"codegenSuccessfull": true, "column": 60, "durationMicroseconds": 41, "errorMessage": "", "functionName": "onPositionChanged", "line": 358}, {"codegenSuccessfull": true, "column": 51, "durationMicroseconds": 146, "errorMessage": "", "functionName": "onExited", "line": 369}, {"codegenSuccessfull": false, "column": 52, "durationMicroseconds": 89, "errorMessage": "Cannot access value for name model", "functionName": "onDropped", "line": 373}, {"codegenSuccessfull": true, "column": 52, "durationMicroseconds": 43, "errorMessage": "", "functionName": "onDropped", "line": 373}, {"codegenSuccessfull": false, "column": 58, "durationMicroseconds": 60, "errorMessage": "Cannot load property width from (component in /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/FolderGridViewPopup.qml)::parent with type QObject.", "functionName": "onTriggered", "line": 396}, {"codegenSuccessfull": true, "column": 58, "durationMicroseconds": 32, "errorMessage": "", "functionName": "onTriggered", "line": 396}, {"codegenSuccessfull": true, "column": 57, "durationMicroseconds": 124, "errorMessage": "", "functionName": "forwardTo", "line": 412}, {"codegenSuccessfull": false, "column": 58, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type  for binding on displayFont.", "functionName": "displayFont", "line": 418}, {"codegenSuccessfull": false, "column": 54, "durationMicroseconds": 182, "errorMessage": "Cannot access value for name dndItem", "functionName": "visible", "line": 420}, {"codegenSuccessfull": false, "column": 57, "durationMicroseconds": 160, "errorMessage": "Cannot access value for name iconName", "functionName": "iconSource", "line": 421}, {"codegenSuccessfull": true, "column": 54, "durationMicroseconds": 111, "errorMessage": "", "functionName": "padding", "line": 423}, {"codegenSuccessfull": false, "column": 60, "durationMicroseconds": 189, "errorMessage": "Cannot access value for name desktopId", "functionName": "onItemClicked", "line": 424}, {"codegenSuccessfull": false, "column": 62, "durationMicroseconds": 45, "errorMessage": "Cannot access value for name model", "functionName": "onMenuTriggered", "line": 427}, {"codegenSuccessfull": true, "column": 59, "durationMicroseconds": 53, "errorMessage": "", "functionName": "fill", "line": 416}, {"codegenSuccessfull": false, "column": 60, "durationMicroseconds": 1987, "errorMessage": "Cannot access value for name model", "functionName": "mimeData", "line": 419}, {"codegenSuccessfull": true, "column": 39, "durationMicroseconds": 55, "errorMessage": "", "functionName": "fill", "line": 439}, {"codegenSuccessfull": true, "column": 38, "durationMicroseconds": 73, "errorMessage": "", "functionName": "enabled", "line": 442}, {"codegenSuccessfull": true, "column": 40, "durationMicroseconds": 101, "errorMessage": "", "functionName": "onClicked", "line": 443}, {"codegenSuccessfull": true, "column": 43, "durationMicroseconds": 49, "errorMessage": "", "functionName": "fill", "line": 441}, {"codegenSuccessfull": false, "column": 30, "durationMicroseconds": 23, "errorMessage": "Cannot retrieve a non-object type by ID: folderPagesView", "functionName": "visible", "line": 452}, {"codegenSuccessfull": false, "column": 37, "durationMicroseconds": 29, "errorMessage": "Cannot access value for name isWindowedMode", "functionName": "implicitHeight", "line": 454}, {"codegenSuccessfull": false, "column": 28, "durationMicroseconds": 30, "errorMessage": "Cannot retrieve a non-object type by ID: folderPagesView", "functionName": "count", "line": 455}, {"codegenSuccessfull": false, "column": 35, "durationMicroseconds": 15, "errorMessage": "Cannot retrieve a non-object type by ID: folderPagesView", "functionName": "currentIndex", "line": 456}, {"codegenSuccessfull": false, "column": 30, "durationMicroseconds": 32, "errorMessage": "Cannot access value for name isWindowedMode", "functionName": "spacing", "line": 458}, {"codegenSuccessfull": true, "column": 39, "durationMicroseconds": 75, "errorMessage": "", "functionName": "alignment", "line": 451}, {"codegenSuccessfull": false, "column": 40, "durationMicroseconds": 28, "errorMessage": "Cannot access value for name isWindowedMode", "functionName": "implicitWidth", "line": 461}, {"codegenSuccessfull": false, "column": 41, "durationMicroseconds": 22, "errorMessage": "Cannot access value for name isWindowedMode", "functionName": "implicitHeight", "line": 462}, {"codegenSuccessfull": true, "column": 33, "durationMicroseconds": 98, "errorMessage": "", "functionName": "radius", "line": 464}, {"codegenSuccessfull": false, "column": 32, "durationMicroseconds": 39, "errorMessage": "Cannot access value for name index", "functionName": "color", "line": 465}, {"codegenSuccessfull": false, "column": 37, "durationMicroseconds": 43, "errorMessage": "Cannot load property radius from (component in /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/FolderGridViewPopup.qml)::parent with type QQuickItem.", "functionName": "radius", "line": 469}, {"codegenSuccessfull": false, "column": 36, "durationMicroseconds": 224, "errorMessage": "Cannot generate efficient code for internal conversion with incompatible or ambiguous types: QVariant -> QColor stored as QVariant", "functionName": "color", "line": 471}, {"codegenSuccessfull": true, "column": 43, "durationMicroseconds": 51, "errorMessage": "", "functionName": "fill", "line": 468}, {"codegenSuccessfull": false, "column": 17, "durationMicroseconds": 26, "errorMessage": "Cannot access value for name isWindowedMode", "functionName": "radius", "line": 479}, {"codegenSuccessfull": false, "column": 29, "durationMicroseconds": 20, "errorMessage": "Cannot access value for name isWindowedMode", "functionName": "outsideBorderColor", "line": 486}, {"codegenSuccessfull": false, "column": 28, "durationMicroseconds": 19, "errorMessage": "Cannot access value for name isWindowedMode", "functionName": "insideBorderColor", "line": 487}, {"codegenSuccessfull": false, "column": 21, "durationMicroseconds": 221, "errorMessage": "Cannot generate efficient code for internal conversion with incompatible or ambiguous types: QVariant -> Dtk::Quick::DQuickControlColor stored as QVariant", "functionName": "normal", "line": 482}, {"codegenSuccessfull": false, "column": 25, "durationMicroseconds": 29, "errorMessage": "Cannot access value for name isWindowedMode", "functionName": "normalDark", "line": 483}, {"codegenSuccessfull": false, "column": 17, "durationMicroseconds": 221, "errorMessage": "Cannot generate efficient code for internal conversion with incompatible or ambiguous types: QVariant -> Dtk::Quick::DQuickControlColor stored as QVariant", "functionName": "normal", "line": 492}, {"codegenSuccessfull": false, "column": 21, "durationMicroseconds": 209, "errorMessage": "Cannot generate efficient code for internal conversion with incompatible or ambiguous types: QVariant -> Dtk::Quick::DQuickControlColor stored as QVariant", "functionName": "normalDark", "line": 493}], "filepath": "/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/FolderGridViewPopup.qml"}], "moduleId": "org.deepin.launchpad(launchpadcommon)"}]