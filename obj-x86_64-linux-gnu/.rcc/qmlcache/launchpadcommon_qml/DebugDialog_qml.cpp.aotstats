[{"moduleFiles": [{"entries": [{"codegenSuccessfull": true, "column": 18, "durationMicroseconds": 93, "errorMessage": "", "functionName": "checked", "line": 21}, {"codegenSuccessfull": true, "column": 27, "durationMicroseconds": 110, "errorMessage": "", "functionName": "onCheckedChanged", "line": 22}, {"codegenSuccessfull": true, "column": 18, "durationMicroseconds": 69, "errorMessage": "", "functionName": "checked", "line": 29}, {"codegenSuccessfull": true, "column": 27, "durationMicroseconds": 104, "errorMessage": "", "functionName": "onCheckedChanged", "line": 30}, {"codegenSuccessfull": true, "column": 18, "durationMicroseconds": 104, "errorMessage": "", "functionName": "checked", "line": 37}, {"codegenSuccessfull": true, "column": 27, "durationMicroseconds": 102, "errorMessage": "", "functionName": "onCheckedChanged", "line": 38}, {"codegenSuccessfull": true, "column": 18, "durationMicroseconds": 68, "errorMessage": "", "functionName": "checked", "line": 45}, {"codegenSuccessfull": true, "column": 27, "durationMicroseconds": 107, "errorMessage": "", "functionName": "onCheckedChanged", "line": 46}, {"codegenSuccessfull": true, "column": 20, "durationMicroseconds": 83, "errorMessage": "", "functionName": "onClicked", "line": 53}], "filepath": "/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/DebugDialog.qml"}], "moduleId": "org.deepin.launchpad(launchpadcommon)"}]