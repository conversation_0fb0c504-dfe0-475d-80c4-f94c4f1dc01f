[{"moduleFiles": [{"entries": [{"codegenSuccessfull": true, "column": 25, "durationMicroseconds": 69, "errorMessage": "", "functionName": "icons", "line": 21}, {"codegenSuccessfull": false, "column": 36, "durationMicroseconds": 187, "errorMessage": "Cannot access value for name display", "functionName": "text", "line": 27}, {"codegenSuccessfull": true, "column": 44, "durationMicroseconds": 157, "errorMessage": "", "functionName": "isWindowedMode", "line": 31}, {"codegenSuccessfull": false, "column": 22, "durationMicroseconds": 28, "errorMessage": "Cannot retrieve a non-object type by ID: iconItemLabel", "functionName": "name", "line": 34}, {"codegenSuccessfull": true, "column": 20, "durationMicroseconds": 76, "errorMessage": "", "functionName": "dragType", "line": 40}, {"codegenSuccessfull": true, "column": 15, "durationMicroseconds": 76, "errorMessage": "", "functionName": "when", "line": 44}, {"codegenSuccessfull": true, "column": 21, "durationMicroseconds": 81, "errorMessage": "", "functionName": "target", "line": 48}, {"codegenSuccessfull": false, "column": 16, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type  for binding on x. You may want use ID-based grouped properties here.", "functionName": "x", "line": 49}, {"codegenSuccessfull": false, "column": 16, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type  for binding on y. You may want use ID-based grouped properties here.", "functionName": "y", "line": 50}, {"codegenSuccessfull": true, "column": 22, "durationMicroseconds": 80, "errorMessage": "", "functionName": "focusPolicy", "line": 55}, {"codegenSuccessfull": true, "column": 31, "durationMicroseconds": 75, "errorMessage": "", "functionName": "family", "line": 57}, {"codegenSuccessfull": true, "column": 27, "durationMicroseconds": 50, "errorMessage": "", "functionName": "fill", "line": 60}, {"codegenSuccessfull": true, "column": 24, "durationMicroseconds": 71, "errorMessage": "", "functionName": "width", "line": 64}, {"codegenSuccessfull": false, "column": 25, "durationMicroseconds": 29, "errorMessage": "Cannot access value for name isWindowedMode", "functionName": "height", "line": 65}, {"codegenSuccessfull": true, "column": 24, "durationMicroseconds": 124, "errorMessage": "", "functionName": "width", "line": 69}, {"codegenSuccessfull": true, "column": 25, "durationMicroseconds": 46, "errorMessage": "", "functionName": "height", "line": 70}, {"codegenSuccessfull": true, "column": 43, "durationMicroseconds": 110, "errorMessage": "", "functionName": "horizontalCenter", "line": 71}, {"codegenSuccessfull": true, "column": 38, "durationMicroseconds": 235, "errorMessage": "", "functionName": "sourceComponent", "line": 76}, {"codegenSuccessfull": true, "column": 35, "durationMicroseconds": 49, "errorMessage": "", "functionName": "fill", "line": 75}, {"codegenSuccessfull": true, "column": 33, "durationMicroseconds": 65, "errorMessage": "", "functionName": "target", "line": 79}, {"codegenSuccessfull": true, "column": 42, "durationMicroseconds": 90, "errorMessage": "", "functionName": "acceptedButtons", "line": 80}, {"codegenSuccessfull": true, "column": 34, "durationMicroseconds": 68, "errorMessage": "", "functionName": "enabled", "line": 81}, {"codegenSuccessfull": false, "column": 42, "durationMicroseconds": 89, "errorMessage": "Cannot access value for name dndItem", "functionName": "onActiveChanged", "line": 83}, {"codegenSuccessfull": false, "column": 30, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type  for binding on visible.", "functionName": "visible", "line": 111}, {"codegenSuccessfull": true, "column": 33, "durationMicroseconds": 231, "errorMessage": "", "functionName": "sourceSize", "line": 112}, {"codegenSuccessfull": false, "column": 30, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type Dtk::Gui::DDciIconPalette for binding on palette.", "functionName": "palette", "line": 113}, {"codegenSuccessfull": false, "column": 28, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type Dtk::Gui::DGuiApplicationHelper::ColorType for binding on theme.", "functionName": "theme", "line": 114}, {"codegenSuccessfull": false, "column": 35, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type  for binding on left.", "functionName": "left", "line": 107}, {"codegenSuccessfull": false, "column": 37, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type  for binding on bottom.", "functionName": "bottom", "line": 108}, {"codegenSuccessfull": true, "column": 39, "durationMicroseconds": 53, "errorMessage": "", "functionName": "fill", "line": 121}, {"codegenSuccessfull": true, "column": 43, "durationMicroseconds": 50, "errorMessage": "", "functionName": "fill", "line": 127}, {"codegenSuccessfull": false, "column": 40, "durationMicroseconds": 30, "errorMessage": "Cannot access value for name icons", "functionName": "model", "line": 135}, {"codegenSuccessfull": false, "column": 43, "durationMicroseconds": 44, "errorMessage": "Cannot access value for name modelData", "functionName": "name", "line": 145}, {"codegenSuccessfull": false, "column": 49, "durationMicroseconds": 45, "errorMessage": "Cannot access value for name root", "functionName": "sourceSize", "line": 146}, {"codegenSuccessfull": false, "column": 44, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type  for binding on scale.", "functionName": "scale", "line": 147}, {"codegenSuccessfull": false, "column": 46, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type Dtk::Gui::DDciIconPalette for binding on palette.", "functionName": "palette", "line": 148}, {"codegenSuccessfull": false, "column": 44, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type Dtk::Gui::DGuiApplicationHelper::ColorType for binding on theme.", "functionName": "theme", "line": 149}, {"codegenSuccessfull": true, "column": 55, "durationMicroseconds": 144, "errorMessage": "", "functionName": "alignment", "line": 140}, {"codegenSuccessfull": false, "column": 59, "durationMicroseconds": 54, "errorMessage": "Cannot access value for name parent", "functionName": "maximumHeight", "line": 143}, {"codegenSuccessfull": false, "column": 40, "durationMicroseconds": 29, "errorMessage": "Cannot access value for name icons", "functionName": "model", "line": 154}, {"codegenSuccessfull": true, "column": 44, "durationMicroseconds": 124, "errorMessage": "", "functionName": "width", "line": 161}, {"codegenSuccessfull": true, "column": 45, "durationMicroseconds": 125, "errorMessage": "", "functionName": "height", "line": 162}, {"codegenSuccessfull": true, "column": 55, "durationMicroseconds": 142, "errorMessage": "", "functionName": "alignment", "line": 159}, {"codegenSuccessfull": false, "column": 31, "durationMicroseconds": 27, "errorMessage": "Cannot access value for name iconSource", "functionName": "name", "line": 175}, {"codegenSuccessfull": false, "column": 37, "durationMicroseconds": 35, "errorMessage": "Cannot access value for name root", "functionName": "sourceSize", "line": 176}, {"codegenSuccessfull": false, "column": 32, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type  for binding on scale.", "functionName": "scale", "line": 177}, {"codegenSuccessfull": false, "column": 34, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type Dtk::Gui::DDciIconPalette for binding on palette.", "functionName": "palette", "line": 178}, {"codegenSuccessfull": false, "column": 32, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type Dtk::Gui::DGuiApplicationHelper::ColorType for binding on theme.", "functionName": "theme", "line": 179}, {"codegenSuccessfull": false, "column": 39, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type  for binding on fill.", "functionName": "fill", "line": 174}, {"codegenSuccessfull": false, "column": 25, "durationMicroseconds": 26, "errorMessage": "Cannot access value for name isWindowedMode", "functionName": "height", "line": 187}, {"codegenSuccessfull": false, "column": 42, "durationMicroseconds": 33, "errorMessage": "Cannot access value for name font", "functionName": "singleRow", "line": 191}, {"codegenSuccessfull": false, "column": 49, "durationMicroseconds": 35, "errorMessage": "Cannot access value for name model", "functionName": "isNewlyInstalled", "line": 192}, {"codegenSuccessfull": false, "column": 23, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type  for binding on text.", "functionName": "text", "line": 194}, {"codegenSuccessfull": false, "column": 29, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type  for binding on textFormat.", "functionName": "textFormat", "line": 195}, {"codegenSuccessfull": false, "column": 24, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type  for binding on width.", "functionName": "width", "line": 196}, {"codegenSuccessfull": false, "column": 38, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type  for binding on horizontalAlignment.", "functionName": "horizontalAlignment", "line": 199}, {"codegenSuccessfull": false, "column": 27, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type  for binding on wrapMode.", "functionName": "wrapMode", "line": 200}, {"codegenSuccessfull": false, "column": 24, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type  for binding on elide.", "functionName": "elide", "line": 201}, {"codegenSuccessfull": false, "column": 35, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type  for binding on maximumLineCount.", "functionName": "maximumLineCount", "line": 202}, {"codegenSuccessfull": true, "column": 34, "durationMicroseconds": 105, "errorMessage": "", "functionName": "acceptedButtons", "line": 206}, {"codegenSuccessfull": true, "column": 32, "durationMicroseconds": 70, "errorMessage": "", "functionName": "gesturePolicy", "line": 207}, {"codegenSuccessfull": true, "column": 27, "durationMicroseconds": 136, "errorMessage": "", "functionName": "onTapped", "line": 208}, {"codegenSuccessfull": true, "column": 34, "durationMicroseconds": 93, "errorMessage": "", "functionName": "acceptedButtons", "line": 214}, {"codegenSuccessfull": true, "column": 32, "durationMicroseconds": 69, "errorMessage": "", "functionName": "gesturePolicy", "line": 215}, {"codegenSuccessfull": false, "column": 35, "durationMicroseconds": 149, "errorMessage": "method mapToItem cannot be resolved.", "functionName": "onPressedChanged", "line": 216}, {"codegenSuccessfull": false, "column": 27, "durationMicroseconds": 72, "errorMessage": "Cannot access value for name model", "functionName": "onTapped", "line": 221}, {"codegenSuccessfull": true, "column": 23, "durationMicroseconds": 76, "errorMessage": "", "functionName": "text", "line": 230}, {"codegenSuccessfull": false, "column": 26, "durationMicroseconds": 30, "errorMessage": "Cannot retrieve a non-object type by ID: iconItemLabel", "functionName": "visible", "line": 232}, {"codegenSuccessfull": false, "column": 21, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type  for binding on radius.", "functionName": "radius", "line": 234}, {"codegenSuccessfull": false, "column": 21, "durationMicroseconds": 0, "errorMessage": "Cannot resolve property type  for binding on button.", "functionName": "button", "line": 235}, {"codegenSuccessfull": false, "column": 26, "durationMicroseconds": 189, "errorMessage": "Cannot access value for name model", "functionName": "onSpacePressed", "line": 240}, {"codegenSuccessfull": false, "column": 27, "durationMicroseconds": 146, "errorMessage": "Cannot access value for name model", "functionName": "onReturnPressed", "line": 248}], "filepath": "/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/IconItemDelegate.qml"}], "moduleId": "org.deepin.launchpad(launchpadcommon)"}]