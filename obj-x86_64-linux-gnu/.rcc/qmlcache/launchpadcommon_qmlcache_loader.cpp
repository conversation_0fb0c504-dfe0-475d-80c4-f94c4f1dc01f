#include <QtQml/qqmlprivate.h>
#include <QtCore/qdir.h>
#include <QtCore/qurl.h>
#include <QtCore/qhash.h>
#include <QtCore/qstring.h>

namespace QmlCacheGeneratedCode {
namespace _qt_qml_org_deepin_launchpad_Helper_qml { 
    extern const unsigned char qmlData[];
    extern const QQmlPrivate::AOTCompiledFunction aotBuiltFunctions[];
    const QQmlPrivate::CachedQmlUnit unit = {
        reinterpret_cast<const QV4::CompiledData::Unit*>(&qmlData), &aotBuiltFunctions[0], nullptr
    };
}
namespace _qt_qml_org_deepin_launchpad_Main_qml { 
    extern const unsigned char qmlData[];
    extern const QQmlPrivate::AOTCompiledFunction aotBuiltFunctions[];
    const QQmlPrivate::CachedQmlUnit unit = {
        reinterpret_cast<const QV4::CompiledData::Unit*>(&qmlData), &aotBuiltFunctions[0], nullptr
    };
}
namespace _qt_qml_org_deepin_launchpad_FullscreenFrame_qml { 
    extern const unsigned char qmlData[];
    extern const QQmlPrivate::AOTCompiledFunction aotBuiltFunctions[];
    const QQmlPrivate::CachedQmlUnit unit = {
        reinterpret_cast<const QV4::CompiledData::Unit*>(&qmlData), &aotBuiltFunctions[0], nullptr
    };
}
namespace _qt_qml_org_deepin_launchpad_AppItemMenu_qml { 
    extern const unsigned char qmlData[];
    extern const QQmlPrivate::AOTCompiledFunction aotBuiltFunctions[];
    const QQmlPrivate::CachedQmlUnit unit = {
        reinterpret_cast<const QV4::CompiledData::Unit*>(&qmlData), &aotBuiltFunctions[0], nullptr
    };
}
namespace _qt_qml_org_deepin_launchpad_DummyAppItemMenu_qml { 
    extern const unsigned char qmlData[];
    extern const QQmlPrivate::AOTCompiledFunction aotBuiltFunctions[];
    const QQmlPrivate::CachedQmlUnit unit = {
        reinterpret_cast<const QV4::CompiledData::Unit*>(&qmlData), &aotBuiltFunctions[0], nullptr
    };
}
namespace _qt_qml_org_deepin_launchpad_GridViewContainer_qml { 
    extern const unsigned char qmlData[];
    extern const QQmlPrivate::AOTCompiledFunction aotBuiltFunctions[];
    const QQmlPrivate::CachedQmlUnit unit = {
        reinterpret_cast<const QV4::CompiledData::Unit*>(&qmlData), &aotBuiltFunctions[0], nullptr
    };
}
namespace _qt_qml_org_deepin_launchpad_DrawerFolder_qml { 
    extern const unsigned char qmlData[];
    extern const QQmlPrivate::AOTCompiledFunction aotBuiltFunctions[];
    const QQmlPrivate::CachedQmlUnit unit = {
        reinterpret_cast<const QV4::CompiledData::Unit*>(&qmlData), &aotBuiltFunctions[0], nullptr
    };
}
namespace _qt_qml_org_deepin_launchpad_IconItemDelegate_qml { 
    extern const unsigned char qmlData[];
    extern const QQmlPrivate::AOTCompiledFunction aotBuiltFunctions[];
    const QQmlPrivate::CachedQmlUnit unit = {
        reinterpret_cast<const QV4::CompiledData::Unit*>(&qmlData), &aotBuiltFunctions[0], nullptr
    };
}
namespace _qt_qml_org_deepin_launchpad_DebugDialog_qml { 
    extern const unsigned char qmlData[];
    extern const QQmlPrivate::AOTCompiledFunction aotBuiltFunctions[];
    const QQmlPrivate::CachedQmlUnit unit = {
        reinterpret_cast<const QV4::CompiledData::Unit*>(&qmlData), &aotBuiltFunctions[0], nullptr
    };
}
namespace _qt_qml_org_deepin_launchpad_DebugBounding_qml { 
    extern const unsigned char qmlData[];
    extern const QQmlPrivate::AOTCompiledFunction aotBuiltFunctions[];
    const QQmlPrivate::CachedQmlUnit unit = {
        reinterpret_cast<const QV4::CompiledData::Unit*>(&qmlData), &aotBuiltFunctions[0], nullptr
    };
}
namespace _qt_qml_org_deepin_launchpad_FolderGridViewPopup_qml { 
    extern const unsigned char qmlData[];
    extern const QQmlPrivate::AOTCompiledFunction aotBuiltFunctions[];
    const QQmlPrivate::CachedQmlUnit unit = {
        reinterpret_cast<const QV4::CompiledData::Unit*>(&qmlData), &aotBuiltFunctions[0], nullptr
    };
}

}
namespace {
struct Registry {
    Registry();
    ~Registry();
    QHash<QString, const QQmlPrivate::CachedQmlUnit*> resourcePathToCachedUnit;
    static const QQmlPrivate::CachedQmlUnit *lookupCachedUnit(const QUrl &url);
};

Q_GLOBAL_STATIC(Registry, unitRegistry)


Registry::Registry() {
    resourcePathToCachedUnit.insert(QStringLiteral("/qt/qml/org/deepin/launchpad/Helper.qml"), &QmlCacheGeneratedCode::_qt_qml_org_deepin_launchpad_Helper_qml::unit);
    resourcePathToCachedUnit.insert(QStringLiteral("/qt/qml/org/deepin/launchpad/Main.qml"), &QmlCacheGeneratedCode::_qt_qml_org_deepin_launchpad_Main_qml::unit);
    resourcePathToCachedUnit.insert(QStringLiteral("/qt/qml/org/deepin/launchpad/FullscreenFrame.qml"), &QmlCacheGeneratedCode::_qt_qml_org_deepin_launchpad_FullscreenFrame_qml::unit);
    resourcePathToCachedUnit.insert(QStringLiteral("/qt/qml/org/deepin/launchpad/AppItemMenu.qml"), &QmlCacheGeneratedCode::_qt_qml_org_deepin_launchpad_AppItemMenu_qml::unit);
    resourcePathToCachedUnit.insert(QStringLiteral("/qt/qml/org/deepin/launchpad/DummyAppItemMenu.qml"), &QmlCacheGeneratedCode::_qt_qml_org_deepin_launchpad_DummyAppItemMenu_qml::unit);
    resourcePathToCachedUnit.insert(QStringLiteral("/qt/qml/org/deepin/launchpad/GridViewContainer.qml"), &QmlCacheGeneratedCode::_qt_qml_org_deepin_launchpad_GridViewContainer_qml::unit);
    resourcePathToCachedUnit.insert(QStringLiteral("/qt/qml/org/deepin/launchpad/DrawerFolder.qml"), &QmlCacheGeneratedCode::_qt_qml_org_deepin_launchpad_DrawerFolder_qml::unit);
    resourcePathToCachedUnit.insert(QStringLiteral("/qt/qml/org/deepin/launchpad/IconItemDelegate.qml"), &QmlCacheGeneratedCode::_qt_qml_org_deepin_launchpad_IconItemDelegate_qml::unit);
    resourcePathToCachedUnit.insert(QStringLiteral("/qt/qml/org/deepin/launchpad/DebugDialog.qml"), &QmlCacheGeneratedCode::_qt_qml_org_deepin_launchpad_DebugDialog_qml::unit);
    resourcePathToCachedUnit.insert(QStringLiteral("/qt/qml/org/deepin/launchpad/DebugBounding.qml"), &QmlCacheGeneratedCode::_qt_qml_org_deepin_launchpad_DebugBounding_qml::unit);
    resourcePathToCachedUnit.insert(QStringLiteral("/qt/qml/org/deepin/launchpad/FolderGridViewPopup.qml"), &QmlCacheGeneratedCode::_qt_qml_org_deepin_launchpad_FolderGridViewPopup_qml::unit);
    QQmlPrivate::RegisterQmlUnitCacheHook registration;
    registration.structVersion = 0;
    registration.lookupCachedQmlUnit = &lookupCachedUnit;
    QQmlPrivate::qmlregister(QQmlPrivate::QmlUnitCacheHookRegistration, &registration);
}

Registry::~Registry() {
    QQmlPrivate::qmlunregister(QQmlPrivate::QmlUnitCacheHookRegistration, quintptr(&lookupCachedUnit));
}

const QQmlPrivate::CachedQmlUnit *Registry::lookupCachedUnit(const QUrl &url) {
    if (url.scheme() != QLatin1String("qrc"))
        return nullptr;
    QString resourcePath = QDir::cleanPath(url.path());
    if (resourcePath.isEmpty())
        return nullptr;
    if (!resourcePath.startsWith(QLatin1Char('/')))
        resourcePath.prepend(QLatin1Char('/'));
    return unitRegistry()->resourcePathToCachedUnit.value(resourcePath, nullptr);
}
}
int QT_MANGLE_NAMESPACE(qInitResources_qmlcache_launchpadcommon)() {
    ::unitRegistry();
    return 1;
}
Q_CONSTRUCTOR_FUNCTION(QT_MANGLE_NAMESPACE(qInitResources_qmlcache_launchpadcommon))
int QT_MANGLE_NAMESPACE(qCleanupResources_qmlcache_launchpadcommon)() {
    return 1;
}
